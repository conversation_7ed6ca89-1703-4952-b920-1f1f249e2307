"""
CISM Core: Score Distillation Sampling损失实现
阶段2.3: 将扩散模型的语义引导转换为3D高斯优化损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Union
import logging
import math

class SDSLoss:
    """
    Score Distillation Sampling损失计算器
    
    功能：
    1. 实现SDS损失公式
    2. 时间步权重函数
    3. 梯度计算和反向传播
    4. 多概念SDS损失融合
    """
    
    def __init__(
        self,
        diffusion_engine,
        concept_guidance,
        min_step: int = 20,
        max_step: int = 980,
        device: str = "cuda"
    ):
        """
        初始化SDS损失计算器
        
        Args:
            diffusion_engine: 扩散模型引擎
            concept_guidance: 概念引导系统
            min_step: 最小时间步
            max_step: 最大时间步
            device: 计算设备
        """
        self.diffusion_engine = diffusion_engine
        self.concept_guidance = concept_guidance
        self.min_step = min_step
        self.max_step = max_step
        self.device = device
        
        # SDS参数
        self.guidance_scale = 7.5
        self.grad_clip_val = None
        
        logging.info("SDSLoss initialized")
    
    def time_weight(self, t: torch.Tensor, weight_type: str = "dreamfusion") -> torch.Tensor:
        """
        计算时间步权重函数
        
        Args:
            t: 时间步 [batch_size]
            weight_type: 权重函数类型
            
        Returns:
            weights: 时间步权重 [batch_size]
        """
        if weight_type == "dreamfusion":
            # DreamFusion权重函数: w(t) = (1 - t/T)^2
            normalized_t = t.float() / 1000.0
            weights = (1.0 - normalized_t) ** 2
        elif weight_type == "uniform":
            # 均匀权重
            weights = torch.ones_like(t, dtype=torch.float32)
        elif weight_type == "linear":
            # 线性递减权重
            normalized_t = t.float() / 1000.0
            weights = 1.0 - normalized_t
        else:
            raise ValueError(f"Unknown weight type: {weight_type}")
        
        return weights.to(self.device)
    
    def sample_timesteps(self, batch_size: int) -> torch.Tensor:
        """
        采样随机时间步
        
        Args:
            batch_size: 批次大小
            
        Returns:
            timesteps: 随机时间步 [batch_size]
        """
        timesteps = torch.randint(
            self.min_step, 
            self.max_step + 1, 
            (batch_size,), 
            device=self.device,
            dtype=torch.long
        )
        return timesteps
    
    def encode_images_to_latents(self, images: torch.Tensor) -> torch.Tensor:
        """
        将渲染图像编码到潜在空间
        
        Args:
            images: 渲染图像 [batch_size, 3, H, W], 范围[0, 1]
            
        Returns:
            latents: 潜在表示 [batch_size, 4, H//8, W//8]
        """
        return self.diffusion_engine.encode_image_to_latent(images)
    
    def compute_sds_loss_single_concept(
        self,
        rendered_images: torch.Tensor,
        concept_id: int,
        guidance_scale: Optional[float] = None,
        weight_type: str = "dreamfusion"
    ) -> Tuple[torch.Tensor, Dict]:
        """
        计算单概念SDS损失
        
        Args:
            rendered_images: 渲染图像 [batch_size, 3, H, W]
            concept_id: 概念ID
            guidance_scale: 引导强度，None则使用默认值
            weight_type: 时间步权重类型
            
        Returns:
            loss: SDS损失
            info: 损失计算信息
        """
        if guidance_scale is None:
            guidance_scale = self.guidance_scale
        
        batch_size = rendered_images.shape[0]
        
        # 1. 编码到潜在空间
        latents = self.encode_images_to_latents(rendered_images)
        
        # 2. 采样时间步
        timesteps = self.sample_timesteps(batch_size)
        
        # 3. 添加噪声
        noise = torch.randn_like(latents)
        noisy_latents = self.diffusion_engine.add_noise(latents, noise, timesteps)
        
        # 4. 计算引导差异
        delta_epsilon = self.concept_guidance.compute_guidance_delta(
            noisy_latents, timesteps, concept_id, guidance_scale
        )
        
        # 5. 计算时间步权重
        w_t = self.time_weight(timesteps, weight_type)
        
        # 6. 计算SDS损失
        # SDS公式: L = w(t) * (delta_epsilon * ∂latent/∂θ)
        # 这里使用梯度技巧: loss = (delta_epsilon * (latent - latent.detach())).sum()
        target = (latents - latents.detach())
        loss_per_sample = torch.sum(delta_epsilon * target, dim=[1, 2, 3])
        
        # 应用时间步权重
        weighted_loss = w_t * loss_per_sample
        loss = weighted_loss.mean()
        
        # 收集信息
        info = {
            'timesteps': timesteps.cpu(),
            'weights': w_t.cpu(),
            'loss_per_sample': loss_per_sample.detach().cpu(),
            'concept_id': concept_id,
            'guidance_scale': guidance_scale
        }
        
        return loss, info
    
    def compute_sds_loss_multi_concept(
        self,
        rendered_images: torch.Tensor,
        concept_ids: list,
        concept_weights: Optional[torch.Tensor] = None,
        guidance_scale: Optional[float] = None,
        weight_type: str = "dreamfusion"
    ) -> Tuple[torch.Tensor, Dict]:
        """
        计算多概念SDS损失
        
        Args:
            rendered_images: 渲染图像 [batch_size, 3, H, W]
            concept_ids: 概念ID列表
            concept_weights: 概念权重 [num_concepts]
            guidance_scale: 引导强度
            weight_type: 时间步权重类型
            
        Returns:
            loss: 总SDS损失
            info: 损失计算信息
        """
        if guidance_scale is None:
            guidance_scale = self.guidance_scale
        
        if concept_weights is None:
            concept_weights = torch.ones(len(concept_ids), device=self.device) / len(concept_ids)
        
        batch_size = rendered_images.shape[0]
        
        # 1. 编码到潜在空间
        latents = self.encode_images_to_latents(rendered_images)
        
        # 2. 采样时间步
        timesteps = self.sample_timesteps(batch_size)
        
        # 3. 添加噪声
        noise = torch.randn_like(latents)
        noisy_latents = self.diffusion_engine.add_noise(latents, noise, timesteps)
        
        # 4. 计算多概念引导差异
        delta_epsilons = self.concept_guidance.compute_multi_concept_guidance(
            noisy_latents, timesteps, concept_ids, concept_weights, guidance_scale
        )
        
        # 5. 混合概念引导
        blended_delta = self.concept_guidance.blend_concept_guidance(
            delta_epsilons, concept_weights
        )
        
        # 6. 计算时间步权重
        w_t = self.time_weight(timesteps, weight_type)
        
        # 7. 计算SDS损失
        target = (latents - latents.detach())
        loss_per_sample = torch.sum(blended_delta * target, dim=[1, 2, 3])
        
        # 应用时间步权重
        weighted_loss = w_t * loss_per_sample
        loss = weighted_loss.mean()
        
        # 收集信息
        info = {
            'timesteps': timesteps.cpu(),
            'weights': w_t.cpu(),
            'loss_per_sample': loss_per_sample.detach().cpu(),
            'concept_ids': concept_ids,
            'concept_weights': concept_weights.cpu(),
            'guidance_scale': guidance_scale,
            'individual_losses': {
                cid: torch.sum(delta_eps * target, dim=[1, 2, 3]).detach().cpu()
                for cid, delta_eps in delta_epsilons.items()
            }
        }
        
        return loss, info
    
    def compute_concept_aware_sds_loss(
        self,
        rendered_images: torch.Tensor,
        concept_masks: torch.Tensor,
        concept_ids: list,
        guidance_scale: Optional[float] = None,
        weight_type: str = "dreamfusion"
    ) -> Tuple[torch.Tensor, Dict]:
        """
        计算概念感知的SDS损失（基于概念掩码）
        
        Args:
            rendered_images: 渲染图像 [batch_size, 3, H, W]
            concept_masks: 概念掩码 [batch_size, num_concepts, H, W]
            concept_ids: 概念ID列表
            guidance_scale: 引导强度
            weight_type: 时间步权重类型
            
        Returns:
            loss: 概念感知SDS损失
            info: 损失计算信息
        """
        if guidance_scale is None:
            guidance_scale = self.guidance_scale
        
        batch_size = rendered_images.shape[0]
        
        # 1. 编码到潜在空间
        latents = self.encode_images_to_latents(rendered_images)
        
        # 2. 下采样概念掩码到潜在空间分辨率
        latent_h, latent_w = latents.shape[2], latents.shape[3]
        concept_masks_latent = F.interpolate(
            concept_masks, 
            size=(latent_h, latent_w), 
            mode='bilinear', 
            align_corners=False
        )
        
        # 3. 采样时间步
        timesteps = self.sample_timesteps(batch_size)
        
        # 4. 添加噪声
        noise = torch.randn_like(latents)
        noisy_latents = self.diffusion_engine.add_noise(latents, noise, timesteps)
        
        # 5. 计算每个概念的引导差异
        delta_epsilons = self.concept_guidance.compute_multi_concept_guidance(
            noisy_latents, timesteps, concept_ids, None, guidance_scale
        )
        
        # 6. 应用空间掩码
        masked_deltas = []
        for i, concept_id in enumerate(concept_ids):
            delta_eps = delta_epsilons[concept_id]
            mask = concept_masks_latent[:, i:i+1, :, :]  # [batch, 1, h, w]
            masked_delta = delta_eps * mask
            masked_deltas.append(masked_delta)
        
        # 7. 合并掩码引导
        total_delta = torch.sum(torch.stack(masked_deltas, dim=0), dim=0)
        
        # 8. 计算时间步权重
        w_t = self.time_weight(timesteps, weight_type)
        
        # 9. 计算SDS损失
        target = (latents - latents.detach())
        loss_per_sample = torch.sum(total_delta * target, dim=[1, 2, 3])
        
        # 应用时间步权重
        weighted_loss = w_t * loss_per_sample
        loss = weighted_loss.mean()
        
        # 收集信息
        info = {
            'timesteps': timesteps.cpu(),
            'weights': w_t.cpu(),
            'loss_per_sample': loss_per_sample.detach().cpu(),
            'concept_ids': concept_ids,
            'guidance_scale': guidance_scale,
            'mask_coverage': [mask.sum().item() for mask in concept_masks_latent[0]]
        }
        
        return loss, info
    
    def set_guidance_scale(self, guidance_scale: float):
        """设置引导强度"""
        self.guidance_scale = guidance_scale
        logging.info(f"Guidance scale set to {guidance_scale}")
    
    def set_timestep_range(self, min_step: int, max_step: int):
        """设置时间步范围"""
        self.min_step = min_step
        self.max_step = max_step
        logging.info(f"Timestep range set to [{min_step}, {max_step}]")
