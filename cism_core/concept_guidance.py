"""
CISM Core: 概念条件化引导实现
阶段2.2: 基于concept_id实现差异化的文本概念引导
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Union
import yaml
import logging
from pathlib import Path

class ConceptGuidance:
    """
    概念条件化引导系统
    
    功能：
    1. 管理concept_id与文本概念的映射
    2. 实现差异化的概念引导
    3. 支持用户自定义概念
    4. 提供概念嵌入缓存机制
    """
    
    def __init__(
        self,
        diffusion_engine,
        concept_config_path: Optional[str] = None,
        device: str = "cuda"
    ):
        """
        初始化概念引导系统
        
        Args:
            diffusion_engine: 扩散模型引擎
            concept_config_path: 概念配置文件路径
            device: 计算设备
        """
        self.diffusion_engine = diffusion_engine
        self.device = device
        
        # 默认概念配置
        self.default_concept_prompts = {
            0: "natural garden background, lush vegetation, harmonious landscape, photorealistic",
            1: "USER_DEFINED_CONCEPT",  # 用户自定义概念
            2: "smooth natural transition, seamless blending edge, soft boundary"
        }
        
        # 加载概念配置
        self.concept_prompts = self._load_concept_config(concept_config_path)
        
        # 概念嵌入缓存
        self.concept_embeddings_cache = {}
        self.uncond_embedding_cache = None
        
        # 预计算常用嵌入
        self._precompute_embeddings()
        
        logging.info("ConceptGuidance initialized")
    
    def _load_concept_config(self, config_path: Optional[str]) -> Dict[int, str]:
        """
        加载概念配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            concept_prompts: 概念ID到文本提示的映射
        """
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    concept_prompts = config.get('concept_prompts', self.default_concept_prompts)
                    logging.info(f"Loaded concept config from {config_path}")
                    return concept_prompts
            except Exception as e:
                logging.warning(f"Failed to load concept config: {e}, using defaults")
        
        return self.default_concept_prompts.copy()
    
    def save_concept_config(self, config_path: str):
        """
        保存当前概念配置到文件
        
        Args:
            config_path: 保存路径
        """
        config = {
            'concept_prompts': self.concept_prompts,
            'description': 'Concept ID to text prompt mapping for CISM guidance'
        }
        
        Path(config_path).parent.mkdir(parents=True, exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logging.info(f"Concept config saved to {config_path}")
    
    def set_user_concept(self, concept_id: int, prompt: str):
        """
        设置用户自定义概念
        
        Args:
            concept_id: 概念ID
            prompt: 文本提示
        """
        self.concept_prompts[concept_id] = prompt
        
        # 清除缓存，强制重新计算
        if concept_id in self.concept_embeddings_cache:
            del self.concept_embeddings_cache[concept_id]
        
        logging.info(f"Set concept {concept_id}: {prompt}")
    
    def get_concept_prompt(self, concept_id: int) -> str:
        """
        获取概念对应的文本提示
        
        Args:
            concept_id: 概念ID
            
        Returns:
            prompt: 文本提示
        """
        return self.concept_prompts.get(concept_id, f"concept_{concept_id}")
    
    def _precompute_embeddings(self):
        """预计算常用概念嵌入"""
        logging.info("Precomputing concept embeddings...")
        
        # 计算所有概念的嵌入
        for concept_id, prompt in self.concept_prompts.items():
            if prompt != "USER_DEFINED_CONCEPT":  # 跳过占位符
                self.concept_embeddings_cache[concept_id] = self.diffusion_engine.encode_text(prompt)
        
        # 计算无条件嵌入
        self.uncond_embedding_cache = self.diffusion_engine.encode_text("")
        
        logging.info(f"Precomputed embeddings for {len(self.concept_embeddings_cache)} concepts")
    
    def get_concept_embedding(self, concept_id: int) -> torch.Tensor:
        """
        获取概念嵌入，支持缓存
        
        Args:
            concept_id: 概念ID
            
        Returns:
            embedding: 概念嵌入 [1, 77, 768]
        """
        if concept_id not in self.concept_embeddings_cache:
            prompt = self.get_concept_prompt(concept_id)
            if prompt == "USER_DEFINED_CONCEPT":
                raise ValueError(f"Concept {concept_id} is not defined. Use set_user_concept() first.")
            
            self.concept_embeddings_cache[concept_id] = self.diffusion_engine.encode_text(prompt)
        
        return self.concept_embeddings_cache[concept_id]
    
    def get_uncond_embedding(self) -> torch.Tensor:
        """
        获取无条件嵌入
        
        Returns:
            embedding: 无条件嵌入 [1, 77, 768]
        """
        if self.uncond_embedding_cache is None:
            self.uncond_embedding_cache = self.diffusion_engine.encode_text("")
        
        return self.uncond_embedding_cache
    
    def prepare_concept_embeddings(self, concept_ids: List[int]) -> Dict[int, torch.Tensor]:
        """
        批量准备概念嵌入
        
        Args:
            concept_ids: 概念ID列表
            
        Returns:
            embeddings: 概念ID到嵌入的映射
        """
        embeddings = {}
        for concept_id in concept_ids:
            embeddings[concept_id] = self.get_concept_embedding(concept_id)
        
        return embeddings
    
    def compute_guidance_delta(
        self,
        latents: torch.Tensor,
        timesteps: torch.Tensor,
        concept_id: int,
        guidance_scale: float = 7.5
    ) -> torch.Tensor:
        """
        计算单个概念的引导差异（标准实现，无RCA复杂性）

        Args:
            latents: 噪声潜在表示 [batch_size, 4, H//8, W//8]
            timesteps: 时间步 [batch_size]
            concept_id: 概念ID
            guidance_scale: 引导强度

        Returns:
            delta_epsilon: 引导差异 [batch_size, 4, H//8, W//8]
        """
        batch_size = latents.shape[0]

        # 获取概念嵌入
        cond_embedding = self.get_concept_embedding(concept_id)
        uncond_embedding = self.get_uncond_embedding()

        # 确保嵌入的batch维度匹配
        if cond_embedding.shape[0] != batch_size:
            cond_embedding = cond_embedding.repeat(batch_size, 1, 1)
        if uncond_embedding.shape[0] != batch_size:
            uncond_embedding = uncond_embedding.repeat(batch_size, 1, 1)

        # 组合条件和无条件嵌入 [2*batch_size, 77, 768]
        text_embeddings = torch.cat([uncond_embedding, cond_embedding], dim=0)

        # 使用修复后的predict_noise方法，返回分离的预测结果
        noise_pred_uncond, noise_pred_cond = self.diffusion_engine.predict_noise(
            latents, timesteps, text_embeddings, guidance_scale, return_separate=True
        )

        # 计算引导差异（这是CFG的核心）
        delta_epsilon = noise_pred_cond - noise_pred_uncond

        return delta_epsilon
    
    def compute_multi_concept_guidance(
        self,
        latents: torch.Tensor,
        timesteps: torch.Tensor,
        concept_ids: List[int],
        concept_weights: Optional[torch.Tensor] = None,
        guidance_scale: float = 7.5
    ) -> Dict[int, torch.Tensor]:
        """
        计算多概念引导差异
        
        Args:
            latents: 噪声潜在表示
            timesteps: 时间步
            concept_ids: 概念ID列表
            concept_weights: 概念权重 [num_concepts]
            guidance_scale: 引导强度
            
        Returns:
            delta_epsilons: 概念ID到引导差异的映射
        """
        delta_epsilons = {}
        
        for concept_id in concept_ids:
            delta_epsilon = self.compute_guidance_delta(
                latents, timesteps, concept_id, guidance_scale
            )
            delta_epsilons[concept_id] = delta_epsilon
        
        return delta_epsilons
    
    def blend_concept_guidance(
        self,
        delta_epsilons: Dict[int, torch.Tensor],
        concept_weights: torch.Tensor
    ) -> torch.Tensor:
        """
        混合多概念引导
        
        Args:
            delta_epsilons: 概念引导差异映射
            concept_weights: 概念权重 [num_concepts]
            
        Returns:
            blended_delta: 混合后的引导差异
        """
        blended_delta = torch.zeros_like(list(delta_epsilons.values())[0])
        
        for i, (concept_id, delta_epsilon) in enumerate(delta_epsilons.items()):
            weight = concept_weights[i] if i < len(concept_weights) else 1.0
            blended_delta += weight * delta_epsilon
        
        return blended_delta
    
    def get_concept_statistics(self) -> Dict:
        """
        获取概念统计信息
        
        Returns:
            stats: 统计信息
        """
        stats = {
            'total_concepts': len(self.concept_prompts),
            'cached_embeddings': len(self.concept_embeddings_cache),
            'concept_prompts': self.concept_prompts.copy(),
            'user_defined_concepts': [
                cid for cid, prompt in self.concept_prompts.items() 
                if prompt != "USER_DEFINED_CONCEPT" and cid != 0 and cid != 2
            ]
        }
        return stats
    
    def clear_cache(self):
        """清除嵌入缓存"""
        self.concept_embeddings_cache.clear()
        self.uncond_embedding_cache = None
        logging.info("Concept embedding cache cleared")

    def cleanup(self):
        """清理资源"""
        self.clear_cache()
        logging.info("ConceptGuidance cleaned up")
