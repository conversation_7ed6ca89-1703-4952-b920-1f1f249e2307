"""
CISM Core: Stable Diffusion模型集成
阶段2.1: 搭建扩散模型引擎，为语义引导提供AI能力
"""

import torch
import torch.nn as nn
from typing import Dict, Optional, Union
import logging

try:
    from diffusers import StableDiffusionPipeline, UNet2DConditionModel, DDIMScheduler
    from diffusers.models import AutoencoderKL
    from transformers import CLIPTextModel, CLIPTokenizer
    DIFFUSERS_AVAILABLE = True
except ImportError:
    DIFFUSERS_AVAILABLE = False
    logging.warning("Diffusers not available. Please install: pip install diffusers transformers accelerate")

class DiffusionEngine:
    """
    Stable Diffusion引擎，为CISM提供语义引导能力
    
    功能：
    1. 加载预训练的Stable Diffusion组件
    2. 文本编码和条件化
    3. 噪声预测和引导计算
    4. 内存优化配置
    """
    
    def __init__(
        self,
        model_id: str = "runwayml/stable-diffusion-v1-5",
        device: str = "cuda",
        half_precision: bool = True,
        enable_attention_slicing: bool = True,
        enable_memory_efficient_attention: bool = True
    ):
        """
        初始化扩散模型引擎
        
        Args:
            model_id: Hugging Face模型ID
            device: 计算设备
            half_precision: 是否使用半精度
            enable_attention_slicing: 是否启用注意力切片
            enable_memory_efficient_attention: 是否启用内存高效注意力
        """
        if not DIFFUSERS_AVAILABLE:
            raise ImportError("Diffusers package is required. Install with: pip install diffusers transformers accelerate")
        
        self.device = device
        self.half_precision = half_precision
        self.model_id = model_id
        
        # 初始化组件
        self._load_components()
        self._setup_memory_optimization(enable_attention_slicing, enable_memory_efficient_attention)
        
        logging.info(f"DiffusionEngine initialized with model: {model_id}")
    
    def _load_components(self):
        """加载Stable Diffusion的核心组件"""
        logging.info("Loading Stable Diffusion components...")
        
        # 加载UNet
        self.unet = UNet2DConditionModel.from_pretrained(
            self.model_id, 
            subfolder="unet",
            torch_dtype=torch.float16 if self.half_precision else torch.float32
        ).to(self.device)
        
        # 加载VAE
        self.vae = AutoencoderKL.from_pretrained(
            self.model_id,
            subfolder="vae", 
            torch_dtype=torch.float16 if self.half_precision else torch.float32
        ).to(self.device)
        
        # 加载文本编码器
        self.text_encoder = CLIPTextModel.from_pretrained(
            self.model_id,
            subfolder="text_encoder",
            torch_dtype=torch.float16 if self.half_precision else torch.float32
        ).to(self.device)
        
        # 加载分词器
        self.tokenizer = CLIPTokenizer.from_pretrained(
            self.model_id,
            subfolder="tokenizer"
        )
        
        # 加载调度器
        self.scheduler = DDIMScheduler.from_pretrained(
            self.model_id,
            subfolder="scheduler"
        )
        
        # 设置为评估模式
        self.unet.eval()
        self.vae.eval()
        self.text_encoder.eval()
        
        logging.info("All components loaded successfully")
    
    def _setup_memory_optimization(self, enable_attention_slicing: bool, enable_memory_efficient_attention: bool):
        """设置内存优化"""
        if enable_attention_slicing:
            print("    🔧 尝试启用 attention slicing...")
            try:
                # 直接尝试调用，捕获所有可能的异常
                self.unet.enable_attention_slicing()
                print("     ✅ Attention slicing 启用成功")
                logging.info("Attention slicing enabled")
            except AttributeError as e:
                print(f"     ⚠️  UNet 模型不支持 enable_attention_slicing()。跳过。错误: {e}")
                logging.warning(f"Attention slicing not available: {e}")
            except Exception as e:
                print(f"     ⚠️  启用 attention slicing 时出错: {e}。跳过。")
                logging.warning(f"Error enabling attention slicing: {e}")

        if enable_memory_efficient_attention:
            print("    🔧 尝试启用 memory efficient attention...")
            try:
                # 检查是否存在该方法
                if hasattr(self.unet, 'enable_xformers_memory_efficient_attention'):
                    self.unet.enable_xformers_memory_efficient_attention()
                    print("     ✅ XFormers memory efficient attention 启用成功")
                    logging.info("XFormers memory efficient attention enabled")
                elif hasattr(self.unet, 'enable_memory_efficient_attention'):
                    self.unet.enable_memory_efficient_attention()
                    print("     ✅ Memory efficient attention 启用成功")
                    logging.info("Memory efficient attention enabled")
                else:
                    print("     ⚠️  UNet 模型不支持 memory efficient attention。跳过。")
                    logging.warning("Memory efficient attention not supported")
            except Exception as e:
                print(f"     ⚠️  启用 memory efficient attention 时出错: {e}。跳过。")
                logging.warning(f"Error enabling memory efficient attention: {e}")
    
    def encode_text(self, prompts: Union[str, list]) -> torch.Tensor:
        """
        编码文本提示为嵌入向量
        
        Args:
            prompts: 文本提示，可以是单个字符串或字符串列表
            
        Returns:
            text_embeddings: 文本嵌入 [batch_size, 77, 768]
        """
        if isinstance(prompts, str):
            prompts = [prompts]
        
        # 分词
        text_inputs = self.tokenizer(
            prompts,
            padding="max_length",
            max_length=self.tokenizer.model_max_length,
            truncation=True,
            return_tensors="pt"
        )
        
        # 编码
        with torch.no_grad():
            text_embeddings = self.text_encoder(text_inputs.input_ids.to(self.device))[0]
        
        return text_embeddings
    
    def encode_image_to_latent(self, images: torch.Tensor) -> torch.Tensor:
        """
        将图像编码到潜在空间
        
        Args:
            images: 输入图像 [batch_size, 3, H, W], 范围[0, 1]
            
        Returns:
            latents: 潜在表示 [batch_size, 4, H//8, W//8]
        """
        # 转换到[-1, 1]范围
        images = 2.0 * images - 1.0
        
        with torch.no_grad():
            latents = self.vae.encode(images).latent_dist.sample()
            latents = latents * self.vae.config.scaling_factor
        
        return latents
    
    def decode_latent_to_image(self, latents: torch.Tensor) -> torch.Tensor:
        """
        将潜在表示解码为图像
        
        Args:
            latents: 潜在表示 [batch_size, 4, H//8, W//8]
            
        Returns:
            images: 解码图像 [batch_size, 3, H, W], 范围[0, 1]
        """
        latents = latents / self.vae.config.scaling_factor
        
        with torch.no_grad():
            images = self.vae.decode(latents).sample
        
        # 转换到[0, 1]范围
        images = (images + 1.0) / 2.0
        images = torch.clamp(images, 0.0, 1.0)
        
        return images
    
    def predict_noise(
        self, 
        latents: torch.Tensor, 
        timesteps: torch.Tensor, 
        text_embeddings: torch.Tensor,
        guidance_scale: float = 7.5
    ) -> torch.Tensor:
        """
        预测噪声，支持分类器自由引导
        
        Args:
            latents: 噪声潜在表示
            timesteps: 时间步
            text_embeddings: 文本嵌入
            guidance_scale: 引导强度
            
        Returns:
            noise_pred: 预测的噪声
        """
        # 扩展潜在表示用于CFG
        latent_model_input = torch.cat([latents] * 2)
        
        # 预测噪声
        with torch.no_grad():
            noise_pred = self.unet(
                latent_model_input,
                timesteps,
                encoder_hidden_states=text_embeddings
            ).sample
        
        # 分离条件和无条件预测
        noise_pred_uncond, noise_pred_text = noise_pred.chunk(2)
        
        # 计算CFG引导
        noise_pred = noise_pred_uncond + guidance_scale * (noise_pred_text - noise_pred_uncond)
        
        return noise_pred
    
    def add_noise(self, latents: torch.Tensor, noise: torch.Tensor, timesteps: torch.Tensor) -> torch.Tensor:
        """
        向潜在表示添加噪声
        
        Args:
            latents: 原始潜在表示
            noise: 噪声
            timesteps: 时间步
            
        Returns:
            noisy_latents: 添加噪声后的潜在表示
        """
        return self.scheduler.add_noise(latents, noise, timesteps)
    
    def get_timesteps(self, num_inference_steps: int = 50) -> torch.Tensor:
        """
        获取推理时间步
        
        Args:
            num_inference_steps: 推理步数
            
        Returns:
            timesteps: 时间步序列
        """
        self.scheduler.set_timesteps(num_inference_steps)
        return self.scheduler.timesteps
    
    def cleanup(self):
        """清理GPU内存"""
        if hasattr(self, 'unet'):
            del self.unet
        if hasattr(self, 'vae'):
            del self.vae
        if hasattr(self, 'text_encoder'):
            del self.text_encoder
        
        torch.cuda.empty_cache()
        logging.info("DiffusionEngine cleaned up")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
