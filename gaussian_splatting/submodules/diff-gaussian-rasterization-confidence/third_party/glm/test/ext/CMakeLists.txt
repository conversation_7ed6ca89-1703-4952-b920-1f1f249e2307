glmCreateTestGTC(ext_matrix_relational)
glmCreateTestGTC(ext_matrix_transform)
glmCreateTestGTC(ext_matrix_common)
glmCreateTestGTC(ext_matrix_integer)
glmCreateTestGTC(ext_matrix_int2x2_sized)
glmCreateTestGTC(ext_matrix_int2x3_sized)
glmCreateTestGTC(ext_matrix_int2x4_sized)
glmCreateTestGTC(ext_matrix_int3x2_sized)
glmCreateTestGTC(ext_matrix_int3x3_sized)
glmCreateTestGTC(ext_matrix_int3x4_sized)
glmCreateTestGTC(ext_matrix_int4x2_sized)
glmCreateTestGTC(ext_matrix_int4x3_sized)
glmCreateTestGTC(ext_matrix_int4x4_sized)
glmCreateTestGTC(ext_matrix_uint2x2_sized)
glmCreateTestGTC(ext_matrix_uint2x3_sized)
glmCreateTestGTC(ext_matrix_uint2x4_sized)
glmCreateTestGTC(ext_matrix_uint3x2_sized)
glmCreateTestGTC(ext_matrix_uint3x3_sized)
glmCreateTestGTC(ext_matrix_uint3x4_sized)
glmCreateTestGTC(ext_matrix_uint4x2_sized)
glmCreateTestGTC(ext_matrix_uint4x3_sized)
glmCreateTestGTC(ext_matrix_uint4x4_sized)
glmCreateTestGTC(ext_quaternion_common)
glmCreateTestGTC(ext_quaternion_exponential)
glmCreateTestGTC(ext_quaternion_geometric)
glmCreateTestGTC(ext_quaternion_relational)
glmCreateTestGTC(ext_quaternion_transform)
glmCreateTestGTC(ext_quaternion_trigonometric)
glmCreateTestGTC(ext_quaternion_type)
glmCreateTestGTC(ext_scalar_common)
glmCreateTestGTC(ext_scalar_constants)
glmCreateTestGTC(ext_scalar_int_sized)
glmCreateTestGTC(ext_scalar_uint_sized)
glmCreateTestGTC(ext_scalar_integer)
glmCreateTestGTC(ext_scalar_ulp)
glmCreateTestGTC(ext_scalar_reciprocal)
glmCreateTestGTC(ext_scalar_relational)
glmCreateTestGTC(ext_vec1)
glmCreateTestGTC(ext_vector_bool1)
glmCreateTestGTC(ext_vector_common)
glmCreateTestGTC(ext_vector_iec559)
glmCreateTestGTC(ext_vector_int1_sized)
glmCreateTestGTC(ext_vector_int2_sized)
glmCreateTestGTC(ext_vector_int3_sized)
glmCreateTestGTC(ext_vector_int4_sized)
glmCreateTestGTC(ext_vector_integer)
glmCreateTestGTC(ext_vector_integer_sized)
glmCreateTestGTC(ext_vector_uint1_sized)
glmCreateTestGTC(ext_vector_uint2_sized)
glmCreateTestGTC(ext_vector_uint3_sized)
glmCreateTestGTC(ext_vector_uint4_sized)
glmCreateTestGTC(ext_vector_reciprocal)
glmCreateTestGTC(ext_vector_relational)
glmCreateTestGTC(ext_vector_ulp)

