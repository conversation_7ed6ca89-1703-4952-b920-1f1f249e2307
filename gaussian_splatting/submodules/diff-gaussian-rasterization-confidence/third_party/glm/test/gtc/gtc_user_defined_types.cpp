///////////////////////////////////////////////////////////////////////////////////////////////////
// OpenGL Mathematics Copyright (c) 2005 - 2014 G-Truc Creation (www.g-truc.net)
///////////////////////////////////////////////////////////////////////////////////////////////////
// Created : 2010-09-16
// Updated : 2011-05-27
// Licence : This source is under MIT licence
// File    : test/gtc/type_ptr.cpp
///////////////////////////////////////////////////////////////////////////////////////////////////

#define GLM_FORCE_RADIANS
#include <glm/gtc/user_defined_type.hpp>

int test_make_pointer_vec()
{
	int Error = 0;

	glm::func();
	//func();

	return Error;
}

int main()
{
	int Error = 0;

	Error += test_make_pointer_vec();

	return Error;
}
