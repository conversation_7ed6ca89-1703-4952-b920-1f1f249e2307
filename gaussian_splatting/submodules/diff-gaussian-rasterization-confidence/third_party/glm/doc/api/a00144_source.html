<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: scalar_common.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">scalar_common.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00144.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_scalar_common extension included&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;{</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga0d24a9acb8178df77e4aff90cbb2010d">min</a>(T a, T b, T c);</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga0d24a9acb8178df77e4aff90cbb2010d">min</a>(T a, T b, T c, T d);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">max</a>(T a, T b, T c);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">max</a>(T a, T b, T c, T d);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">fmin</a>(T a, T b);</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">fmin</a>(T a, T b, T c);</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">fmin</a>(T a, T b, T c, T d);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">fmax</a>(T a, T b);</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">fmax</a>(T a, T b, T C);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">fmax</a>(T a, T b, T C, T D);</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#include &quot;scalar_common.inl&quot;</span></div>
<div class="ttc" id="a00258_html_ga0d24a9acb8178df77e4aff90cbb2010d"><div class="ttname"><a href="a00258.html#ga0d24a9acb8178df77e4aff90cbb2010d">glm::min</a></div><div class="ttdeci">GLM_FUNC_DECL T min(T a, T b, T c, T d)</div><div class="ttdoc">Returns the minimum component-wise values of 4 inputs. </div></div>
<div class="ttc" id="a00258_html_ga2274b5e75ed84b0b1e50d8d22f1f2f67"><div class="ttname"><a href="a00258.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">glm::max</a></div><div class="ttdeci">GLM_FUNC_DECL T max(T a, T b, T c, T d)</div><div class="ttdoc">Returns the maximum component-wise values of 4 inputs. </div></div>
<div class="ttc" id="a00258_html_ga27e260b1ff4d04c3ad4b864d26cbaf08"><div class="ttname"><a href="a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">glm::fmax</a></div><div class="ttdeci">GLM_FUNC_DECL T fmax(T a, T b, T C, T D)</div><div class="ttdoc">Returns the maximum component-wise values of 4 inputs. </div></div>
<div class="ttc" id="a00258_html_ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><div class="ttname"><a href="a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">glm::fmin</a></div><div class="ttdeci">GLM_FUNC_DECL T fmin(T a, T b, T c, T d)</div><div class="ttdoc">Returns the minimum component-wise values of 4 inputs. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
