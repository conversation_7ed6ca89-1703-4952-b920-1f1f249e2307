<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: rotate_vector.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">rotate_vector.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00142.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../gtx/transform.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../gtc/epsilon.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../ext/vector_relational.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_rotate_vector is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_rotate_vector extension included&quot;)</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;{</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00356.html#ga8b11b18ce824174ea1a5a69ea14e2cee">slerp</a>(</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                T <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, Q&gt; <a class="code" href="a00356.html#ga1005f1267ed9c57faa3f24cf6873b961">rotate</a>(</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00356.html#ga1005f1267ed9c57faa3f24cf6873b961">rotate</a>(</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>,</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; normal);</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00356.html#ga1005f1267ed9c57faa3f24cf6873b961">rotate</a>(</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>,</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; normal);</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00356.html#ga4333b1ea8ebf1bd52bc3801a7617398a">rotateX</a>(</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00356.html#ga3ce3db0867b7f8efd878ee34f95a623b">rotateY</a>(</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00356.html#ga923b75c6448161053768822d880702e6">rotateZ</a>(</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00356.html#ga4333b1ea8ebf1bd52bc3801a7617398a">rotateX</a>(</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00356.html#ga3ce3db0867b7f8efd878ee34f95a623b">rotateY</a>(</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00356.html#ga923b75c6448161053768822d880702e6">rotateZ</a>(</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                T <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00356.html#ga1a32fceb71962e6160e8af295c91930a">orientation</a>(</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; Normal,</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; Up);</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="preprocessor">#include &quot;rotate_vector.inl&quot;</span></div>
<div class="ttc" id="a00257_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle. </div></div>
<div class="ttc" id="a00356_html_ga923b75c6448161053768822d880702e6"><div class="ttname"><a href="a00356.html#ga923b75c6448161053768822d880702e6">glm::rotateZ</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, Q &gt; rotateZ(vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle)</div><div class="ttdoc">Rotate a four dimensional vector around the Z axis. </div></div>
<div class="ttc" id="a00356_html_ga3ce3db0867b7f8efd878ee34f95a623b"><div class="ttname"><a href="a00356.html#ga3ce3db0867b7f8efd878ee34f95a623b">glm::rotateY</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, Q &gt; rotateY(vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle)</div><div class="ttdoc">Rotate a four dimensional vector around the Y axis. </div></div>
<div class="ttc" id="a00356_html_ga4333b1ea8ebf1bd52bc3801a7617398a"><div class="ttname"><a href="a00356.html#ga4333b1ea8ebf1bd52bc3801a7617398a">glm::rotateX</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, Q &gt; rotateX(vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle)</div><div class="ttdoc">Rotate a four dimensional vector around the X axis. </div></div>
<div class="ttc" id="a00356_html_ga8b11b18ce824174ea1a5a69ea14e2cee"><div class="ttname"><a href="a00356.html#ga8b11b18ce824174ea1a5a69ea14e2cee">glm::slerp</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; slerp(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y, T const &amp;a)</div><div class="ttdoc">Returns Spherical interpolation between two vectors. </div></div>
<div class="ttc" id="a00356_html_ga1a32fceb71962e6160e8af295c91930a"><div class="ttname"><a href="a00356.html#ga1a32fceb71962e6160e8af295c91930a">glm::orientation</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; orientation(vec&lt; 3, T, Q &gt; const &amp;Normal, vec&lt; 3, T, Q &gt; const &amp;Up)</div><div class="ttdoc">Build a rotation matrix from a normal and a up vector. </div></div>
<div class="ttc" id="a00356_html_ga1005f1267ed9c57faa3f24cf6873b961"><div class="ttname"><a href="a00356.html#ga1005f1267ed9c57faa3f24cf6873b961">glm::rotate</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, Q &gt; rotate(vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle, vec&lt; 3, T, Q &gt; const &amp;normal)</div><div class="ttdoc">Rotate a four dimensional vector around an axis. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
