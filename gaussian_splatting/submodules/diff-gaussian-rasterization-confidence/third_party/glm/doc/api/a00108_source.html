<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_transform.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">ext/matrix_transform.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00108.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../geometric.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../trigonometric.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../matrix.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_transform extension included&quot;)</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;{</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00247.html#ga81696f2b8d1db02ea1aff8da8f269314">identity</a>();</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00247.html#ga1a4ecc4ad82652b8fb14dcb087879284">translate</a>(</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00247.html#gaee9e865eaa9776370996da2940873fd4">rotate</a>(</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m, T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga764254f10248b505e936e5309a88c23d">axis</a>);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00247.html#ga05051adbee603fb3c5095d8cf5cc229b">scale</a>(</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00247.html#gacfa12c8889c754846bc20c65d9b5c701">lookAtRH</a>(</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; eye, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00247.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">lookAtLH</a>(</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; eye, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00247.html#gaa64aa951a0e99136bba9008d2b59c78e">lookAt</a>(</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; eye, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#include &quot;matrix_transform.inl&quot;</span></div>
<div class="ttc" id="a00247_html_gab2c09e25b0a16d3a9d89cc85bbae41b0"><div class="ttname"><a href="a00247.html#gab2c09e25b0a16d3a9d89cc85bbae41b0">glm::lookAtLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; lookAtLH(vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a left handed look at view matrix. </div></div>
<div class="ttc" id="a00247_html_gacfa12c8889c754846bc20c65d9b5c701"><div class="ttname"><a href="a00247.html#gacfa12c8889c754846bc20c65d9b5c701">glm::lookAtRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; lookAtRH(vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a right handed look at view matrix. </div></div>
<div class="ttc" id="a00257_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle. </div></div>
<div class="ttc" id="a00247_html_ga1a4ecc4ad82652b8fb14dcb087879284"><div class="ttname"><a href="a00247.html#ga1a4ecc4ad82652b8fb14dcb087879284">glm::translate</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; translate(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a translation 4 * 4 matrix created from a vector of 3 components. </div></div>
<div class="ttc" id="a00247_html_gaee9e865eaa9776370996da2940873fd4"><div class="ttname"><a href="a00247.html#gaee9e865eaa9776370996da2940873fd4">glm::rotate</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; rotate(mat&lt; 4, 4, T, Q &gt; const &amp;m, T angle, vec&lt; 3, T, Q &gt; const &amp;axis)</div><div class="ttdoc">Builds a rotation 4 * 4 matrix created from an axis vector and an angle. </div></div>
<div class="ttc" id="a00247_html_ga81696f2b8d1db02ea1aff8da8f269314"><div class="ttname"><a href="a00247.html#ga81696f2b8d1db02ea1aff8da8f269314">glm::identity</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType identity()</div><div class="ttdoc">Builds an identity matrix. </div></div>
<div class="ttc" id="a00247_html_ga05051adbee603fb3c5095d8cf5cc229b"><div class="ttname"><a href="a00247.html#ga05051adbee603fb3c5095d8cf5cc229b">glm::scale</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; scale(mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Builds a scale 4 * 4 matrix created from 3 scalars. </div></div>
<div class="ttc" id="a00257_html_ga764254f10248b505e936e5309a88c23d"><div class="ttname"><a href="a00257.html#ga764254f10248b505e936e5309a88c23d">glm::axis</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; axis(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the q rotation axis. </div></div>
<div class="ttc" id="a00247_html_gaa64aa951a0e99136bba9008d2b59c78e"><div class="ttname"><a href="a00247.html#gaa64aa951a0e99136bba9008d2b59c78e">glm::lookAt</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; lookAt(vec&lt; 3, T, Q &gt; const &amp;eye, vec&lt; 3, T, Q &gt; const &amp;center, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a look at view matrix based on the default handedness. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
