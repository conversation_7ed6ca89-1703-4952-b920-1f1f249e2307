<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: _noise.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_033f5edb0915b828d2c46ed4804e5503.html">detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">_noise.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;</div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;</div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;{</div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;        GLM_FUNC_QUALIFIER T mod289(T <span class="keyword">const</span>&amp; x)</div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;        {</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;                <span class="keywordflow">return</span> x - <a class="code" href="a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d">floor</a>(x * (static_cast&lt;T&gt;(1.0) / static_cast&lt;T&gt;(289.0))) * <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(289.0);</div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;        }</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;        GLM_FUNC_QUALIFIER T permute(T <span class="keyword">const</span>&amp; x)</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;        {</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;                <span class="keywordflow">return</span> mod289(((x * static_cast&lt;T&gt;(34)) + static_cast&lt;T&gt;(1)) * x);</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;        }</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; permute(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; x)</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;        {</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;                <span class="keywordflow">return</span> mod289(((x * static_cast&lt;T&gt;(34)) + static_cast&lt;T&gt;(1)) * x);</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;        }</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; permute(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x)</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;        {</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;                <span class="keywordflow">return</span> mod289(((x * static_cast&lt;T&gt;(34)) + static_cast&lt;T&gt;(1)) * x);</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        }</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; permute(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; x)</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        {</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                <span class="keywordflow">return</span> mod289(((x * static_cast&lt;T&gt;(34)) + static_cast&lt;T&gt;(1)) * x);</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        }</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_QUALIFIER T taylorInvSqrt(T <span class="keyword">const</span>&amp; r)</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        {</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(1.79284291400159) - <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(0.85373472095314) * r;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        }</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; taylorInvSqrt(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; r)</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        {</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(1.79284291400159) - <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(0.85373472095314) * r;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        }</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; taylorInvSqrt(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; r)</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        {</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(1.79284291400159) - <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(0.85373472095314) * r;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        }</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; taylorInvSqrt(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; r)</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        {</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(1.79284291400159) - <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(0.85373472095314) * r;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        }</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; fade(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; t)</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        {</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                <span class="keywordflow">return</span> (t * t * t) * (t * (t * <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(6) - static_cast&lt;T&gt;(15)) + static_cast&lt;T&gt;(10));</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        }</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; fade(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; t)</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        {</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                <span class="keywordflow">return</span> (t * t * t) * (t * (t * <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(6) - static_cast&lt;T&gt;(15)) + static_cast&lt;T&gt;(10));</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        }</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; fade(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; t)</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        {</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                <span class="keywordflow">return</span> (t * t * t) * (t * (t * <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(6) - static_cast&lt;T&gt;(15)) + static_cast&lt;T&gt;(10));</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        }</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="ttc" id="a00241_html_gaa9d0742639e85b29c7c5de11cfd6840d"><div class="ttname"><a href="a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d">glm::floor</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; floor(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns a value equal to the nearest integer that is less then or equal to x. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
