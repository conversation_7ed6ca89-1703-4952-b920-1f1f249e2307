<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_projection.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_projection.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00105.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../geometric.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../trigonometric.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../matrix.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_projection extension included&quot;)</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;{</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00245.html#ga77d157525063dec83a557186873ee080">projectZO</a>(</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; obj, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; model, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">proj</a>, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00245.html#ga05249751f48d14cb282e4979802b8111">projectNO</a>(</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; obj, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; model, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">proj</a>, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00245.html#gaf36e96033f456659e6705472a06b6e11">project</a>(</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; obj, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; model, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">proj</a>, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00245.html#gade5136413ce530f8e606124d570fba32">unProjectZO</a>(</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; win, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; model, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">proj</a>, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00245.html#gae089ba9fc150ff69c252a20e508857b5">unProjectNO</a>(</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; win, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; model, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">proj</a>, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00245.html#ga36641e5d60f994e01c3d8f56b10263d2">unProject</a>(</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; win, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; model, mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">proj</a>, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00245.html#gaf6b21eadb7ac2ecbbe258a9a233b4c82">pickMatrix</a>(</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; center, vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; delta, vec&lt;4, U, Q&gt; <span class="keyword">const</span>&amp; viewport);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="preprocessor">#include &quot;matrix_projection.inl&quot;</span></div>
<div class="ttc" id="a00245_html_gade5136413ce530f8e606124d570fba32"><div class="ttname"><a href="a00245.html#gade5136413ce530f8e606124d570fba32">glm::unProjectZO</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; unProjectZO(vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates. </div></div>
<div class="ttc" id="a00351_html_ga58384b7170801dd513de46f87c7fb00e"><div class="ttname"><a href="a00351.html#ga58384b7170801dd513de46f87c7fb00e">glm::proj</a></div><div class="ttdeci">GLM_FUNC_DECL genType proj(genType const &amp;x, genType const &amp;Normal)</div><div class="ttdoc">Projects x on Normal. </div></div>
<div class="ttc" id="a00245_html_ga77d157525063dec83a557186873ee080"><div class="ttname"><a href="a00245.html#ga77d157525063dec83a557186873ee080">glm::projectZO</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; projectZO(vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates. </div></div>
<div class="ttc" id="a00245_html_ga05249751f48d14cb282e4979802b8111"><div class="ttname"><a href="a00245.html#ga05249751f48d14cb282e4979802b8111">glm::projectNO</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; projectNO(vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates. </div></div>
<div class="ttc" id="a00245_html_gaf36e96033f456659e6705472a06b6e11"><div class="ttname"><a href="a00245.html#gaf36e96033f456659e6705472a06b6e11">glm::project</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; project(vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates using default near...</div></div>
<div class="ttc" id="a00245_html_gae089ba9fc150ff69c252a20e508857b5"><div class="ttname"><a href="a00245.html#gae089ba9fc150ff69c252a20e508857b5">glm::unProjectNO</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; unProjectNO(vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates. </div></div>
<div class="ttc" id="a00245_html_ga36641e5d60f994e01c3d8f56b10263d2"><div class="ttname"><a href="a00245.html#ga36641e5d60f994e01c3d8f56b10263d2">glm::unProject</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; unProject(vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates using default near...</div></div>
<div class="ttc" id="a00245_html_gaf6b21eadb7ac2ecbbe258a9a233b4c82"><div class="ttname"><a href="a00245.html#gaf6b21eadb7ac2ecbbe258a9a233b4c82">glm::pickMatrix</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; pickMatrix(vec&lt; 2, T, Q &gt; const &amp;center, vec&lt; 2, T, Q &gt; const &amp;delta, vec&lt; 4, U, Q &gt; const &amp;viewport)</div><div class="ttdoc">Define a picking region. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
