<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: random.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">random.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00137.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_int_sized.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_uint_sized.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_random extension included&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;{</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00300.html#ga94731130c298a9ff5e5025fdee6d97a0">linearRand</a>(genType Min, genType Max);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00300.html#ga94731130c298a9ff5e5025fdee6d97a0">linearRand</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Min, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Max);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00300.html#ga5193a83e49e4fdc5652c084711083574">gaussRand</a>(genType Mean, genType Deviation);</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, defaultp&gt; <a class="code" href="a00300.html#ga9dd05c36025088fae25b97c869e88517">circularRand</a>(T Radius);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, defaultp&gt; <a class="code" href="a00300.html#ga22f90fcaccdf001c516ca90f6428e138">sphericalRand</a>(T Radius);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, defaultp&gt; <a class="code" href="a00300.html#gaa0b18071f3f97dbf8bcf6f53c6fe5f73">diskRand</a>(T Radius);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, defaultp&gt; <a class="code" href="a00300.html#ga7c53b7797f3147af68a11c767679fa3f">ballRand</a>(T Radius);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#include &quot;random.inl&quot;</span></div>
<div class="ttc" id="a00300_html_ga9dd05c36025088fae25b97c869e88517"><div class="ttname"><a href="a00300.html#ga9dd05c36025088fae25b97c869e88517">glm::circularRand</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 2, T, defaultp &gt; circularRand(T Radius)</div><div class="ttdoc">Generate a random 2D vector which coordinates are regulary distributed on a circle of a given radius...</div></div>
<div class="ttc" id="a00300_html_gaa0b18071f3f97dbf8bcf6f53c6fe5f73"><div class="ttname"><a href="a00300.html#gaa0b18071f3f97dbf8bcf6f53c6fe5f73">glm::diskRand</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 2, T, defaultp &gt; diskRand(T Radius)</div><div class="ttdoc">Generate a random 2D vector which coordinates are regulary distributed within the area of a disk of a...</div></div>
<div class="ttc" id="a00300_html_ga5193a83e49e4fdc5652c084711083574"><div class="ttname"><a href="a00300.html#ga5193a83e49e4fdc5652c084711083574">glm::gaussRand</a></div><div class="ttdeci">GLM_FUNC_DECL genType gaussRand(genType Mean, genType Deviation)</div><div class="ttdoc">Generate random numbers in the interval [Min, Max], according a gaussian distribution. </div></div>
<div class="ttc" id="a00300_html_ga22f90fcaccdf001c516ca90f6428e138"><div class="ttname"><a href="a00300.html#ga22f90fcaccdf001c516ca90f6428e138">glm::sphericalRand</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, defaultp &gt; sphericalRand(T Radius)</div><div class="ttdoc">Generate a random 3D vector which coordinates are regulary distributed on a sphere of a given radius...</div></div>
<div class="ttc" id="a00300_html_ga7c53b7797f3147af68a11c767679fa3f"><div class="ttname"><a href="a00300.html#ga7c53b7797f3147af68a11c767679fa3f">glm::ballRand</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, defaultp &gt; ballRand(T Radius)</div><div class="ttdoc">Generate a random 3D vector which coordinates are regulary distributed within the volume of a ball of...</div></div>
<div class="ttc" id="a00300_html_ga94731130c298a9ff5e5025fdee6d97a0"><div class="ttname"><a href="a00300.html#ga94731130c298a9ff5e5025fdee6d97a0">glm::linearRand</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; linearRand(vec&lt; L, T, Q &gt; const &amp;Min, vec&lt; L, T, Q &gt; const &amp;Max)</div><div class="ttdoc">Generate random numbers in the interval [Min, Max], according a linear distribution. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
