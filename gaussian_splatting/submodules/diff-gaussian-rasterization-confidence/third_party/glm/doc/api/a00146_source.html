<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: scalar_int_sized.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">scalar_int_sized.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00146.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_scalar_int_sized extension included&quot;)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;{</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#       if GLM_HAS_EXTENDED_INTEGER_TYPE</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">std::int8_t</a>                     <a class="code" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">std::int16_t</a>            <a class="code" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">std::int32_t</a>            <a class="code" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">signed</span> <span class="keywordtype">char</span>                     <a class="code" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">signed</span> <span class="keywordtype">short</span>            <a class="code" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">signed</span> <span class="keywordtype">int</span>                      <a class="code" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#endif//</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        <span class="keyword">struct </span>is_int&lt;int8&gt;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        {</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;                <span class="keyword">enum</span> test {value = ~0};</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        };</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        <span class="keyword">struct </span>is_int&lt;int16&gt;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        {</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;                <span class="keyword">enum</span> test {value = ~0};</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        };</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        <span class="keyword">struct </span>is_int&lt;<a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>&gt;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        {</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                <span class="keyword">enum</span> test {value = ~0};</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        };</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">   58</a></span>&#160;        <span class="keyword">typedef</span> detail::int8            <a class="code" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div>
<div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">   61</a></span>&#160;        <span class="keyword">typedef</span> detail::int16           <a class="code" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">   64</a></span>&#160;        <span class="keyword">typedef</span> detail::int32           <a class="code" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">   67</a></span>&#160;        <span class="keyword">typedef</span> detail::int64           <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="ttc" id="a00304_html_ga4bf09d8838a86866b39ee6e109341645"><div class="ttname"><a href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">glm::int8_t</a></div><div class="ttdeci">int8 int8_t</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00043">fwd.hpp:43</a></div></div>
<div class="ttc" id="a00260_html_ga1b956fe1df85f3c132b21edb4e116458"><div class="ttname"><a href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">glm::int8</a></div><div class="ttdeci">detail::int8 int8</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00058">scalar_int_sized.hpp:58</a></div></div>
<div class="ttc" id="a00304_html_gae8f5e3e964ca2ae240adc2c0d74adede"><div class="ttname"><a href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">glm::int16_t</a></div><div class="ttdeci">int16 int16_t</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00057">fwd.hpp:57</a></div></div>
<div class="ttc" id="a00304_html_ga042ef09ff2f0cb24a36f541bcb3a3710"><div class="ttname"><a href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">glm::int32_t</a></div><div class="ttdeci">int32 int32_t</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00071">fwd.hpp:71</a></div></div>
<div class="ttc" id="a00260_html_gaff5189f97f9e842d9636a0f240001b2e"><div class="ttname"><a href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">glm::int64</a></div><div class="ttdeci">detail::int64 int64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00067">scalar_int_sized.hpp:67</a></div></div>
<div class="ttc" id="a00260_html_ga43d43196463bde49cb067f5c20ab8481"><div class="ttname"><a href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">glm::int32</a></div><div class="ttdeci">detail::int32 int32</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00064">scalar_int_sized.hpp:64</a></div></div>
<div class="ttc" id="a00260_html_ga259fa4834387bd68627ddf37bb3ebdb9"><div class="ttname"><a href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">glm::int16</a></div><div class="ttdeci">detail::int16 int16</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00061">scalar_int_sized.hpp:61</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
