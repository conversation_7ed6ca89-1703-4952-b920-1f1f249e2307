<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: glm.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">glm.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00037.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#include &quot;detail/_fixes.hpp&quot;</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#include &quot;detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="preprocessor">#include &lt;climits&gt;</span></div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#include &lt;cfloat&gt;</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#include &lt;cassert&gt;</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor">#include &quot;fwd.hpp&quot;</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00184.html">vec2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00185.html">vec3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00186.html">vec4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00048.html">mat2x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00049.html">mat2x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00050.html">mat2x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00051.html">mat3x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00052.html">mat3x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00053.html">mat3x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00054.html">mat4x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00055.html">mat4x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00056.html">mat4x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00160.html">trigonometric.hpp</a>&quot;</span></div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00026.html">exponential.hpp</a>&quot;</span></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00015.html">common.hpp</a>&quot;</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00120.html">packing.hpp</a>&quot;</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00036.html">geometric.hpp</a>&quot;</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00057.html">matrix.hpp</a>&quot;</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00225.html">vector_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00043.html">integer.hpp</a>&quot;</span></div>
<div class="ttc" id="a00056_html"><div class="ttname"><a href="a00056.html">mat4x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00015_html"><div class="ttname"><a href="a00015.html">common.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00050_html"><div class="ttname"><a href="a00050.html">mat2x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00055_html"><div class="ttname"><a href="a00055.html">mat4x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00184_html"><div class="ttname"><a href="a00184.html">vec2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00043_html"><div class="ttname"><a href="a00043.html">integer.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00026_html"><div class="ttname"><a href="a00026.html">exponential.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00120_html"><div class="ttname"><a href="a00120.html">packing.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00049_html"><div class="ttname"><a href="a00049.html">mat2x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00054_html"><div class="ttname"><a href="a00054.html">mat4x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00225_html"><div class="ttname"><a href="a00225.html">vector_relational.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00052_html"><div class="ttname"><a href="a00052.html">mat3x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00057_html"><div class="ttname"><a href="a00057.html">matrix.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00186_html"><div class="ttname"><a href="a00186.html">vec4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00160_html"><div class="ttname"><a href="a00160.html">trigonometric.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00053_html"><div class="ttname"><a href="a00053.html">mat3x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00051_html"><div class="ttname"><a href="a00051.html">mat3x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00185_html"><div class="ttname"><a href="a00185.html">vec3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00048_html"><div class="ttname"><a href="a00048.html">mat2x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00036_html"><div class="ttname"><a href="a00036.html">geometric.hpp</a></div><div class="ttdoc">Core features </div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
