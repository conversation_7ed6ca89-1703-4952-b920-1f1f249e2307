<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: packing.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">packing.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00120.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00228.html">./ext/vector_uint2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00208.html">./ext/vector_float2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00212.html">./ext/vector_float4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;{</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00372.html#ga0e2d107039fe608a209497af867b85fb">packUnorm2x16</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00372.html#ga977ab172da5494e5ac63e952afacfbe2">packSnorm2x16</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00372.html#gaf7d2f7341a9eeb4a436929d6f9ad08f2">packUnorm4x8</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00372.html#ga85e8f17627516445026ab7a9c2e3531a">packSnorm4x8</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <a class="code" href="a00372.html#ga1f66188e5d65afeb9ffba1ad971e4007">unpackUnorm2x16</a>(uint p);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <a class="code" href="a00372.html#gacd8f8971a3fe28418be0d0fa1f786b38">unpackSnorm2x16</a>(uint p);</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00372.html#ga7f903259150b67e9466f5f8edffcd197">unpackUnorm4x8</a>(uint p);</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00372.html#ga2db488646d48b7c43d3218954523fe82">unpackSnorm4x8</a>(uint p);</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">double</span> <a class="code" href="a00372.html#gaa916ca426b2bb0343ba17e3753e245c2">packDouble2x32</a>(<a class="code" href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">uvec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">uvec2</a> <a class="code" href="a00372.html#ga5f4296dc5f12f0aa67ac05b8bb322483">unpackDouble2x32</a>(<span class="keywordtype">double</span> v);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00372.html#ga20f134b07db3a3d3a38efb2617388c92">packHalf2x16</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <a class="code" href="a00372.html#gaf59b52e6b28da9335322c4ae19b5d745">unpackHalf2x16</a>(uint v);</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="preprocessor">#include &quot;detail/func_packing.inl&quot;</span></div>
<div class="ttc" id="a00372_html_ga1f66188e5d65afeb9ffba1ad971e4007"><div class="ttname"><a href="a00372.html#ga1f66188e5d65afeb9ffba1ad971e4007">glm::unpackUnorm2x16</a></div><div class="ttdeci">GLM_FUNC_DECL vec2 unpackUnorm2x16(uint p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </div></div>
<div class="ttc" id="a00281_html_gabe65c061834f61b4f7cb6037b19006a4"><div class="ttname"><a href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">glm::vec2</a></div><div class="ttdeci">vec&lt; 2, float, defaultp &gt; vec2</div><div class="ttdoc">2 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00208_source.html#l00015">vector_float2.hpp:15</a></div></div>
<div class="ttc" id="a00372_html_ga977ab172da5494e5ac63e952afacfbe2"><div class="ttname"><a href="a00372.html#ga977ab172da5494e5ac63e952afacfbe2">glm::packSnorm2x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint packSnorm2x16(vec2 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer val...</div></div>
<div class="ttc" id="a00372_html_ga85e8f17627516445026ab7a9c2e3531a"><div class="ttname"><a href="a00372.html#ga85e8f17627516445026ab7a9c2e3531a">glm::packSnorm4x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint packSnorm4x8(vec4 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer val...</div></div>
<div class="ttc" id="a00372_html_ga0e2d107039fe608a209497af867b85fb"><div class="ttname"><a href="a00372.html#ga0e2d107039fe608a209497af867b85fb">glm::packUnorm2x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint packUnorm2x16(vec2 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer val...</div></div>
<div class="ttc" id="a00372_html_ga5f4296dc5f12f0aa67ac05b8bb322483"><div class="ttname"><a href="a00372.html#ga5f4296dc5f12f0aa67ac05b8bb322483">glm::unpackDouble2x32</a></div><div class="ttdeci">GLM_FUNC_DECL uvec2 unpackDouble2x32(double v)</div><div class="ttdoc">Returns a two-component unsigned integer vector representation of v. </div></div>
<div class="ttc" id="a00281_html_gac215a35481a6597d1bf622a382e9d6e2"><div class="ttname"><a href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">glm::vec4</a></div><div class="ttdeci">vec&lt; 4, float, defaultp &gt; vec4</div><div class="ttdoc">4 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00212_source.html#l00015">vector_float4.hpp:15</a></div></div>
<div class="ttc" id="a00372_html_gacd8f8971a3fe28418be0d0fa1f786b38"><div class="ttname"><a href="a00372.html#gacd8f8971a3fe28418be0d0fa1f786b38">glm::unpackSnorm2x16</a></div><div class="ttdeci">GLM_FUNC_DECL vec2 unpackSnorm2x16(uint p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </div></div>
<div class="ttc" id="a00281_html_ga2f6d9ec3ae14813ade37d6aee3715fdb"><div class="ttname"><a href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">glm::uvec2</a></div><div class="ttdeci">vec&lt; 2, unsigned int, defaultp &gt; uvec2</div><div class="ttdoc">2 components vector of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00228_source.html#l00015">vector_uint2.hpp:15</a></div></div>
<div class="ttc" id="a00372_html_gaf59b52e6b28da9335322c4ae19b5d745"><div class="ttname"><a href="a00372.html#gaf59b52e6b28da9335322c4ae19b5d745">glm::unpackHalf2x16</a></div><div class="ttdeci">GLM_FUNC_DECL vec2 unpackHalf2x16(uint v)</div><div class="ttdoc">Returns a two-component floating-point vector with components obtained by unpacking a 32-bit unsigned...</div></div>
<div class="ttc" id="a00212_html"><div class="ttname"><a href="a00212.html">vector_float4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00372_html_gaf7d2f7341a9eeb4a436929d6f9ad08f2"><div class="ttname"><a href="a00372.html#gaf7d2f7341a9eeb4a436929d6f9ad08f2">glm::packUnorm4x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint packUnorm4x8(vec4 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer val...</div></div>
<div class="ttc" id="a00372_html_ga2db488646d48b7c43d3218954523fe82"><div class="ttname"><a href="a00372.html#ga2db488646d48b7c43d3218954523fe82">glm::unpackSnorm4x8</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackSnorm4x8(uint p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </div></div>
<div class="ttc" id="a00372_html_gaa916ca426b2bb0343ba17e3753e245c2"><div class="ttname"><a href="a00372.html#gaa916ca426b2bb0343ba17e3753e245c2">glm::packDouble2x32</a></div><div class="ttdeci">GLM_FUNC_DECL double packDouble2x32(uvec2 const &amp;v)</div><div class="ttdoc">Returns a double-qualifier value obtained by packing the components of v into a 64-bit value...</div></div>
<div class="ttc" id="a00372_html_ga20f134b07db3a3d3a38efb2617388c92"><div class="ttname"><a href="a00372.html#ga20f134b07db3a3d3a38efb2617388c92">glm::packHalf2x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint packHalf2x16(vec2 const &amp;v)</div><div class="ttdoc">Returns an unsigned integer obtained by converting the components of a two-component floating-point v...</div></div>
<div class="ttc" id="a00208_html"><div class="ttname"><a href="a00208.html">vector_float2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00372_html_ga7f903259150b67e9466f5f8edffcd197"><div class="ttname"><a href="a00372.html#ga7f903259150b67e9466f5f8edffcd197">glm::unpackUnorm4x8</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackUnorm4x8(uint p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </div></div>
<div class="ttc" id="a00228_html"><div class="ttname"><a href="a00228.html">vector_uint2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
