<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: quaternion.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtx/quaternion.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00126.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../gtc/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_exponential.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../gtx/norm.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_quaternion is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_quaternion extension included&quot;)</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;{</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#ga5ee8332600b2aca3a77622a28d857b55">quat_identity</a>();</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00352.html#ga9f5f77255756e5668dfee7f0d07ed021">cross</a>(</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q,</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00352.html#ga9f5f77255756e5668dfee7f0d07ed021">cross</a>(</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v,</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#ga0b9bf3459e132ad8a18fe970669e3e35">squad</a>(</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q1,</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q2,</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; s1,</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; s2,</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                T <span class="keyword">const</span>&amp; h);</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#gacc5cd5f3e78de61d141c2355417424de">intermediate</a>(</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; prev,</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; curr,</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; next);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="comment">//template&lt;typename T, qualifier Q&gt;</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        <span class="comment">//qua&lt;T, Q&gt; sqrt(</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <span class="comment">//      qua&lt;T, Q&gt; const&amp; q);</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00352.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">rotate</a>(</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q,</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00352.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">rotate</a>(</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q,</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00352.html#ga321953c1b2e7befe6f5dcfddbfc6b76b">extractRealComponent</a>(</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q);</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="a00352.html#gaab0afabb894b28a983fb8ec610409d56">  113</a></span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00352.html#gaab0afabb894b28a983fb8ec610409d56">toMat3</a>(</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00299.html#ga333ab70047fbe4132406100c292dbc89">mat3_cast</a>(x);}</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="a00352.html#gadfa2c77094e8cc9adad321d938855ffb">  120</a></span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00352.html#gadfa2c77094e8cc9adad321d938855ffb">toMat4</a>(</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">mat4_cast</a>(x);}</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="a00352.html#ga798de5d186499c9a9231cd92c8afaef1">  127</a></span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#ga5eb36f51e1638e710451eba194dbc011">toQuat</a>(</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">quat_cast</a>(x);}</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="a00352.html#ga5eb36f51e1638e710451eba194dbc011">  134</a></span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#ga5eb36f51e1638e710451eba194dbc011">toQuat</a>(</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;                mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">quat_cast</a>(x);}</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#gadc576cc957adc2a568cdcbc3799175bc">shortMix</a>(</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;                T <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#ga264e10708d58dd0ff53b7902a2bd2561">fastMix</a>(</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x,</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;                qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y,</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;                T <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00352.html#ga03e61282831cc3f52cc76f72f52ad2c5">rotation</a>(</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; orig,</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; dest);</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00352.html#ga58a609b1b8ab965f5df2702e8ca4e75b">length2</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q);</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;<span class="preprocessor">#include &quot;quaternion.inl&quot;</span></div>
<div class="ttc" id="a00299_html_ga1113212d9bdefc2e31ad40e5bbb506f3"><div class="ttname"><a href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">glm::mat4_cast</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; mat4_cast(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 4 * 4 matrix. </div></div>
<div class="ttc" id="a00352_html_gadc576cc957adc2a568cdcbc3799175bc"><div class="ttname"><a href="a00352.html#gadc576cc957adc2a568cdcbc3799175bc">glm::shortMix</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; shortMix(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T const &amp;a)</div><div class="ttdoc">Quaternion interpolation using the rotation short path. </div></div>
<div class="ttc" id="a00352_html_ga5ee8332600b2aca3a77622a28d857b55"><div class="ttname"><a href="a00352.html#ga5ee8332600b2aca3a77622a28d857b55">glm::quat_identity</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; quat_identity()</div><div class="ttdoc">Create an identity quaternion. </div></div>
<div class="ttc" id="a00299_html_ga1108a4ab88ca87bac321454eea7702f8"><div class="ttname"><a href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">glm::quat_cast</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; quat_cast(mat&lt; 3, 3, T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a pure rotation 3 * 3 matrix to a quaternion. </div></div>
<div class="ttc" id="a00352_html_gacc5cd5f3e78de61d141c2355417424de"><div class="ttname"><a href="a00352.html#gacc5cd5f3e78de61d141c2355417424de">glm::intermediate</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; intermediate(qua&lt; T, Q &gt; const &amp;prev, qua&lt; T, Q &gt; const &amp;curr, qua&lt; T, Q &gt; const &amp;next)</div><div class="ttdoc">Returns an intermediate control point for squad interpolation. </div></div>
<div class="ttc" id="a00299_html_ga333ab70047fbe4132406100c292dbc89"><div class="ttname"><a href="a00299.html#ga333ab70047fbe4132406100c292dbc89">glm::mat3_cast</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt; mat3_cast(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 3 * 3 matrix. </div></div>
<div class="ttc" id="a00352_html_gadfa2c77094e8cc9adad321d938855ffb"><div class="ttname"><a href="a00352.html#gadfa2c77094e8cc9adad321d938855ffb">glm::toMat4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; toMat4(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 4 * 4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00126_source.html#l00120">gtx/quaternion.hpp:120</a></div></div>
<div class="ttc" id="a00352_html_ga321953c1b2e7befe6f5dcfddbfc6b76b"><div class="ttname"><a href="a00352.html#ga321953c1b2e7befe6f5dcfddbfc6b76b">glm::extractRealComponent</a></div><div class="ttdeci">GLM_FUNC_DECL T extractRealComponent(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Extract the real component of a quaternion. </div></div>
<div class="ttc" id="a00352_html_gaab0afabb894b28a983fb8ec610409d56"><div class="ttname"><a href="a00352.html#gaab0afabb894b28a983fb8ec610409d56">glm::toMat3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt; toMat3(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 3 * 3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00126_source.html#l00113">gtx/quaternion.hpp:113</a></div></div>
<div class="ttc" id="a00352_html_ga0b9bf3459e132ad8a18fe970669e3e35"><div class="ttname"><a href="a00352.html#ga0b9bf3459e132ad8a18fe970669e3e35">glm::squad</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; squad(qua&lt; T, Q &gt; const &amp;q1, qua&lt; T, Q &gt; const &amp;q2, qua&lt; T, Q &gt; const &amp;s1, qua&lt; T, Q &gt; const &amp;s2, T const &amp;h)</div><div class="ttdoc">Compute a point on a path according squad equation. </div></div>
<div class="ttc" id="a00352_html_ga9f5f77255756e5668dfee7f0d07ed021"><div class="ttname"><a href="a00352.html#ga9f5f77255756e5668dfee7f0d07ed021">glm::cross</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; cross(vec&lt; 3, T, Q &gt; const &amp;v, qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Compute a cross product between a vector and a quaternion. </div></div>
<div class="ttc" id="a00352_html_ga5eb36f51e1638e710451eba194dbc011"><div class="ttname"><a href="a00352.html#ga5eb36f51e1638e710451eba194dbc011">glm::toQuat</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; toQuat(mat&lt; 4, 4, T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a 4 * 4 matrix to a quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00126_source.html#l00134">gtx/quaternion.hpp:134</a></div></div>
<div class="ttc" id="a00352_html_ga03e61282831cc3f52cc76f72f52ad2c5"><div class="ttname"><a href="a00352.html#ga03e61282831cc3f52cc76f72f52ad2c5">glm::rotation</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; rotation(vec&lt; 3, T, Q &gt; const &amp;orig, vec&lt; 3, T, Q &gt; const &amp;dest)</div><div class="ttdoc">Compute the rotation between two vectors. </div></div>
<div class="ttc" id="a00352_html_gafcb78dfff45fbf19a7fcb2bd03fbf196"><div class="ttname"><a href="a00352.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">glm::rotate</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, Q &gt; rotate(qua&lt; T, Q &gt; const &amp;q, vec&lt; 4, T, Q &gt; const &amp;v)</div><div class="ttdoc">Rotates a 4 components vector by a quaternion. </div></div>
<div class="ttc" id="a00352_html_ga264e10708d58dd0ff53b7902a2bd2561"><div class="ttname"><a href="a00352.html#ga264e10708d58dd0ff53b7902a2bd2561">glm::fastMix</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; fastMix(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T const &amp;a)</div><div class="ttdoc">Quaternion normalized linear interpolation. </div></div>
<div class="ttc" id="a00352_html_ga58a609b1b8ab965f5df2702e8ca4e75b"><div class="ttname"><a href="a00352.html#ga58a609b1b8ab965f5df2702e8ca4e75b">glm::length2</a></div><div class="ttdeci">GLM_FUNC_DECL T length2(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the squared length of x. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
