<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: number_precision.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">number_precision.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00116.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../gtc/type_precision.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_number_precision is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_number_precision extension included&quot;)</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span>gtx</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;{</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        <span class="comment">// Unsigned int vector types</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="a00346.html#ga5853fe457f4c8a6bc09343d0e9833980">   38</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">u8</a>                      <a class="code" href="a00346.html#ga5853fe457f4c8a6bc09343d0e9833980">u8vec1</a>;         </div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="a00346.html#ga52cc069a92e126c3a8dcde93424d2ef0">   39</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">u16</a>                     <a class="code" href="a00346.html#ga52cc069a92e126c3a8dcde93424d2ef0">u16vec1</a>;    </div>
<div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="a00346.html#ga9bbc1e14aea65cba5e2dcfef6a67d9f3">   40</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga8165913e068444f7842302d40ba897b9">u32</a>                     <a class="code" href="a00346.html#ga9bbc1e14aea65cba5e2dcfef6a67d9f3">u32vec1</a>;    </div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="a00346.html#ga818de170e2584ab037130f2881925974">   41</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#gaf3f312156984c365e9f65620354da70b">u64</a>                     <a class="code" href="a00346.html#ga818de170e2584ab037130f2881925974">u64vec1</a>;    </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="comment">// Float vector types</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00346.html#ga07f8d7348eb7ae059a84c118fdfeb943">   46</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>                     <a class="code" href="a00346.html#ga07f8d7348eb7ae059a84c118fdfeb943">f32vec1</a>;    </div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="a00346.html#gae5987a61b8c03d5c432a9e62f0b3efe1">   47</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>                     <a class="code" href="a00346.html#gae5987a61b8c03d5c432a9e62f0b3efe1">f64vec1</a>;    </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="comment">// Float matrix types</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="a00346.html#ga145ad477a2a3e152855511c3b52469a6">   52</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>                     <a class="code" href="a00346.html#ga145ad477a2a3e152855511c3b52469a6">f32mat1</a>;        </div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="a00346.html#gac88c6a4dbfc380aa26e3adbbade36348">   53</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>                     <a class="code" href="a00346.html#gac88c6a4dbfc380aa26e3adbbade36348">f32mat1x1</a>;      </div>
<div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="a00346.html#ga59bfa589419b5265d01314fcecd33435">   54</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>                     <a class="code" href="a00346.html#ga59bfa589419b5265d01314fcecd33435">f64mat1</a>;        </div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="a00346.html#ga448eeb08d0b7d8c43a8b292c981955fd">   55</a></span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>                     <a class="code" href="a00346.html#ga448eeb08d0b7d8c43a8b292c981955fd">f64mat1x1</a>;      </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;}<span class="comment">//namespace gtx</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#include &quot;number_precision.inl&quot;</span></div>
<div class="ttc" id="a00304_html_ga8165913e068444f7842302d40ba897b9"><div class="ttname"><a href="a00304.html#ga8165913e068444f7842302d40ba897b9">glm::u32</a></div><div class="ttdeci">uint32 u32</div><div class="ttdoc">Default qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00120">fwd.hpp:120</a></div></div>
<div class="ttc" id="a00304_html_gaf3f312156984c365e9f65620354da70b"><div class="ttname"><a href="a00304.html#gaf3f312156984c365e9f65620354da70b">glm::u64</a></div><div class="ttdeci">uint64 u64</div><div class="ttdoc">Default qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00134">fwd.hpp:134</a></div></div>
<div class="ttc" id="a00346_html_gac88c6a4dbfc380aa26e3adbbade36348"><div class="ttname"><a href="a00346.html#gac88c6a4dbfc380aa26e3adbbade36348">glm::gtx::f32mat1x1</a></div><div class="ttdeci">f32 f32mat1x1</div><div class="ttdoc">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00053">number_precision.hpp:53</a></div></div>
<div class="ttc" id="a00346_html_ga59bfa589419b5265d01314fcecd33435"><div class="ttname"><a href="a00346.html#ga59bfa589419b5265d01314fcecd33435">glm::gtx::f64mat1</a></div><div class="ttdeci">f64 f64mat1</div><div class="ttdoc">Double-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00054">number_precision.hpp:54</a></div></div>
<div class="ttc" id="a00346_html_ga52cc069a92e126c3a8dcde93424d2ef0"><div class="ttname"><a href="a00346.html#ga52cc069a92e126c3a8dcde93424d2ef0">glm::gtx::u16vec1</a></div><div class="ttdeci">u16 u16vec1</div><div class="ttdoc">16bit unsigned integer scalar. (from GLM_GTX_number_precision extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00039">number_precision.hpp:39</a></div></div>
<div class="ttc" id="a00304_html_gaecc7082561fc9028b844b6cf3d305d36"><div class="ttname"><a href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">glm::u8</a></div><div class="ttdeci">uint8 u8</div><div class="ttdoc">Default qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00092">fwd.hpp:92</a></div></div>
<div class="ttc" id="a00346_html_ga145ad477a2a3e152855511c3b52469a6"><div class="ttname"><a href="a00346.html#ga145ad477a2a3e152855511c3b52469a6">glm::gtx::f32mat1</a></div><div class="ttdeci">f32 f32mat1</div><div class="ttdoc">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00052">number_precision.hpp:52</a></div></div>
<div class="ttc" id="a00346_html_ga07f8d7348eb7ae059a84c118fdfeb943"><div class="ttname"><a href="a00346.html#ga07f8d7348eb7ae059a84c118fdfeb943">glm::gtx::f32vec1</a></div><div class="ttdeci">f32 f32vec1</div><div class="ttdoc">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00046">number_precision.hpp:46</a></div></div>
<div class="ttc" id="a00346_html_gae5987a61b8c03d5c432a9e62f0b3efe1"><div class="ttname"><a href="a00346.html#gae5987a61b8c03d5c432a9e62f0b3efe1">glm::gtx::f64vec1</a></div><div class="ttdeci">f64 f64vec1</div><div class="ttdoc">Single-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00047">number_precision.hpp:47</a></div></div>
<div class="ttc" id="a00346_html_ga448eeb08d0b7d8c43a8b292c981955fd"><div class="ttname"><a href="a00346.html#ga448eeb08d0b7d8c43a8b292c981955fd">glm::gtx::f64mat1x1</a></div><div class="ttdeci">f64 f64mat1x1</div><div class="ttdoc">Double-qualifier floating-point scalar. (from GLM_GTX_number_precision extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00055">number_precision.hpp:55</a></div></div>
<div class="ttc" id="a00346_html_ga818de170e2584ab037130f2881925974"><div class="ttname"><a href="a00346.html#ga818de170e2584ab037130f2881925974">glm::gtx::u64vec1</a></div><div class="ttdeci">u64 u64vec1</div><div class="ttdoc">64bit unsigned integer scalar. (from GLM_GTX_number_precision extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00041">number_precision.hpp:41</a></div></div>
<div class="ttc" id="a00346_html_ga9bbc1e14aea65cba5e2dcfef6a67d9f3"><div class="ttname"><a href="a00346.html#ga9bbc1e14aea65cba5e2dcfef6a67d9f3">glm::gtx::u32vec1</a></div><div class="ttdeci">u32 u32vec1</div><div class="ttdoc">32bit unsigned integer scalar. (from GLM_GTX_number_precision extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00040">number_precision.hpp:40</a></div></div>
<div class="ttc" id="a00304_html_gabe6a542dd6c1d5ffd847f1b9b4c9c9b7"><div class="ttname"><a href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">glm::f32</a></div><div class="ttdeci">float f32</div><div class="ttdoc">Default 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00150">fwd.hpp:150</a></div></div>
<div class="ttc" id="a00304_html_gaa2d7acc0adb536fab71fe261232a40ff"><div class="ttname"><a href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">glm::u16</a></div><div class="ttdeci">uint16 u16</div><div class="ttdoc">Default qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00106">fwd.hpp:106</a></div></div>
<div class="ttc" id="a00346_html_ga5853fe457f4c8a6bc09343d0e9833980"><div class="ttname"><a href="a00346.html#ga5853fe457f4c8a6bc09343d0e9833980">glm::gtx::u8vec1</a></div><div class="ttdeci">u8 u8vec1</div><div class="ttdoc">8bit unsigned integer scalar. (from GLM_GTX_number_precision extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00116_source.html#l00038">number_precision.hpp:38</a></div></div>
<div class="ttc" id="a00304_html_ga1d794d240091678f602e8de225b8d8c9"><div class="ttname"><a href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">glm::f64</a></div><div class="ttdeci">double f64</div><div class="ttdoc">Default 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00166">fwd.hpp:166</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
