<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: fast_square_root.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">fast_square_root.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00324.html">GLM_GTX_fast_square_root</a>  
<a href="#details">More...</a></p>

<p><a href="a00032_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaac333418d0c4e0cc6d3d219ed606c238"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaac333418d0c4e0cc6d3d219ed606c238"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#gaac333418d0c4e0cc6d3d219ed606c238">fastDistance</a> (genType x, genType y)</td></tr>
<tr class="memdesc:gaac333418d0c4e0cc6d3d219ed606c238"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common distance function but less accurate.  <a href="a00324.html#gaac333418d0c4e0cc6d3d219ed606c238">More...</a><br /></td></tr>
<tr class="separator:gaac333418d0c4e0cc6d3d219ed606c238"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga42d3e771fa7cb3c60d828e315829df19"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga42d3e771fa7cb3c60d828e315829df19"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#ga42d3e771fa7cb3c60d828e315829df19">fastDistance</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga42d3e771fa7cb3c60d828e315829df19"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common distance function but less accurate.  <a href="a00324.html#ga42d3e771fa7cb3c60d828e315829df19">More...</a><br /></td></tr>
<tr class="separator:ga42d3e771fa7cb3c60d828e315829df19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f081b14d9c7035c8714eba5f7f75a8f"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga7f081b14d9c7035c8714eba5f7f75a8f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#ga7f081b14d9c7035c8714eba5f7f75a8f">fastInverseSqrt</a> (genType x)</td></tr>
<tr class="memdesc:ga7f081b14d9c7035c8714eba5f7f75a8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common inversesqrt function but less accurate.  <a href="a00324.html#ga7f081b14d9c7035c8714eba5f7f75a8f">More...</a><br /></td></tr>
<tr class="separator:ga7f081b14d9c7035c8714eba5f7f75a8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadcd7be12b1e5ee182141359d4c45dd24"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gadcd7be12b1e5ee182141359d4c45dd24"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#gadcd7be12b1e5ee182141359d4c45dd24">fastInverseSqrt</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gadcd7be12b1e5ee182141359d4c45dd24"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common inversesqrt function but less accurate.  <a href="a00324.html#gadcd7be12b1e5ee182141359d4c45dd24">More...</a><br /></td></tr>
<tr class="separator:gadcd7be12b1e5ee182141359d4c45dd24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe697d6287719538346bbdf8b1367c59"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gafe697d6287719538346bbdf8b1367c59"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#gafe697d6287719538346bbdf8b1367c59">fastLength</a> (genType x)</td></tr>
<tr class="memdesc:gafe697d6287719538346bbdf8b1367c59"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common length function but less accurate.  <a href="a00324.html#gafe697d6287719538346bbdf8b1367c59">More...</a><br /></td></tr>
<tr class="separator:gafe697d6287719538346bbdf8b1367c59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90f66be92ef61e705c005e7b3209edb8"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga90f66be92ef61e705c005e7b3209edb8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#ga90f66be92ef61e705c005e7b3209edb8">fastLength</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga90f66be92ef61e705c005e7b3209edb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common length function but less accurate.  <a href="a00324.html#ga90f66be92ef61e705c005e7b3209edb8">More...</a><br /></td></tr>
<tr class="separator:ga90f66be92ef61e705c005e7b3209edb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b02c1d6e0c754144e2f1e110bf9f16c"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga3b02c1d6e0c754144e2f1e110bf9f16c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#ga3b02c1d6e0c754144e2f1e110bf9f16c">fastNormalize</a> (genType const &amp;x)</td></tr>
<tr class="memdesc:ga3b02c1d6e0c754144e2f1e110bf9f16c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common normalize function but less accurate.  <a href="a00324.html#ga3b02c1d6e0c754144e2f1e110bf9f16c">More...</a><br /></td></tr>
<tr class="separator:ga3b02c1d6e0c754144e2f1e110bf9f16c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c460e9414a50b2fc455c8f64c86cdc9"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga6c460e9414a50b2fc455c8f64c86cdc9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#ga6c460e9414a50b2fc455c8f64c86cdc9">fastSqrt</a> (genType x)</td></tr>
<tr class="memdesc:ga6c460e9414a50b2fc455c8f64c86cdc9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common sqrt function but less accurate.  <a href="a00324.html#ga6c460e9414a50b2fc455c8f64c86cdc9">More...</a><br /></td></tr>
<tr class="separator:ga6c460e9414a50b2fc455c8f64c86cdc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae83f0c03614f73eae5478c5b6274ee6d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae83f0c03614f73eae5478c5b6274ee6d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00324.html#gae83f0c03614f73eae5478c5b6274ee6d">fastSqrt</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gae83f0c03614f73eae5478c5b6274ee6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common sqrt function but less accurate.  <a href="a00324.html#gae83f0c03614f73eae5478c5b6274ee6d">More...</a><br /></td></tr>
<tr class="separator:gae83f0c03614f73eae5478c5b6274ee6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00324.html">GLM_GTX_fast_square_root</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00032_source.html">fast_square_root.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
