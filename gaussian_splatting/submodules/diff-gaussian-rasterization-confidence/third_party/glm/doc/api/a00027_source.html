<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: ext.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">ext.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00027.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#include &quot;detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;</div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;</div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00037.html">glm.hpp</a>&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_MESSAGE_EXT_INCLUDED_DISPLAYED)</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#       define GLM_MESSAGE_EXT_INCLUDED_DISPLAYED</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: All extensions included (not recommended)&quot;)</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#endif//GLM_MESSAGES</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00063.html">./ext/matrix_double2x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00064.html">./ext/matrix_double2x2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00065.html">./ext/matrix_double2x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00066.html">./ext/matrix_double2x3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00067.html">./ext/matrix_double2x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00068.html">./ext/matrix_double2x4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00069.html">./ext/matrix_double3x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00070.html">./ext/matrix_double3x2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00071.html">./ext/matrix_double3x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00072.html">./ext/matrix_double3x3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00073.html">./ext/matrix_double3x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00074.html">./ext/matrix_double3x4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00075.html">./ext/matrix_double4x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00076.html">./ext/matrix_double4x2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00077.html">./ext/matrix_double4x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00078.html">./ext/matrix_double4x3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00079.html">./ext/matrix_double4x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00080.html">./ext/matrix_double4x4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00082.html">./ext/matrix_float2x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00083.html">./ext/matrix_float2x2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00084.html">./ext/matrix_float2x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00085.html">./ext/matrix_float2x3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00086.html">./ext/matrix_float2x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00087.html">./ext/matrix_float2x4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00088.html">./ext/matrix_float3x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00089.html">./ext/matrix_float3x2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00090.html">./ext/matrix_float3x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00091.html">./ext/matrix_float3x3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00092.html">./ext/matrix_float3x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00093.html">./ext/matrix_float3x4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00094.html">./ext/matrix_float4x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#include &quot;./ext/matrix_float4x2_precision.hpp&quot;</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00096.html">./ext/matrix_float4x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00097.html">./ext/matrix_float4x3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00098.html">./ext/matrix_float4x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00099.html">./ext/matrix_float4x4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00107.html">./ext/matrix_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00128.html">./ext/quaternion_double.hpp</a>&quot;</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00129.html">./ext/quaternion_double_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00131.html">./ext/quaternion_float.hpp</a>&quot;</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00132.html">./ext/quaternion_float_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00133.html">./ext/quaternion_geometric.hpp</a>&quot;</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00134.html">./ext/quaternion_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00145.html">./ext/scalar_constants.hpp</a>&quot;</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00146.html">./ext/scalar_int_sized.hpp</a>&quot;</span></div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00149.html">./ext/scalar_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00189.html">./ext/vector_bool1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00190.html">./ext/vector_bool1_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00191.html">./ext/vector_bool2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00192.html">./ext/vector_bool2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00193.html">./ext/vector_bool3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00194.html">./ext/vector_bool3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00195.html">./ext/vector_bool4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00196.html">./ext/vector_bool4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00198.html">./ext/vector_double1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00199.html">./ext/vector_double1_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00200.html">./ext/vector_double2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00201.html">./ext/vector_double2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00202.html">./ext/vector_double3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00203.html">./ext/vector_double3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00204.html">./ext/vector_double4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00205.html">./ext/vector_double4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00206.html">./ext/vector_float1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00207.html">./ext/vector_float1_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00208.html">./ext/vector_float2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00209.html">./ext/vector_float2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00210.html">./ext/vector_float3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00211.html">./ext/vector_float3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00212.html">./ext/vector_float4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00213.html">./ext/vector_float4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00214.html">./ext/vector_int1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00215.html">./ext/vector_int1_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00216.html">./ext/vector_int2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00217.html">./ext/vector_int2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00218.html">./ext/vector_int3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00219.html">./ext/vector_int3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00220.html">./ext/vector_int4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00221.html">./ext/vector_int4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00224.html">./ext/vector_relational.hpp</a>&quot;</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00226.html">./ext/vector_uint1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00227.html">./ext/vector_uint1_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00228.html">./ext/vector_uint2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00229.html">./ext/vector_uint2_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00230.html">./ext/vector_uint3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00231.html">./ext/vector_uint3_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00232.html">./ext/vector_uint4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00233.html">./ext/vector_uint4_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00009.html">./gtc/bitfield.hpp</a>&quot;</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00012.html">./gtc/color_space.hpp</a>&quot;</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00021.html">./gtc/constants.hpp</a>&quot;</span></div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00024.html">./gtc/epsilon.hpp</a>&quot;</span></div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00041.html">./gtc/integer.hpp</a>&quot;</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00058.html">./gtc/matrix_access.hpp</a>&quot;</span></div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00100.html">./gtc/matrix_integer.hpp</a>&quot;</span></div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00102.html">./gtc/matrix_inverse.hpp</a>&quot;</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00109.html">./gtc/matrix_transform.hpp</a>&quot;</span></div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00112.html">./gtc/noise.hpp</a>&quot;</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00119.html">./gtc/packing.hpp</a>&quot;</span></div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00125.html">./gtc/quaternion.hpp</a>&quot;</span></div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00137.html">./gtc/random.hpp</a>&quot;</span></div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00140.html">./gtc/reciprocal.hpp</a>&quot;</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00143.html">./gtc/round.hpp</a>&quot;</span></div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00174.html">./gtc/type_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00175.html">./gtc/type_ptr.hpp</a>&quot;</span></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00182.html">./gtc/ulp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00183.html">./gtc/vec1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor">#if GLM_CONFIG_ALIGNED_GENTYPES == GLM_ENABLE</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="preprocessor">#       include &quot;<a class="code" href="a00161.html">./gtc/type_aligned.hpp</a>&quot;</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="preprocessor">#ifdef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00007.html">./gtx/associated_min_max.hpp</a>&quot;</span></div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00008.html">./gtx/bit.hpp</a>&quot;</span></div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00010.html">./gtx/closest_point.hpp</a>&quot;</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00011.html">./gtx/color_encoding.hpp</a>&quot;</span></div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00013.html">./gtx/color_space.hpp</a>&quot;</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00014.html">./gtx/color_space_YCoCg.hpp</a>&quot;</span></div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00017.html">./gtx/compatibility.hpp</a>&quot;</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00018.html">./gtx/component_wise.hpp</a>&quot;</span></div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00022.html">./gtx/dual_quaternion.hpp</a>&quot;</span></div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00025.html">./gtx/euler_angles.hpp</a>&quot;</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00028.html">./gtx/extend.hpp</a>&quot;</span></div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00029.html">./gtx/extended_min_max.hpp</a>&quot;</span></div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00031.html">./gtx/fast_exponential.hpp</a>&quot;</span></div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00032.html">./gtx/fast_square_root.hpp</a>&quot;</span></div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00033.html">./gtx/fast_trigonometry.hpp</a>&quot;</span></div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00034.html">./gtx/functions.hpp</a>&quot;</span></div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00038.html">./gtx/gradient_paint.hpp</a>&quot;</span></div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00039.html">./gtx/handed_coordinate_space.hpp</a>&quot;</span></div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00042.html">./gtx/integer.hpp</a>&quot;</span></div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00044.html">./gtx/intersect.hpp</a>&quot;</span></div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00046.html">./gtx/log_base.hpp</a>&quot;</span></div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00061.html">./gtx/matrix_cross_product.hpp</a>&quot;</span></div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00101.html">./gtx/matrix_interpolation.hpp</a>&quot;</span></div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00103.html">./gtx/matrix_major_storage.hpp</a>&quot;</span></div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00104.html">./gtx/matrix_operation.hpp</a>&quot;</span></div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00106.html">./gtx/matrix_query.hpp</a>&quot;</span></div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00111.html">./gtx/mixed_product.hpp</a>&quot;</span></div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00113.html">./gtx/norm.hpp</a>&quot;</span></div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00114.html">./gtx/normal.hpp</a>&quot;</span></div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00115.html">./gtx/normalize_dot.hpp</a>&quot;</span></div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00116.html">./gtx/number_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00117.html">./gtx/optimum_pow.hpp</a>&quot;</span></div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00118.html">./gtx/orthonormalize.hpp</a>&quot;</span></div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00121.html">./gtx/perpendicular.hpp</a>&quot;</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00122.html">./gtx/polar_coordinates.hpp</a>&quot;</span></div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00123.html">./gtx/projection.hpp</a>&quot;</span></div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00126.html">./gtx/quaternion.hpp</a>&quot;</span></div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00139.html">./gtx/raw_data.hpp</a>&quot;</span></div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00142.html">./gtx/rotate_vector.hpp</a>&quot;</span></div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00154.html">./gtx/spline.hpp</a>&quot;</span></div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00155.html">./gtx/std_based_type.hpp</a>&quot;</span></div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;<span class="preprocessor">#if !(GLM_COMPILER &amp; GLM_COMPILER_CUDA)</span></div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;<span class="preprocessor">#       include &quot;<a class="code" href="a00156.html">./gtx/string_cast.hpp</a>&quot;</span></div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00158.html">./gtx/transform.hpp</a>&quot;</span></div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00159.html">./gtx/transform2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00187.html">./gtx/vec_swizzle.hpp</a>&quot;</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00188.html">./gtx/vector_angle.hpp</a>&quot;</span></div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00223.html">./gtx/vector_query.hpp</a>&quot;</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00235.html">./gtx/wrap.hpp</a>&quot;</span></div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;<span class="preprocessor">#if GLM_HAS_TEMPLATE_ALIASES</span></div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;<span class="preprocessor">#       include &quot;<a class="code" href="a00148.html">./gtx/scalar_multiplication.hpp</a>&quot;</span></div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;<span class="preprocessor">#if GLM_HAS_RANGE_FOR</span></div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;<span class="preprocessor">#       include &quot;<a class="code" href="a00138.html">./gtx/range.hpp</a>&quot;</span></div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="preprocessor">#endif//GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="ttc" id="a00024_html"><div class="ttname"><a href="a00024.html">epsilon.hpp</a></div><div class="ttdoc">GLM_GTC_epsilon </div></div>
<div class="ttc" id="a00224_html"><div class="ttname"><a href="a00224.html">vector_relational.hpp</a></div><div class="ttdoc">GLM_EXT_vector_relational </div></div>
<div class="ttc" id="a00022_html"><div class="ttname"><a href="a00022.html">dual_quaternion.hpp</a></div><div class="ttdoc">GLM_GTX_dual_quaternion </div></div>
<div class="ttc" id="a00122_html"><div class="ttname"><a href="a00122.html">polar_coordinates.hpp</a></div><div class="ttdoc">GLM_GTX_polar_coordinates </div></div>
<div class="ttc" id="a00010_html"><div class="ttname"><a href="a00010.html">closest_point.hpp</a></div><div class="ttdoc">GLM_GTX_closest_point </div></div>
<div class="ttc" id="a00210_html"><div class="ttname"><a href="a00210.html">vector_float3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00093_html"><div class="ttname"><a href="a00093.html">matrix_float3x4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00068_html"><div class="ttname"><a href="a00068.html">matrix_double2x4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00039_html"><div class="ttname"><a href="a00039.html">handed_coordinate_space.hpp</a></div><div class="ttdoc">GLM_GTX_handed_coordinate_space </div></div>
<div class="ttc" id="a00193_html"><div class="ttname"><a href="a00193.html">vector_bool3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00139_html"><div class="ttname"><a href="a00139.html">raw_data.hpp</a></div><div class="ttdoc">GLM_GTX_raw_data </div></div>
<div class="ttc" id="a00091_html"><div class="ttname"><a href="a00091.html">matrix_float3x3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00191_html"><div class="ttname"><a href="a00191.html">vector_bool2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00156_html"><div class="ttname"><a href="a00156.html">string_cast.hpp</a></div><div class="ttdoc">GLM_GTX_string_cast </div></div>
<div class="ttc" id="a00227_html"><div class="ttname"><a href="a00227.html">vector_uint1_precision.hpp</a></div><div class="ttdoc">GLM_EXT_vector_uint1_precision </div></div>
<div class="ttc" id="a00044_html"><div class="ttname"><a href="a00044.html">intersect.hpp</a></div><div class="ttdoc">GLM_GTX_intersect </div></div>
<div class="ttc" id="a00215_html"><div class="ttname"><a href="a00215.html">vector_int1_precision.hpp</a></div><div class="ttdoc">GLM_EXT_vector_int1_precision </div></div>
<div class="ttc" id="a00115_html"><div class="ttname"><a href="a00115.html">normalize_dot.hpp</a></div><div class="ttdoc">GLM_GTX_normalize_dot </div></div>
<div class="ttc" id="a00042_html"><div class="ttname"><a href="a00042.html">integer.hpp</a></div><div class="ttdoc">GLM_GTX_integer </div></div>
<div class="ttc" id="a00142_html"><div class="ttname"><a href="a00142.html">rotate_vector.hpp</a></div><div class="ttdoc">GLM_GTX_rotate_vector </div></div>
<div class="ttc" id="a00203_html"><div class="ttname"><a href="a00203.html">vector_double3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00103_html"><div class="ttname"><a href="a00103.html">matrix_major_storage.hpp</a></div><div class="ttdoc">GLM_GTX_matrix_major_storage </div></div>
<div class="ttc" id="a00230_html"><div class="ttname"><a href="a00230.html">vector_uint3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00201_html"><div class="ttname"><a href="a00201.html">vector_double2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00088_html"><div class="ttname"><a href="a00088.html">matrix_float3x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00101_html"><div class="ttname"><a href="a00101.html">matrix_interpolation.hpp</a></div><div class="ttdoc">GLM_GTX_matrix_interpolation </div></div>
<div class="ttc" id="a00188_html"><div class="ttname"><a href="a00188.html">vector_angle.hpp</a></div><div class="ttdoc">GLM_GTX_vector_angle </div></div>
<div class="ttc" id="a00159_html"><div class="ttname"><a href="a00159.html">transform2.hpp</a></div><div class="ttdoc">GLM_GTX_transform2 </div></div>
<div class="ttc" id="a00076_html"><div class="ttname"><a href="a00076.html">matrix_double4x2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00064_html"><div class="ttname"><a href="a00064.html">matrix_double2x2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00235_html"><div class="ttname"><a href="a00235.html">wrap.hpp</a></div><div class="ttdoc">GLM_GTX_wrap </div></div>
<div class="ttc" id="a00223_html"><div class="ttname"><a href="a00223.html">vector_query.hpp</a></div><div class="ttdoc">GLM_GTX_vector_query </div></div>
<div class="ttc" id="a00123_html"><div class="ttname"><a href="a00123.html">projection.hpp</a></div><div class="ttdoc">GLM_GTX_projection </div></div>
<div class="ttc" id="a00021_html"><div class="ttname"><a href="a00021.html">constants.hpp</a></div><div class="ttdoc">GLM_GTC_constants </div></div>
<div class="ttc" id="a00221_html"><div class="ttname"><a href="a00221.html">vector_int4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00121_html"><div class="ttname"><a href="a00121.html">perpendicular.hpp</a></div><div class="ttdoc">GLM_GTX_perpendicular </div></div>
<div class="ttc" id="a00079_html"><div class="ttname"><a href="a00079.html">matrix_double4x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00096_html"><div class="ttname"><a href="a00096.html">matrix_float4x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00067_html"><div class="ttname"><a href="a00067.html">matrix_double2x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00196_html"><div class="ttname"><a href="a00196.html">vector_bool4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00084_html"><div class="ttname"><a href="a00084.html">matrix_float2x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00155_html"><div class="ttname"><a href="a00155.html">std_based_type.hpp</a></div><div class="ttdoc">GLM_GTX_std_based_type </div></div>
<div class="ttc" id="a00082_html"><div class="ttname"><a href="a00082.html">matrix_float2x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00018_html"><div class="ttname"><a href="a00018.html">component_wise.hpp</a></div><div class="ttdoc">GLM_GTX_component_wise </div></div>
<div class="ttc" id="a00182_html"><div class="ttname"><a href="a00182.html">ulp.hpp</a></div><div class="ttdoc">GLM_GTC_ulp </div></div>
<div class="ttc" id="a00143_html"><div class="ttname"><a href="a00143.html">round.hpp</a></div><div class="ttdoc">GLM_GTC_round </div></div>
<div class="ttc" id="a00218_html"><div class="ttname"><a href="a00218.html">vector_int3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00118_html"><div class="ttname"><a href="a00118.html">orthonormalize.hpp</a></div><div class="ttdoc">GLM_GTX_orthonormalize </div></div>
<div class="ttc" id="a00070_html"><div class="ttname"><a href="a00070.html">matrix_double3x2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00041_html"><div class="ttname"><a href="a00041.html">integer.hpp</a></div><div class="ttdoc">GLM_GTC_integer </div></div>
<div class="ttc" id="a00206_html"><div class="ttname"><a href="a00206.html">vector_float1.hpp</a></div><div class="ttdoc">GLM_EXT_vector_float1 </div></div>
<div class="ttc" id="a00099_html"><div class="ttname"><a href="a00099.html">matrix_float4x4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00106_html"><div class="ttname"><a href="a00106.html">matrix_query.hpp</a></div><div class="ttdoc">GLM_GTX_matrix_query </div></div>
<div class="ttc" id="a00199_html"><div class="ttname"><a href="a00199.html">vector_double1_precision.hpp</a></div><div class="ttdoc">GLM_EXT_vector_double1_precision </div></div>
<div class="ttc" id="a00087_html"><div class="ttname"><a href="a00087.html">matrix_float2x4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00187_html"><div class="ttname"><a href="a00187.html">vec_swizzle.hpp</a></div><div class="ttdoc">GLM_GTX_vec_swizzle </div></div>
<div class="ttc" id="a00075_html"><div class="ttname"><a href="a00075.html">matrix_double4x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00175_html"><div class="ttname"><a href="a00175.html">type_ptr.hpp</a></div><div class="ttdoc">GLM_GTC_type_ptr </div></div>
<div class="ttc" id="a00063_html"><div class="ttname"><a href="a00063.html">matrix_double2x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00038_html"><div class="ttname"><a href="a00038.html">gradient_paint.hpp</a></div><div class="ttdoc">GLM_GTX_gradient_paint </div></div>
<div class="ttc" id="a00009_html"><div class="ttname"><a href="a00009.html">bitfield.hpp</a></div><div class="ttdoc">GLM_GTC_bitfield </div></div>
<div class="ttc" id="a00138_html"><div class="ttname"><a href="a00138.html">range.hpp</a></div><div class="ttdoc">GLM_GTX_range </div></div>
<div class="ttc" id="a00209_html"><div class="ttname"><a href="a00209.html">vector_float2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00090_html"><div class="ttname"><a href="a00090.html">matrix_float3x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00109_html"><div class="ttname"><a href="a00109.html">matrix_transform.hpp</a></div><div class="ttdoc">GLM_GTC_matrix_transform </div></div>
<div class="ttc" id="a00061_html"><div class="ttname"><a href="a00061.html">matrix_cross_product.hpp</a></div><div class="ttdoc">GLM_GTX_matrix_cross_product </div></div>
<div class="ttc" id="a00190_html"><div class="ttname"><a href="a00190.html">vector_bool1_precision.hpp</a></div><div class="ttdoc">GLM_EXT_vector_bool1_precision </div></div>
<div class="ttc" id="a00161_html"><div class="ttname"><a href="a00161.html">type_aligned.hpp</a></div><div class="ttdoc">GLM_GTC_type_aligned </div></div>
<div class="ttc" id="a00226_html"><div class="ttname"><a href="a00226.html">vector_uint1.hpp</a></div><div class="ttdoc">GLM_EXT_vector_uint1 </div></div>
<div class="ttc" id="a00126_html"><div class="ttname"><a href="a00126.html">quaternion.hpp</a></div><div class="ttdoc">GLM_GTX_quaternion </div></div>
<div class="ttc" id="a00014_html"><div class="ttname"><a href="a00014.html">color_space_YCoCg.hpp</a></div><div class="ttdoc">GLM_GTX_color_space_YCoCg </div></div>
<div class="ttc" id="a00214_html"><div class="ttname"><a href="a00214.html">vector_int1.hpp</a></div><div class="ttdoc">GLM_EXT_vector_int1 </div></div>
<div class="ttc" id="a00114_html"><div class="ttname"><a href="a00114.html">normal.hpp</a></div><div class="ttdoc">GLM_GTX_normal </div></div>
<div class="ttc" id="a00012_html"><div class="ttname"><a href="a00012.html">color_space.hpp</a></div><div class="ttdoc">GLM_GTC_color_space </div></div>
<div class="ttc" id="a00212_html"><div class="ttname"><a href="a00212.html">vector_float4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00112_html"><div class="ttname"><a href="a00112.html">noise.hpp</a></div><div class="ttdoc">GLM_GTC_noise </div></div>
<div class="ttc" id="a00195_html"><div class="ttname"><a href="a00195.html">vector_bool4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00200_html"><div class="ttname"><a href="a00200.html">vector_double2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00083_html"><div class="ttname"><a href="a00083.html">matrix_float2x2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00100_html"><div class="ttname"><a href="a00100.html">matrix_integer.hpp</a></div><div class="ttdoc">GLM_GTC_matrix_integer </div></div>
<div class="ttc" id="a00058_html"><div class="ttname"><a href="a00058.html">matrix_access.hpp</a></div><div class="ttdoc">GLM_GTC_matrix_access </div></div>
<div class="ttc" id="a00029_html"><div class="ttname"><a href="a00029.html">extended_min_max.hpp</a></div><div class="ttdoc">GLM_GTX_extented_min_max </div></div>
<div class="ttc" id="a00183_html"><div class="ttname"><a href="a00183.html">vec1.hpp</a></div><div class="ttdoc">GLM_GTC_vec1 </div></div>
<div class="ttc" id="a00158_html"><div class="ttname"><a href="a00158.html">transform.hpp</a></div><div class="ttdoc">GLM_GTX_transform </div></div>
<div class="ttc" id="a00229_html"><div class="ttname"><a href="a00229.html">vector_uint2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00129_html"><div class="ttname"><a href="a00129.html">quaternion_double_precision.hpp</a></div><div class="ttdoc">GLM_EXT_quaternion_double_precision </div></div>
<div class="ttc" id="a00046_html"><div class="ttname"><a href="a00046.html">log_base.hpp</a></div><div class="ttdoc">GLM_GTX_log_base </div></div>
<div class="ttc" id="a00017_html"><div class="ttname"><a href="a00017.html">compatibility.hpp</a></div><div class="ttdoc">GLM_GTX_compatibility </div></div>
<div class="ttc" id="a00146_html"><div class="ttname"><a href="a00146.html">scalar_int_sized.hpp</a></div><div class="ttdoc">GLM_EXT_scalar_int_sized </div></div>
<div class="ttc" id="a00217_html"><div class="ttname"><a href="a00217.html">vector_int2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00117_html"><div class="ttname"><a href="a00117.html">optimum_pow.hpp</a></div><div class="ttdoc">GLM_GTX_optimum_pow </div></div>
<div class="ttc" id="a00034_html"><div class="ttname"><a href="a00034.html">functions.hpp</a></div><div class="ttdoc">GLM_GTX_functions </div></div>
<div class="ttc" id="a00134_html"><div class="ttname"><a href="a00134.html">quaternion_relational.hpp</a></div><div class="ttdoc">GLM_EXT_quaternion_relational </div></div>
<div class="ttc" id="a00205_html"><div class="ttname"><a href="a00205.html">vector_double4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00032_html"><div class="ttname"><a href="a00032.html">fast_square_root.hpp</a></div><div class="ttdoc">GLM_GTX_fast_square_root </div></div>
<div class="ttc" id="a00232_html"><div class="ttname"><a href="a00232.html">vector_uint4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00132_html"><div class="ttname"><a href="a00132.html">quaternion_float_precision.hpp</a></div><div class="ttdoc">GLM_EXT_quaternion_float_precision </div></div>
<div class="ttc" id="a00220_html"><div class="ttname"><a href="a00220.html">vector_int4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00078_html"><div class="ttname"><a href="a00078.html">matrix_double4x3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00149_html"><div class="ttname"><a href="a00149.html">scalar_relational.hpp</a></div><div class="ttdoc">GLM_EXT_scalar_relational </div></div>
<div class="ttc" id="a00066_html"><div class="ttname"><a href="a00066.html">matrix_double2x3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00037_html"><div class="ttname"><a href="a00037.html">glm.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00137_html"><div class="ttname"><a href="a00137.html">random.hpp</a></div><div class="ttdoc">GLM_GTC_random </div></div>
<div class="ttc" id="a00025_html"><div class="ttname"><a href="a00025.html">euler_angles.hpp</a></div><div class="ttdoc">GLM_GTX_euler_angles </div></div>
<div class="ttc" id="a00154_html"><div class="ttname"><a href="a00154.html">spline.hpp</a></div><div class="ttdoc">GLM_GTX_spline </div></div>
<div class="ttc" id="a00125_html"><div class="ttname"><a href="a00125.html">quaternion.hpp</a></div><div class="ttdoc">GLM_GTC_quaternion </div></div>
<div class="ttc" id="a00013_html"><div class="ttname"><a href="a00013.html">color_space.hpp</a></div><div class="ttdoc">GLM_GTX_color_space </div></div>
<div class="ttc" id="a00213_html"><div class="ttname"><a href="a00213.html">vector_float4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00113_html"><div class="ttname"><a href="a00113.html">norm.hpp</a></div><div class="ttdoc">GLM_GTX_norm </div></div>
<div class="ttc" id="a00011_html"><div class="ttname"><a href="a00011.html">color_encoding.hpp</a></div><div class="ttdoc">GLM_GTX_color_encoding </div></div>
<div class="ttc" id="a00140_html"><div class="ttname"><a href="a00140.html">reciprocal.hpp</a></div><div class="ttdoc">GLM_GTC_reciprocal </div></div>
<div class="ttc" id="a00211_html"><div class="ttname"><a href="a00211.html">vector_float3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00098_html"><div class="ttname"><a href="a00098.html">matrix_float4x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00111_html"><div class="ttname"><a href="a00111.html">mixed_product.hpp</a></div><div class="ttdoc">GLM_GTX_mixed_producte </div></div>
<div class="ttc" id="a00069_html"><div class="ttname"><a href="a00069.html">matrix_double3x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00198_html"><div class="ttname"><a href="a00198.html">vector_double1.hpp</a></div><div class="ttdoc">GLM_EXT_vector_double1 </div></div>
<div class="ttc" id="a00086_html"><div class="ttname"><a href="a00086.html">matrix_float2x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00074_html"><div class="ttname"><a href="a00074.html">matrix_double3x4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00174_html"><div class="ttname"><a href="a00174.html">type_precision.hpp</a></div><div class="ttdoc">GLM_GTC_type_precision </div></div>
<div class="ttc" id="a00145_html"><div class="ttname"><a href="a00145.html">scalar_constants.hpp</a></div><div class="ttdoc">GLM_EXT_scalar_constants </div></div>
<div class="ttc" id="a00072_html"><div class="ttname"><a href="a00072.html">matrix_double3x3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00033_html"><div class="ttname"><a href="a00033.html">fast_trigonometry.hpp</a></div><div class="ttdoc">GLM_GTX_fast_trigonometry </div></div>
<div class="ttc" id="a00008_html"><div class="ttname"><a href="a00008.html">bit.hpp</a></div><div class="ttdoc">GLM_GTX_bit </div></div>
<div class="ttc" id="a00233_html"><div class="ttname"><a href="a00233.html">vector_uint4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00133_html"><div class="ttname"><a href="a00133.html">quaternion_geometric.hpp</a></div><div class="ttdoc">GLM_EXT_quaternion_geometric </div></div>
<div class="ttc" id="a00208_html"><div class="ttname"><a href="a00208.html">vector_float2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00031_html"><div class="ttname"><a href="a00031.html">fast_exponential.hpp</a></div><div class="ttdoc">GLM_GTX_fast_exponential </div></div>
<div class="ttc" id="a00231_html"><div class="ttname"><a href="a00231.html">vector_uint3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00131_html"><div class="ttname"><a href="a00131.html">quaternion_float.hpp</a></div><div class="ttdoc">GLM_EXT_quaternion_float </div></div>
<div class="ttc" id="a00089_html"><div class="ttname"><a href="a00089.html">matrix_float3x2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00189_html"><div class="ttname"><a href="a00189.html">vector_bool1.hpp</a></div><div class="ttdoc">GLM_EXT_vector_bool1 </div></div>
<div class="ttc" id="a00077_html"><div class="ttname"><a href="a00077.html">matrix_double4x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00094_html"><div class="ttname"><a href="a00094.html">matrix_float4x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00065_html"><div class="ttname"><a href="a00065.html">matrix_double2x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00194_html"><div class="ttname"><a href="a00194.html">vector_bool3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00092_html"><div class="ttname"><a href="a00092.html">matrix_float3x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00028_html"><div class="ttname"><a href="a00028.html">extend.hpp</a></div><div class="ttdoc">GLM_GTX_extend </div></div>
<div class="ttc" id="a00192_html"><div class="ttname"><a href="a00192.html">vector_bool2_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00228_html"><div class="ttname"><a href="a00228.html">vector_uint2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00128_html"><div class="ttname"><a href="a00128.html">quaternion_double.hpp</a></div><div class="ttdoc">GLM_EXT_quaternion_double </div></div>
<div class="ttc" id="a00080_html"><div class="ttname"><a href="a00080.html">matrix_double4x4_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00216_html"><div class="ttname"><a href="a00216.html">vector_int2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00116_html"><div class="ttname"><a href="a00116.html">number_precision.hpp</a></div><div class="ttdoc">GLM_GTX_number_precision </div></div>
<div class="ttc" id="a00204_html"><div class="ttname"><a href="a00204.html">vector_double4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00097_html"><div class="ttname"><a href="a00097.html">matrix_float4x3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00104_html"><div class="ttname"><a href="a00104.html">matrix_operation.hpp</a></div><div class="ttdoc">GLM_GTX_matrix_operation </div></div>
<div class="ttc" id="a00202_html"><div class="ttname"><a href="a00202.html">vector_double3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00085_html"><div class="ttname"><a href="a00085.html">matrix_float2x3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00102_html"><div class="ttname"><a href="a00102.html">matrix_inverse.hpp</a></div><div class="ttdoc">GLM_GTC_matrix_inverse </div></div>
<div class="ttc" id="a00073_html"><div class="ttname"><a href="a00073.html">matrix_double3x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00148_html"><div class="ttname"><a href="a00148.html">scalar_multiplication.hpp</a></div><div class="ttdoc">Experimental extensions </div></div>
<div class="ttc" id="a00219_html"><div class="ttname"><a href="a00219.html">vector_int3_precision.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00119_html"><div class="ttname"><a href="a00119.html">packing.hpp</a></div><div class="ttdoc">GLM_GTC_packing </div></div>
<div class="ttc" id="a00071_html"><div class="ttname"><a href="a00071.html">matrix_double3x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00007_html"><div class="ttname"><a href="a00007.html">associated_min_max.hpp</a></div><div class="ttdoc">GLM_GTX_associated_min_max </div></div>
<div class="ttc" id="a00207_html"><div class="ttname"><a href="a00207.html">vector_float1_precision.hpp</a></div><div class="ttdoc">GLM_EXT_vector_float1_precision </div></div>
<div class="ttc" id="a00107_html"><div class="ttname"><a href="a00107.html">matrix_relational.hpp</a></div><div class="ttdoc">GLM_EXT_matrix_relational </div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
