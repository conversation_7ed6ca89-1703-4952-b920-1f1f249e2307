<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: norm.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">norm.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00343.html">GLM_GTX_norm</a>  
<a href="#details">More...</a></p>

<p><a href="a00113_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga85660f1b79f66c09c7b5a6f80e68c89f">distance2</a> (vec&lt; L, T, Q &gt; const &amp;p0, vec&lt; L, T, Q &gt; const &amp;p1)</td></tr>
<tr class="memdesc:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared distance between p0 and p1, i.e., length2(p0 - p1).  <a href="a00343.html#ga85660f1b79f66c09c7b5a6f80e68c89f">More...</a><br /></td></tr>
<tr class="separator:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2fc0b2aa967bebfd6a244700bff6997"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae2fc0b2aa967bebfd6a244700bff6997"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gae2fc0b2aa967bebfd6a244700bff6997">l1Norm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gae2fc0b2aa967bebfd6a244700bff6997"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L1 norm between x and y.  <a href="a00343.html#gae2fc0b2aa967bebfd6a244700bff6997">More...</a><br /></td></tr>
<tr class="separator:gae2fc0b2aa967bebfd6a244700bff6997"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">l1Norm</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L1 norm of v.  <a href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">More...</a><br /></td></tr>
<tr class="separator:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41340b2ef40a9307ab0f137181565168"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga41340b2ef40a9307ab0f137181565168"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga41340b2ef40a9307ab0f137181565168">l2Norm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga41340b2ef40a9307ab0f137181565168"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L2 norm between x and y.  <a href="a00343.html#ga41340b2ef40a9307ab0f137181565168">More...</a><br /></td></tr>
<tr class="separator:ga41340b2ef40a9307ab0f137181565168"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae288bde8f0e41fb4ed62e65137b18cba"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae288bde8f0e41fb4ed62e65137b18cba"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">l2Norm</a> (vec&lt; 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gae288bde8f0e41fb4ed62e65137b18cba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L2 norm of v.  <a href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">More...</a><br /></td></tr>
<tr class="separator:gae288bde8f0e41fb4ed62e65137b18cba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d1789651050adb7024917984b41c3de"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8d1789651050adb7024917984b41c3de"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga8d1789651050adb7024917984b41c3de">length2</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga8d1789651050adb7024917984b41c3de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared length of x.  <a href="a00343.html#ga8d1789651050adb7024917984b41c3de">More...</a><br /></td></tr>
<tr class="separator:ga8d1789651050adb7024917984b41c3de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gad58a8231fc32e38104a9e1c4d3c0cb64">lMaxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the LMax norm between x and y.  <a href="a00343.html#gad58a8231fc32e38104a9e1c4d3c0cb64">More...</a><br /></td></tr>
<tr class="separator:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6968a324837a8e899396d44de23d5aae"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6968a324837a8e899396d44de23d5aae"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga6968a324837a8e899396d44de23d5aae">lMaxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga6968a324837a8e899396d44de23d5aae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the LMax norm of v.  <a href="a00343.html#ga6968a324837a8e899396d44de23d5aae">More...</a><br /></td></tr>
<tr class="separator:ga6968a324837a8e899396d44de23d5aae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacad23d30497eb16f67709f2375d1f66a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacad23d30497eb16f67709f2375d1f66a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gacad23d30497eb16f67709f2375d1f66a">lxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y, unsigned int Depth)</td></tr>
<tr class="memdesc:gacad23d30497eb16f67709f2375d1f66a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L norm between x and y.  <a href="a00343.html#gacad23d30497eb16f67709f2375d1f66a">More...</a><br /></td></tr>
<tr class="separator:gacad23d30497eb16f67709f2375d1f66a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac61b6d81d796d6eb4d4183396a19ab91"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac61b6d81d796d6eb4d4183396a19ab91"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">lxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x, unsigned int Depth)</td></tr>
<tr class="memdesc:gac61b6d81d796d6eb4d4183396a19ab91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L norm of v.  <a href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">More...</a><br /></td></tr>
<tr class="separator:gac61b6d81d796d6eb4d4183396a19ab91"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00343.html">GLM_GTX_norm</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> (dependence) </dd>
<dd>
<a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00113_source.html">norm.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
