<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: quaternion.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtc/quaternion.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00125.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../gtc/matrix_transform.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../ext/vector_relational.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_common.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_float.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_float_precision.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_double.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_double_precision.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_relational.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_geometric.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_trigonometric.hpp&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_transform.hpp&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat4x4.hpp&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;../detail/type_vec3.hpp&quot;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;../detail/type_vec4.hpp&quot;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_quaternion extension included&quot;)</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;{</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00299.html#gaf4dd967dead22dd932fc7460ceecb03f">eulerAngles</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00299.html#ga0cc5ad970d0b00829b139fe0fe5a1e13">roll</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00299.html#ga7603e81477b46ddb448896909bc04928">pitch</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00299.html#ga8da38cdfdc452dafa660c2f46506bad5">yaw</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00299.html#ga333ab70047fbe4132406100c292dbc89">mat3_cast</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">mat4_cast</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00299.html#ga4524810f07f72e8c7bdc7764fa11cb58">quat_cast</a>(mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00299.html#ga4524810f07f72e8c7bdc7764fa11cb58">quat_cast</a>(mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00299.html#gad091a2d22c8acfebfa92bcfca1dfe9c4">lessThan</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00299.html#gac00012eea281800d2403f4ea8443134d">lessThanEqual</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00299.html#ga8f7fa76e06c417b757ddfd438f3f677b">greaterThan</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00299.html#ga388cbeba987dae7b5937f742efa49a5a">greaterThanEqual</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00299.html#gabe7fc5ec5feb41ab234d5d2b6254697f">quatLookAt</a>(</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; direction,</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00299.html#gaf6529ac8c04a57fcc35865b5c9437cc8">quatLookAtRH</a>(</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; direction,</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00299.html#ga2da350c73411be3bb19441b226b81a74">quatLookAtLH</a>(</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; direction,</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; up);</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="preprocessor">#include &quot;quaternion.inl&quot;</span></div>
<div class="ttc" id="a00299_html_ga1113212d9bdefc2e31ad40e5bbb506f3"><div class="ttname"><a href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">glm::mat4_cast</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; mat4_cast(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 4 * 4 matrix. </div></div>
<div class="ttc" id="a00299_html_ga8f7fa76e06c417b757ddfd438f3f677b"><div class="ttname"><a href="a00299.html#ga8f7fa76e06c417b757ddfd438f3f677b">glm::greaterThan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; greaterThan(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x > y. </div></div>
<div class="ttc" id="a00299_html_ga388cbeba987dae7b5937f742efa49a5a"><div class="ttname"><a href="a00299.html#ga388cbeba987dae7b5937f742efa49a5a">glm::greaterThanEqual</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; greaterThanEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x >= y. </div></div>
<div class="ttc" id="a00299_html_gac00012eea281800d2403f4ea8443134d"><div class="ttname"><a href="a00299.html#gac00012eea281800d2403f4ea8443134d">glm::lessThanEqual</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; lessThanEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x <= y. </div></div>
<div class="ttc" id="a00299_html_ga0cc5ad970d0b00829b139fe0fe5a1e13"><div class="ttname"><a href="a00299.html#ga0cc5ad970d0b00829b139fe0fe5a1e13">glm::roll</a></div><div class="ttdeci">GLM_FUNC_DECL T roll(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns roll value of euler angles expressed in radians. </div></div>
<div class="ttc" id="a00299_html_gabe7fc5ec5feb41ab234d5d2b6254697f"><div class="ttname"><a href="a00299.html#gabe7fc5ec5feb41ab234d5d2b6254697f">glm::quatLookAt</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; quatLookAt(vec&lt; 3, T, Q &gt; const &amp;direction, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a look at quaternion based on the default handedness. </div></div>
<div class="ttc" id="a00299_html_ga4524810f07f72e8c7bdc7764fa11cb58"><div class="ttname"><a href="a00299.html#ga4524810f07f72e8c7bdc7764fa11cb58">glm::quat_cast</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; quat_cast(mat&lt; 4, 4, T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a pure rotation 4 * 4 matrix to a quaternion. </div></div>
<div class="ttc" id="a00299_html_ga333ab70047fbe4132406100c292dbc89"><div class="ttname"><a href="a00299.html#ga333ab70047fbe4132406100c292dbc89">glm::mat3_cast</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt; mat3_cast(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 3 * 3 matrix. </div></div>
<div class="ttc" id="a00299_html_gaf4dd967dead22dd932fc7460ceecb03f"><div class="ttname"><a href="a00299.html#gaf4dd967dead22dd932fc7460ceecb03f">glm::eulerAngles</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; eulerAngles(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns euler angles, pitch as x, yaw as y, roll as z. </div></div>
<div class="ttc" id="a00299_html_gad091a2d22c8acfebfa92bcfca1dfe9c4"><div class="ttname"><a href="a00299.html#gad091a2d22c8acfebfa92bcfca1dfe9c4">glm::lessThan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; lessThan(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison result of x < y. </div></div>
<div class="ttc" id="a00299_html_ga8da38cdfdc452dafa660c2f46506bad5"><div class="ttname"><a href="a00299.html#ga8da38cdfdc452dafa660c2f46506bad5">glm::yaw</a></div><div class="ttdeci">GLM_FUNC_DECL T yaw(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns yaw value of euler angles expressed in radians. </div></div>
<div class="ttc" id="a00299_html_ga2da350c73411be3bb19441b226b81a74"><div class="ttname"><a href="a00299.html#ga2da350c73411be3bb19441b226b81a74">glm::quatLookAtLH</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; quatLookAtLH(vec&lt; 3, T, Q &gt; const &amp;direction, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a left-handed look at quaternion. </div></div>
<div class="ttc" id="a00299_html_gaf6529ac8c04a57fcc35865b5c9437cc8"><div class="ttname"><a href="a00299.html#gaf6529ac8c04a57fcc35865b5c9437cc8">glm::quatLookAtRH</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; quatLookAtRH(vec&lt; 3, T, Q &gt; const &amp;direction, vec&lt; 3, T, Q &gt; const &amp;up)</div><div class="ttdoc">Build a right-handed look at quaternion. </div></div>
<div class="ttc" id="a00299_html_ga7603e81477b46ddb448896909bc04928"><div class="ttname"><a href="a00299.html#ga7603e81477b46ddb448896909bc04928">glm::pitch</a></div><div class="ttdeci">GLM_FUNC_DECL T pitch(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns pitch value of euler angles expressed in radians. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
