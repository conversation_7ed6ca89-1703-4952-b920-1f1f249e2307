<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: exponential.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">exponential.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00026.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00178.html">detail/type_vec1.hpp</a>&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00179.html">detail/type_vec2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00180.html">detail/type_vec3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00181.html">detail/type_vec4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;{</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#ga2254981952d4f333b900a6bf5167a6c4">pow</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; base, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; exponent);</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">exp</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#ga918c9f3fd086ce20e6760c903bd30fa9">log</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#gaff17ace6b579a03bf223ed4d1ed2cd16">exp2</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#ga82831c7d9cca777cebedfe03a19c8d75">log2</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#gaa83e5f1648b7ccdf33b87c07c76cb77c">sqrt</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00242.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a">inversesqrt</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="preprocessor">#include &quot;detail/func_exponential.inl&quot;</span></div>
<div class="ttc" id="a00179_html"><div class="ttname"><a href="a00179.html">type_vec2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00242_html_gaa83e5f1648b7ccdf33b87c07c76cb77c"><div class="ttname"><a href="a00242.html#gaa83e5f1648b7ccdf33b87c07c76cb77c">glm::sqrt</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; sqrt(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the positive square root of v. </div></div>
<div class="ttc" id="a00242_html_gaff17ace6b579a03bf223ed4d1ed2cd16"><div class="ttname"><a href="a00242.html#gaff17ace6b579a03bf223ed4d1ed2cd16">glm::exp2</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; exp2(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns 2 raised to the v power. </div></div>
<div class="ttc" id="a00242_html_ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><div class="ttname"><a href="a00242.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a">glm::inversesqrt</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; inversesqrt(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the reciprocal of the positive square root of v. </div></div>
<div class="ttc" id="a00181_html"><div class="ttname"><a href="a00181.html">type_vec4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00178_html"><div class="ttname"><a href="a00178.html">type_vec1.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00242_html_ga2254981952d4f333b900a6bf5167a6c4"><div class="ttname"><a href="a00242.html#ga2254981952d4f333b900a6bf5167a6c4">glm::pow</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; pow(vec&lt; L, T, Q &gt; const &amp;base, vec&lt; L, T, Q &gt; const &amp;exponent)</div><div class="ttdoc">Returns &#39;base&#39; raised to the power &#39;exponent&#39;. </div></div>
<div class="ttc" id="a00242_html_ga071566cadc7505455e611f2a0353f4d4"><div class="ttname"><a href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">glm::exp</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; exp(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the natural exponentiation of x, i.e., e^x. </div></div>
<div class="ttc" id="a00242_html_ga918c9f3fd086ce20e6760c903bd30fa9"><div class="ttname"><a href="a00242.html#ga918c9f3fd086ce20e6760c903bd30fa9">glm::log</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; log(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the natural logarithm of v, i.e., returns the value y which satisfies the equation x = e^y...</div></div>
<div class="ttc" id="a00180_html"><div class="ttname"><a href="a00180.html">type_vec3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00242_html_ga82831c7d9cca777cebedfe03a19c8d75"><div class="ttname"><a href="a00242.html#ga82831c7d9cca777cebedfe03a19c8d75">glm::log2</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; log2(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the base 2 log of x, i.e., returns the value y, which satisfies the equation x = 2 ^ y...</div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
