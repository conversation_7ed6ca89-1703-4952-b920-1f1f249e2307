<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: intersect.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">intersect.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00331.html">GLM_GTX_intersect</a>  
<a href="#details">More...</a></p>

<p><a href="a00044_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#ga9c68139f3d8a4f3d7fe45f9dbc0de5b7">intersectLineSphere</a> (genType const &amp;point0, genType const &amp;point1, genType const &amp;sphereCenter, typename genType::value_type sphereRadius, genType &amp;intersectionPosition1, genType &amp;intersectionNormal1, genType &amp;intersectionPosition2=genType(), genType &amp;intersectionNormal2=genType())</td></tr>
<tr class="memdesc:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a line and a sphere.  <a href="a00331.html#ga9c68139f3d8a4f3d7fe45f9dbc0de5b7">More...</a><br /></td></tr>
<tr class="separator:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d29b9b3acb504d43986502f42740df4"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9d29b9b3acb504d43986502f42740df4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#ga9d29b9b3acb504d43986502f42740df4">intersectLineTriangle</a> (genType const &amp;orig, genType const &amp;dir, genType const &amp;vert0, genType const &amp;vert1, genType const &amp;vert2, genType &amp;position)</td></tr>
<tr class="memdesc:ga9d29b9b3acb504d43986502f42740df4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a line and a triangle.  <a href="a00331.html#ga9d29b9b3acb504d43986502f42740df4">More...</a><br /></td></tr>
<tr class="separator:ga9d29b9b3acb504d43986502f42740df4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3697a9700ea379739a667ea02573488"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gad3697a9700ea379739a667ea02573488"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#gad3697a9700ea379739a667ea02573488">intersectRayPlane</a> (genType const &amp;orig, genType const &amp;dir, genType const &amp;planeOrig, genType const &amp;planeNormal, typename genType::value_type &amp;intersectionDistance)</td></tr>
<tr class="memdesc:gad3697a9700ea379739a667ea02573488"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a ray and a plane.  <a href="a00331.html#gad3697a9700ea379739a667ea02573488">More...</a><br /></td></tr>
<tr class="separator:gad3697a9700ea379739a667ea02573488"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#gac88f8cd84c4bcb5b947d56acbbcfa56e">intersectRaySphere</a> (genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, typename genType::value_type const sphereRadiusSquared, typename genType::value_type &amp;intersectionDistance)</td></tr>
<tr class="memdesc:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection distance of a ray and a sphere.  <a href="a00331.html#gac88f8cd84c4bcb5b947d56acbbcfa56e">More...</a><br /></td></tr>
<tr class="separator:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad28c00515b823b579c608aafa1100c1d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gad28c00515b823b579c608aafa1100c1d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#gad28c00515b823b579c608aafa1100c1d">intersectRaySphere</a> (genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, const typename genType::value_type sphereRadius, genType &amp;intersectionPosition, genType &amp;intersectionNormal)</td></tr>
<tr class="memdesc:gad28c00515b823b579c608aafa1100c1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a ray and a sphere.  <a href="a00331.html#gad28c00515b823b579c608aafa1100c1d">More...</a><br /></td></tr>
<tr class="separator:gad28c00515b823b579c608aafa1100c1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65bf2c594482f04881c36bc761f9e946"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga65bf2c594482f04881c36bc761f9e946"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#ga65bf2c594482f04881c36bc761f9e946">intersectRayTriangle</a> (vec&lt; 3, T, Q &gt; const &amp;orig, vec&lt; 3, T, Q &gt; const &amp;dir, vec&lt; 3, T, Q &gt; const &amp;v0, vec&lt; 3, T, Q &gt; const &amp;v1, vec&lt; 3, T, Q &gt; const &amp;v2, vec&lt; 2, T, Q &gt; &amp;baryPosition, T &amp;distance)</td></tr>
<tr class="memdesc:ga65bf2c594482f04881c36bc761f9e946"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a ray and a triangle.  <a href="a00331.html#ga65bf2c594482f04881c36bc761f9e946">More...</a><br /></td></tr>
<tr class="separator:ga65bf2c594482f04881c36bc761f9e946"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00331.html">GLM_GTX_intersect</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00310.html" title="Include <glm/gtx/closest_point.hpp> to use the features of this extension. ">GLM_GTX_closest_point</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00044_source.html">intersect.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
