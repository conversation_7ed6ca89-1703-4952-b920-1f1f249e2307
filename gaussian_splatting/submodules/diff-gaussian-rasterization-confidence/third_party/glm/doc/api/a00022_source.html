<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: dual_quaternion.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">dual_quaternion.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00022.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../gtc/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_dual_quaternion is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_dual_quaternion extension included&quot;)</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;{</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q = defaultp&gt;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">struct </span>tdualquat</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        {</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                <span class="comment">// -- Implementation detail --</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                <span class="keyword">typedef</span> T value_type;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                <span class="keyword">typedef</span> qua&lt;T, Q&gt; part_type;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                <span class="comment">// -- Data --</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                qua&lt;T, Q&gt; real, dual;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                <span class="comment">// -- Component accesses --</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                <span class="keyword">typedef</span> length_t length_type;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                GLM_FUNC_DECL <span class="keyword">static</span> GLM_CONSTEXPR length_type <a class="code" href="a00254.html#gab703732449be6c7199369b3f9a91ed38">length</a>(){<span class="keywordflow">return</span> 2;}</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                GLM_FUNC_DECL part_type &amp; operator[](length_type i);</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                GLM_FUNC_DECL part_type <span class="keyword">const</span>&amp; operator[](length_type i) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;                <span class="comment">// -- Implicit basic constructors --</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR tdualquat() GLM_DEFAULT;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR tdualquat(tdualquat&lt;T, Q&gt; const&amp; d) GLM_DEFAULT;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                template&lt;qualifier P&gt;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR tdualquat(tdualquat&lt;T, P&gt; const&amp; d);</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                <span class="comment">// -- Explicit basic constructors --</span></div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR tdualquat(qua&lt;T, Q&gt; const&amp; real);</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR tdualquat(qua&lt;T, Q&gt; const&amp; <a class="code" href="a00356.html#ga1a32fceb71962e6160e8af295c91930a">orientation</a>, vec&lt;3, T, Q&gt; const&amp; translation);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR tdualquat(qua&lt;T, Q&gt; const&amp; real, qua&lt;T, Q&gt; const&amp; dual);</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                <span class="comment">// -- Conversion constructors --</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                template&lt;typename U, qualifier P&gt;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR GLM_EXPLICIT tdualquat(tdualquat&lt;U, P&gt; const&amp; q);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR tdualquat(mat&lt;2, 4, T, Q&gt; const&amp; holder_mat);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR tdualquat(mat&lt;3, 4, T, Q&gt; const&amp; aug_mat);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                <span class="comment">// -- Unary arithmetic operators --</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                GLM_FUNC_DECL tdualquat&lt;T, Q&gt; &amp; operator=(tdualquat&lt;T, Q&gt; const&amp; m) GLM_DEFAULT;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                GLM_FUNC_DECL tdualquat&lt;T, Q&gt; &amp; operator=(tdualquat&lt;U, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                GLM_FUNC_DECL tdualquat&lt;T, Q&gt; &amp; operator*=(U s);</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;                GLM_FUNC_DECL tdualquat&lt;T, Q&gt; &amp; operator/=(U s);</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        };</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        <span class="comment">// -- Unary bit operators --</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator+(tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator-(tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        <span class="comment">// -- Binary operators --</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator+(tdualquat&lt;T, Q&gt; const&amp; q, tdualquat&lt;T, Q&gt; const&amp; p);</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator*(tdualquat&lt;T, Q&gt; const&amp; q, tdualquat&lt;T, Q&gt; const&amp; p);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; operator*(tdualquat&lt;T, Q&gt; const&amp; q, vec&lt;3, T, Q&gt; const&amp; v);</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; operator*(vec&lt;3, T, Q&gt; const&amp; v, tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; operator*(tdualquat&lt;T, Q&gt; const&amp; q, vec&lt;4, T, Q&gt; const&amp; v);</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; operator*(vec&lt;4, T, Q&gt; const&amp; v, tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator*(tdualquat&lt;T, Q&gt; const&amp; q, T const&amp; s);</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator*(T const&amp; s, tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; operator/(tdualquat&lt;T, Q&gt; const&amp; q, T const&amp; s);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        <span class="comment">// -- Boolean operators --</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> operator==(tdualquat&lt;T, Q&gt; const&amp; q1, tdualquat&lt;T, Q&gt; const&amp; q2);</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> operator!=(tdualquat&lt;T, Q&gt; const&amp; q1, tdualquat&lt;T, Q&gt; const&amp; q2);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        template &lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; <a class="code" href="a00317.html#ga0b35c0e30df8a875dbaa751e0bd800e0">dual_quat_identity</a>();</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; <a class="code" href="a00317.html#ga299b8641509606b1958ffa104a162cfe">normalize</a>(tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; <a class="code" href="a00317.html#gace8380112d16d33f520839cb35a4d173">lerp</a>(tdualquat&lt;T, Q&gt; const&amp; x, tdualquat&lt;T, Q&gt; const&amp; y, T const&amp; a);</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; <a class="code" href="a00317.html#ga070f521a953f6461af4ab4cf8ccbf27e">inverse</a>(tdualquat&lt;T, Q&gt; const&amp; q);</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        GLM_FUNC_DECL mat&lt;2, 4, T, Q&gt; <a class="code" href="a00317.html#gae99d143b37f9cad4cd9285571aab685a">mat2x4_cast</a>(tdualquat&lt;T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; <a class="code" href="a00317.html#gaf59f5bb69620d2891c3795c6f2639179">mat3x4_cast</a>(tdualquat&lt;T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; <a class="code" href="a00317.html#ga91025ebdca0f4ea54da08497b00e8c84">dualquat_cast</a>(mat&lt;2, 4, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        GLM_FUNC_DECL tdualquat&lt;T, Q&gt; <a class="code" href="a00317.html#ga91025ebdca0f4ea54da08497b00e8c84">dualquat_cast</a>(mat&lt;3, 4, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div>
<div class="line"><a name="l00187"></a><span class="lineno"><a class="line" href="a00317.html#gade05d29ebd4deea0f883d0e1bb4169aa">  187</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">float</span>, lowp&gt;          <a class="code" href="a00317.html#gade05d29ebd4deea0f883d0e1bb4169aa">lowp_dualquat</a>;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="a00317.html#gaa7aeb54c167712b38f2178a1be2360ad">  192</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">float</span>, mediump&gt;       <a class="code" href="a00317.html#gaa7aeb54c167712b38f2178a1be2360ad">mediump_dualquat</a>;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div>
<div class="line"><a name="l00197"></a><span class="lineno"><a class="line" href="a00317.html#ga9ef5bf1da52a9d4932335a517086ceaf">  197</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">float</span>, highp&gt;         <a class="code" href="a00317.html#ga9ef5bf1da52a9d4932335a517086ceaf">highp_dualquat</a>;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div>
<div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="a00317.html#gaa38f671be25a7f3b136a452a8bb42860">  203</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">float</span>, lowp&gt;          <a class="code" href="a00317.html#gaa38f671be25a7f3b136a452a8bb42860">lowp_fdualquat</a>;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div>
<div class="line"><a name="l00208"></a><span class="lineno"><a class="line" href="a00317.html#ga4a6b594ff7e81150d8143001367a9431">  208</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">float</span>, mediump&gt;       <a class="code" href="a00317.html#ga4a6b594ff7e81150d8143001367a9431">mediump_fdualquat</a>;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div>
<div class="line"><a name="l00213"></a><span class="lineno"><a class="line" href="a00317.html#ga4c4e55e9c99dc57b299ed590968da564">  213</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">float</span>, highp&gt;         <a class="code" href="a00317.html#ga4c4e55e9c99dc57b299ed590968da564">highp_fdualquat</a>;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div>
<div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="a00317.html#gab4c5103338af3dac7e0fbc86895a3f1a">  219</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">double</span>, lowp&gt;         <a class="code" href="a00317.html#gab4c5103338af3dac7e0fbc86895a3f1a">lowp_ddualquat</a>;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;</div>
<div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="a00317.html#ga0fb11e48e2d16348ccb06a25213641b4">  224</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">double</span>, mediump&gt;      <a class="code" href="a00317.html#ga0fb11e48e2d16348ccb06a25213641b4">mediump_ddualquat</a>;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div>
<div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="a00317.html#ga8f67eafa7197d7a668dad5105a463d2a">  229</a></span>&#160;        typedef tdualquat&lt;<span class="keywordtype">double</span>, highp&gt;        <a class="code" href="a00317.html#ga8f67eafa7197d7a668dad5105a463d2a">highp_ddualquat</a>;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;<span class="preprocessor">#if(!defined(GLM_PRECISION_HIGHP_FLOAT) &amp;&amp; !defined(GLM_PRECISION_MEDIUMP_FLOAT) &amp;&amp; !defined(GLM_PRECISION_LOWP_FLOAT))</span></div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;        <span class="keyword">typedef</span> highp_fdualquat                 <a class="code" href="a00317.html#gae93abee0c979902fbec6a7bee0f6fae1">dualquat</a>;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;</div>
<div class="line"><a name="l00241"></a><span class="lineno"><a class="line" href="a00317.html#ga237c2b9b42c9a930e49de5840ae0f930">  241</a></span>&#160;        <span class="keyword">typedef</span> highp_fdualquat                 <a class="code" href="a00317.html#ga237c2b9b42c9a930e49de5840ae0f930">fdualquat</a>;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_HIGHP_FLOAT) &amp;&amp; !defined(GLM_PRECISION_MEDIUMP_FLOAT) &amp;&amp; !defined(GLM_PRECISION_LOWP_FLOAT))</span></div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;        <span class="keyword">typedef</span> highp_fdualquat                 <a class="code" href="a00317.html#gae93abee0c979902fbec6a7bee0f6fae1">dualquat</a>;</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;        <span class="keyword">typedef</span> highp_fdualquat                 <a class="code" href="a00317.html#ga237c2b9b42c9a930e49de5840ae0f930">fdualquat</a>;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;<span class="preprocessor">#elif(!defined(GLM_PRECISION_HIGHP_FLOAT) &amp;&amp; defined(GLM_PRECISION_MEDIUMP_FLOAT) &amp;&amp; !defined(GLM_PRECISION_LOWP_FLOAT))</span></div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;        <span class="keyword">typedef</span> mediump_fdualquat               <a class="code" href="a00317.html#gae93abee0c979902fbec6a7bee0f6fae1">dualquat</a>;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;        <span class="keyword">typedef</span> mediump_fdualquat               <a class="code" href="a00317.html#ga237c2b9b42c9a930e49de5840ae0f930">fdualquat</a>;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;<span class="preprocessor">#elif(!defined(GLM_PRECISION_HIGHP_FLOAT) &amp;&amp; !defined(GLM_PRECISION_MEDIUMP_FLOAT) &amp;&amp; defined(GLM_PRECISION_LOWP_FLOAT))</span></div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;        <span class="keyword">typedef</span> lowp_fdualquat                  <a class="code" href="a00317.html#gae93abee0c979902fbec6a7bee0f6fae1">dualquat</a>;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        <span class="keyword">typedef</span> lowp_fdualquat                  <a class="code" href="a00317.html#ga237c2b9b42c9a930e49de5840ae0f930">fdualquat</a>;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;<span class="preprocessor">#       error &quot;GLM error: multiple default precision requested for single-precision floating-point types&quot;</span></div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;<span class="preprocessor">#if(!defined(GLM_PRECISION_HIGHP_DOUBLE) &amp;&amp; !defined(GLM_PRECISION_MEDIUMP_DOUBLE) &amp;&amp; !defined(GLM_PRECISION_LOWP_DOUBLE))</span></div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;        <span class="keyword">typedef</span> highp_ddualquat                 <a class="code" href="a00317.html#ga3d71f98d84ba59dfe4e369fde4714cd6">ddualquat</a>;</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_HIGHP_DOUBLE) &amp;&amp; !defined(GLM_PRECISION_MEDIUMP_DOUBLE) &amp;&amp; !defined(GLM_PRECISION_LOWP_DOUBLE))</span></div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;        <span class="keyword">typedef</span> highp_ddualquat                 <a class="code" href="a00317.html#ga3d71f98d84ba59dfe4e369fde4714cd6">ddualquat</a>;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;<span class="preprocessor">#elif(!defined(GLM_PRECISION_HIGHP_DOUBLE) &amp;&amp; defined(GLM_PRECISION_MEDIUMP_DOUBLE) &amp;&amp; !defined(GLM_PRECISION_LOWP_DOUBLE))</span></div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        <span class="keyword">typedef</span> mediump_ddualquat               <a class="code" href="a00317.html#ga3d71f98d84ba59dfe4e369fde4714cd6">ddualquat</a>;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;<span class="preprocessor">#elif(!defined(GLM_PRECISION_HIGHP_DOUBLE) &amp;&amp; !defined(GLM_PRECISION_MEDIUMP_DOUBLE) &amp;&amp; defined(GLM_PRECISION_LOWP_DOUBLE))</span></div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;        <span class="keyword">typedef</span> lowp_ddualquat                  <a class="code" href="a00317.html#ga3d71f98d84ba59dfe4e369fde4714cd6">ddualquat</a>;</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;<span class="preprocessor">#       error &quot;GLM error: Multiple default precision requested for double-precision floating-point types&quot;</span></div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;<span class="preprocessor">#include &quot;dual_quaternion.inl&quot;</span></div>
<div class="ttc" id="a00317_html_ga3d71f98d84ba59dfe4e369fde4714cd6"><div class="ttname"><a href="a00317.html#ga3d71f98d84ba59dfe4e369fde4714cd6">glm::ddualquat</a></div><div class="ttdeci">highp_ddualquat ddualquat</div><div class="ttdoc">Dual-quaternion of default double-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00260">dual_quaternion.hpp:260</a></div></div>
<div class="ttc" id="a00317_html_ga237c2b9b42c9a930e49de5840ae0f930"><div class="ttname"><a href="a00317.html#ga237c2b9b42c9a930e49de5840ae0f930">glm::fdualquat</a></div><div class="ttdeci">highp_fdualquat fdualquat</div><div class="ttdoc">Dual-quaternion of single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00241">dual_quaternion.hpp:241</a></div></div>
<div class="ttc" id="a00317_html_gae99d143b37f9cad4cd9285571aab685a"><div class="ttname"><a href="a00317.html#gae99d143b37f9cad4cd9285571aab685a">glm::mat2x4_cast</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 4, T, Q &gt; mat2x4_cast(tdualquat&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 2 * 4 matrix. </div></div>
<div class="ttc" id="a00317_html_ga8f67eafa7197d7a668dad5105a463d2a"><div class="ttname"><a href="a00317.html#ga8f67eafa7197d7a668dad5105a463d2a">glm::highp_ddualquat</a></div><div class="ttdeci">tdualquat&lt; double, highp &gt; highp_ddualquat</div><div class="ttdoc">Dual-quaternion of high double-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00229">dual_quaternion.hpp:229</a></div></div>
<div class="ttc" id="a00317_html_ga299b8641509606b1958ffa104a162cfe"><div class="ttname"><a href="a00317.html#ga299b8641509606b1958ffa104a162cfe">glm::normalize</a></div><div class="ttdeci">GLM_FUNC_DECL tdualquat&lt; T, Q &gt; normalize(tdualquat&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the normalized quaternion. </div></div>
<div class="ttc" id="a00317_html_ga0b35c0e30df8a875dbaa751e0bd800e0"><div class="ttname"><a href="a00317.html#ga0b35c0e30df8a875dbaa751e0bd800e0">glm::dual_quat_identity</a></div><div class="ttdeci">GLM_FUNC_DECL tdualquat&lt; T, Q &gt; dual_quat_identity()</div><div class="ttdoc">Creates an identity dual quaternion. </div></div>
<div class="ttc" id="a00317_html_ga070f521a953f6461af4ab4cf8ccbf27e"><div class="ttname"><a href="a00317.html#ga070f521a953f6461af4ab4cf8ccbf27e">glm::inverse</a></div><div class="ttdeci">GLM_FUNC_DECL tdualquat&lt; T, Q &gt; inverse(tdualquat&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the q inverse. </div></div>
<div class="ttc" id="a00317_html_gace8380112d16d33f520839cb35a4d173"><div class="ttname"><a href="a00317.html#gace8380112d16d33f520839cb35a4d173">glm::lerp</a></div><div class="ttdeci">GLM_FUNC_DECL tdualquat&lt; T, Q &gt; lerp(tdualquat&lt; T, Q &gt; const &amp;x, tdualquat&lt; T, Q &gt; const &amp;y, T const &amp;a)</div><div class="ttdoc">Returns the linear interpolation of two dual quaternion. </div></div>
<div class="ttc" id="a00317_html_gade05d29ebd4deea0f883d0e1bb4169aa"><div class="ttname"><a href="a00317.html#gade05d29ebd4deea0f883d0e1bb4169aa">glm::lowp_dualquat</a></div><div class="ttdeci">tdualquat&lt; float, lowp &gt; lowp_dualquat</div><div class="ttdoc">Dual-quaternion of low single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00187">dual_quaternion.hpp:187</a></div></div>
<div class="ttc" id="a00317_html_gaa38f671be25a7f3b136a452a8bb42860"><div class="ttname"><a href="a00317.html#gaa38f671be25a7f3b136a452a8bb42860">glm::lowp_fdualquat</a></div><div class="ttdeci">tdualquat&lt; float, lowp &gt; lowp_fdualquat</div><div class="ttdoc">Dual-quaternion of low single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00203">dual_quaternion.hpp:203</a></div></div>
<div class="ttc" id="a00254_html_gab703732449be6c7199369b3f9a91ed38"><div class="ttname"><a href="a00254.html#gab703732449be6c7199369b3f9a91ed38">glm::length</a></div><div class="ttdeci">GLM_FUNC_DECL T length(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the norm of a quaternions. </div></div>
<div class="ttc" id="a00317_html_gab4c5103338af3dac7e0fbc86895a3f1a"><div class="ttname"><a href="a00317.html#gab4c5103338af3dac7e0fbc86895a3f1a">glm::lowp_ddualquat</a></div><div class="ttdeci">tdualquat&lt; double, lowp &gt; lowp_ddualquat</div><div class="ttdoc">Dual-quaternion of low double-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00219">dual_quaternion.hpp:219</a></div></div>
<div class="ttc" id="a00317_html_gaf59f5bb69620d2891c3795c6f2639179"><div class="ttname"><a href="a00317.html#gaf59f5bb69620d2891c3795c6f2639179">glm::mat3x4_cast</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 4, T, Q &gt; mat3x4_cast(tdualquat&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a quaternion to a 3 * 4 matrix. </div></div>
<div class="ttc" id="a00317_html_gae93abee0c979902fbec6a7bee0f6fae1"><div class="ttname"><a href="a00317.html#gae93abee0c979902fbec6a7bee0f6fae1">glm::dualquat</a></div><div class="ttdeci">highp_fdualquat dualquat</div><div class="ttdoc">Dual-quaternion of floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00236">dual_quaternion.hpp:236</a></div></div>
<div class="ttc" id="a00317_html_ga4c4e55e9c99dc57b299ed590968da564"><div class="ttname"><a href="a00317.html#ga4c4e55e9c99dc57b299ed590968da564">glm::highp_fdualquat</a></div><div class="ttdeci">tdualquat&lt; float, highp &gt; highp_fdualquat</div><div class="ttdoc">Dual-quaternion of high single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00213">dual_quaternion.hpp:213</a></div></div>
<div class="ttc" id="a00356_html_ga1a32fceb71962e6160e8af295c91930a"><div class="ttname"><a href="a00356.html#ga1a32fceb71962e6160e8af295c91930a">glm::orientation</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; orientation(vec&lt; 3, T, Q &gt; const &amp;Normal, vec&lt; 3, T, Q &gt; const &amp;Up)</div><div class="ttdoc">Build a rotation matrix from a normal and a up vector. </div></div>
<div class="ttc" id="a00317_html_gaa7aeb54c167712b38f2178a1be2360ad"><div class="ttname"><a href="a00317.html#gaa7aeb54c167712b38f2178a1be2360ad">glm::mediump_dualquat</a></div><div class="ttdeci">tdualquat&lt; float, mediump &gt; mediump_dualquat</div><div class="ttdoc">Dual-quaternion of medium single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00192">dual_quaternion.hpp:192</a></div></div>
<div class="ttc" id="a00317_html_ga4a6b594ff7e81150d8143001367a9431"><div class="ttname"><a href="a00317.html#ga4a6b594ff7e81150d8143001367a9431">glm::mediump_fdualquat</a></div><div class="ttdeci">tdualquat&lt; float, mediump &gt; mediump_fdualquat</div><div class="ttdoc">Dual-quaternion of medium single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00208">dual_quaternion.hpp:208</a></div></div>
<div class="ttc" id="a00317_html_ga0fb11e48e2d16348ccb06a25213641b4"><div class="ttname"><a href="a00317.html#ga0fb11e48e2d16348ccb06a25213641b4">glm::mediump_ddualquat</a></div><div class="ttdeci">tdualquat&lt; double, mediump &gt; mediump_ddualquat</div><div class="ttdoc">Dual-quaternion of medium double-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00224">dual_quaternion.hpp:224</a></div></div>
<div class="ttc" id="a00317_html_ga91025ebdca0f4ea54da08497b00e8c84"><div class="ttname"><a href="a00317.html#ga91025ebdca0f4ea54da08497b00e8c84">glm::dualquat_cast</a></div><div class="ttdeci">GLM_FUNC_DECL tdualquat&lt; T, Q &gt; dualquat_cast(mat&lt; 3, 4, T, Q &gt; const &amp;x)</div><div class="ttdoc">Converts a 3 * 4 matrix (augmented matrix rotation + translation) to a quaternion. </div></div>
<div class="ttc" id="a00317_html_ga9ef5bf1da52a9d4932335a517086ceaf"><div class="ttname"><a href="a00317.html#ga9ef5bf1da52a9d4932335a517086ceaf">glm::highp_dualquat</a></div><div class="ttdeci">tdualquat&lt; float, highp &gt; highp_dualquat</div><div class="ttdoc">Dual-quaternion of high single-qualifier floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00022_source.html#l00197">dual_quaternion.hpp:197</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
