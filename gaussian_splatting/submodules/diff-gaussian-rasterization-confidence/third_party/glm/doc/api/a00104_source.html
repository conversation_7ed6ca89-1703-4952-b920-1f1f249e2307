<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_operation.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_operation.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00104.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_matrix_operation is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_matrix_operation extension included&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; <a class="code" href="a00339.html#ga58a32a2beeb2478dae2a721368cdd4ac">diagonal2x2</a>(</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        GLM_FUNC_DECL mat&lt;2, 3, T, Q&gt; <a class="code" href="a00339.html#gab69f900206a430e2875a5a073851e175">diagonal2x3</a>(</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        GLM_FUNC_DECL mat&lt;2, 4, T, Q&gt; <a class="code" href="a00339.html#ga30b4dbfed60a919d66acc8a63bcdc549">diagonal2x4</a>(</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        GLM_FUNC_DECL mat&lt;3, 2, T, Q&gt; <a class="code" href="a00339.html#ga832c805d5130d28ad76236958d15b47d">diagonal3x2</a>(</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00339.html#ga5487ff9cdbc8e04d594adef1bcb16ee0">diagonal3x3</a>(</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; <a class="code" href="a00339.html#gad7551139cff0c4208d27f0ad3437833e">diagonal3x4</a>(</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        GLM_FUNC_DECL mat&lt;4, 2, T, Q&gt; <a class="code" href="a00339.html#gacb8969e6543ba775c6638161a37ac330">diagonal4x2</a>(</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        GLM_FUNC_DECL mat&lt;4, 3, T, Q&gt; <a class="code" href="a00339.html#gae235def5049d6740f0028433f5e13f90">diagonal4x3</a>(</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00339.html#ga0b4cd8dea436791b072356231ee8578f">diagonal4x4</a>(</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; <a class="code" href="a00339.html#ga9aaa7d1f40391b0b5cacccb60e104ba8">adjugate</a>(mat&lt;2, 2, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; <a class="code" href="a00339.html#ga9aaa7d1f40391b0b5cacccb60e104ba8">adjugate</a>(mat&lt;3, 3, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; <a class="code" href="a00339.html#ga9aaa7d1f40391b0b5cacccb60e104ba8">adjugate</a>(mat&lt;4, 4, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#include &quot;matrix_operation.inl&quot;</span></div>
<div class="ttc" id="a00339_html_gae235def5049d6740f0028433f5e13f90"><div class="ttname"><a href="a00339.html#gae235def5049d6740f0028433f5e13f90">glm::diagonal4x3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 3, T, Q &gt; diagonal4x3(vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_ga58a32a2beeb2478dae2a721368cdd4ac"><div class="ttname"><a href="a00339.html#ga58a32a2beeb2478dae2a721368cdd4ac">glm::diagonal2x2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt; diagonal2x2(vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_gad7551139cff0c4208d27f0ad3437833e"><div class="ttname"><a href="a00339.html#gad7551139cff0c4208d27f0ad3437833e">glm::diagonal3x4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 4, T, Q &gt; diagonal3x4(vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_ga832c805d5130d28ad76236958d15b47d"><div class="ttname"><a href="a00339.html#ga832c805d5130d28ad76236958d15b47d">glm::diagonal3x2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 2, T, Q &gt; diagonal3x2(vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_gab69f900206a430e2875a5a073851e175"><div class="ttname"><a href="a00339.html#gab69f900206a430e2875a5a073851e175">glm::diagonal2x3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 3, T, Q &gt; diagonal2x3(vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_ga5487ff9cdbc8e04d594adef1bcb16ee0"><div class="ttname"><a href="a00339.html#ga5487ff9cdbc8e04d594adef1bcb16ee0">glm::diagonal3x3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt; diagonal3x3(vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_ga9aaa7d1f40391b0b5cacccb60e104ba8"><div class="ttname"><a href="a00339.html#ga9aaa7d1f40391b0b5cacccb60e104ba8">glm::adjugate</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; adjugate(mat&lt; 4, 4, T, Q &gt; const &amp;m)</div><div class="ttdoc">Build an adjugate matrix. </div></div>
<div class="ttc" id="a00339_html_ga30b4dbfed60a919d66acc8a63bcdc549"><div class="ttname"><a href="a00339.html#ga30b4dbfed60a919d66acc8a63bcdc549">glm::diagonal2x4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 4, T, Q &gt; diagonal2x4(vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_gacb8969e6543ba775c6638161a37ac330"><div class="ttname"><a href="a00339.html#gacb8969e6543ba775c6638161a37ac330">glm::diagonal4x2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 2, T, Q &gt; diagonal4x2(vec&lt; 2, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00339_html_ga0b4cd8dea436791b072356231ee8578f"><div class="ttname"><a href="a00339.html#ga0b4cd8dea436791b072356231ee8578f">glm::diagonal4x4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt; diagonal4x4(vec&lt; 4, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a diagonal matrix. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
