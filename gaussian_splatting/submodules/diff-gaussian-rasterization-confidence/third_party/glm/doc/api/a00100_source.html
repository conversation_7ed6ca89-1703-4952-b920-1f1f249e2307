<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_integer.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_integer.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00100.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../mat2x2.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../mat2x3.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../mat2x4.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../mat3x2.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../mat3x4.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;../mat4x2.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../mat4x3.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../mat4x4.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_matrix_integer extension included&quot;)</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;{</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="a00294.html#ga8499cc3b016003f835314c1c756e9db9">   37</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, highp&gt;                           <a class="code" href="a00294.html#ga8499cc3b016003f835314c1c756e9db9">highp_imat2</a>;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="a00294.html#gaca4506a3efa679eff7c006d9826291fd">   41</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, highp&gt;                           <a class="code" href="a00294.html#gaca4506a3efa679eff7c006d9826291fd">highp_imat3</a>;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="a00294.html#ga7cfb09b34e0fcf73eaf6512d6483ef56">   45</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, highp&gt;                           <a class="code" href="a00294.html#ga7cfb09b34e0fcf73eaf6512d6483ef56">highp_imat4</a>;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="a00294.html#gaa389e2d1c3b10941cae870bc0aeba5b3">   49</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, highp&gt;                           <a class="code" href="a00294.html#gaa389e2d1c3b10941cae870bc0aeba5b3">highp_imat2x2</a>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="a00294.html#gaba49d890e06c9444795f5a133fbf1336">   53</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, int, highp&gt;                           <a class="code" href="a00294.html#gaba49d890e06c9444795f5a133fbf1336">highp_imat2x3</a>;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="a00294.html#ga05a970fd4366dad6c8a0be676b1eae5b">   57</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int, highp&gt;                           <a class="code" href="a00294.html#ga05a970fd4366dad6c8a0be676b1eae5b">highp_imat2x4</a>;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div>
<div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="a00294.html#ga91c671c3ff9706c2393e78b22fd84bcb">   61</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, int, highp&gt;                           <a class="code" href="a00294.html#ga91c671c3ff9706c2393e78b22fd84bcb">highp_imat3x2</a>;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="a00294.html#ga07d7b7173e2a6f843ff5f1c615a95b41">   65</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, highp&gt;                           <a class="code" href="a00294.html#ga07d7b7173e2a6f843ff5f1c615a95b41">highp_imat3x3</a>;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="a00294.html#ga53008f580be99018a17b357b5a4ffc0d">   69</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, int, highp&gt;                           <a class="code" href="a00294.html#ga53008f580be99018a17b357b5a4ffc0d">highp_imat3x4</a>;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="a00294.html#ga1858820fb292cae396408b2034407f72">   73</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, int, highp&gt;                           <a class="code" href="a00294.html#ga1858820fb292cae396408b2034407f72">highp_imat4x2</a>;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="a00294.html#ga6be0b80ae74bb309bc5b964d93d68fc5">   77</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, int, highp&gt;                           <a class="code" href="a00294.html#ga6be0b80ae74bb309bc5b964d93d68fc5">highp_imat4x3</a>;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="a00294.html#ga2c783ee6f8f040ab37df2f70392c8b44">   81</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, highp&gt;                           <a class="code" href="a00294.html#ga2c783ee6f8f040ab37df2f70392c8b44">highp_imat4x4</a>;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div>
<div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="a00294.html#ga20f4cc7ab23e2aa1f4db9fdb5496d378">   86</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, mediump&gt;                 <a class="code" href="a00294.html#ga20f4cc7ab23e2aa1f4db9fdb5496d378">mediump_imat2</a>;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="a00294.html#ga6c63bdc736efd3466e0730de0251cb71">   90</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, mediump&gt;                 <a class="code" href="a00294.html#ga6c63bdc736efd3466e0730de0251cb71">mediump_imat3</a>;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="a00294.html#gaf348552978553630d2a00b78eb887ced">   94</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, mediump&gt;                 <a class="code" href="a00294.html#gaf348552978553630d2a00b78eb887ced">mediump_imat4</a>;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="a00294.html#ga4b2aeb11a329940721dda9583e71f856">   99</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, mediump&gt;                 <a class="code" href="a00294.html#ga4b2aeb11a329940721dda9583e71f856">mediump_imat2x2</a>;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="a00294.html#ga74362470ba99843ac70aee5ac38cc674">  103</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, int, mediump&gt;                 <a class="code" href="a00294.html#ga74362470ba99843ac70aee5ac38cc674">mediump_imat2x3</a>;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="a00294.html#ga8da25cd380ba30fc5b68a4687deb3e09">  107</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int, mediump&gt;                 <a class="code" href="a00294.html#ga8da25cd380ba30fc5b68a4687deb3e09">mediump_imat2x4</a>;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="a00294.html#gac0b4e42d648fb3eaf4bb88da82ecc809">  111</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, int, mediump&gt;                 <a class="code" href="a00294.html#gac0b4e42d648fb3eaf4bb88da82ecc809">mediump_imat3x2</a>;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
<div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="a00294.html#gad99cc2aad8fc57f068cfa7719dbbea12">  115</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, mediump&gt;                 <a class="code" href="a00294.html#gad99cc2aad8fc57f068cfa7719dbbea12">mediump_imat3x3</a>;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="a00294.html#ga67689a518b181a26540bc44a163525cd">  119</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, int, mediump&gt;                 <a class="code" href="a00294.html#ga67689a518b181a26540bc44a163525cd">mediump_imat3x4</a>;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="a00294.html#ga8b2d35816f7103f0f4c82dd2f27571fc">  123</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, int, mediump&gt;                 <a class="code" href="a00294.html#ga8b2d35816f7103f0f4c82dd2f27571fc">mediump_imat4x2</a>;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div>
<div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="a00294.html#ga5b10acc696759e03f6ab918f4467e94c">  127</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, int, mediump&gt;                 <a class="code" href="a00294.html#ga5b10acc696759e03f6ab918f4467e94c">mediump_imat4x3</a>;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="a00294.html#ga2596869d154dec1180beadbb9df80501">  131</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, mediump&gt;                 <a class="code" href="a00294.html#ga2596869d154dec1180beadbb9df80501">mediump_imat4x4</a>;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div>
<div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="a00294.html#gaa0bff0be804142bb16d441aec0a7962e">  136</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, lowp&gt;                            <a class="code" href="a00294.html#gaa0bff0be804142bb16d441aec0a7962e">lowp_imat2</a>;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="a00294.html#ga69bfe668f4170379fc1f35d82b060c43">  140</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, lowp&gt;                            <a class="code" href="a00294.html#ga69bfe668f4170379fc1f35d82b060c43">lowp_imat3</a>;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno"><a class="line" href="a00294.html#gad1e77f7270cad461ca4fcb4c3ec2e98c">  144</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, lowp&gt;                            <a class="code" href="a00294.html#gad1e77f7270cad461ca4fcb4c3ec2e98c">lowp_imat4</a>;</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="a00294.html#ga92b95b679975d408645547ab45a8dcd8">  149</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, lowp&gt;                            <a class="code" href="a00294.html#ga92b95b679975d408645547ab45a8dcd8">lowp_imat2x2</a>;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div>
<div class="line"><a name="l00153"></a><span class="lineno"><a class="line" href="a00294.html#ga8c9e7a388f8e7c52f1e6857dee8afb65">  153</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, int, lowp&gt;                            <a class="code" href="a00294.html#ga8c9e7a388f8e7c52f1e6857dee8afb65">lowp_imat2x3</a>;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div>
<div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="a00294.html#ga9cc13bd1f8dd2933e9fa31fe3f70e16e">  157</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int, lowp&gt;                            <a class="code" href="a00294.html#ga9cc13bd1f8dd2933e9fa31fe3f70e16e">lowp_imat2x4</a>;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div>
<div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="a00294.html#ga33db8f27491d30906cd37c0d86b3f432">  161</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, int, lowp&gt;                            <a class="code" href="a00294.html#ga33db8f27491d30906cd37c0d86b3f432">lowp_imat3x2</a>;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div>
<div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="a00294.html#ga664f061df00020048c3f8530329ace45">  165</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, lowp&gt;                            <a class="code" href="a00294.html#ga664f061df00020048c3f8530329ace45">lowp_imat3x3</a>;</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="a00294.html#ga9273faab33623d944af4080befbb2c80">  169</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, int, lowp&gt;                            <a class="code" href="a00294.html#ga9273faab33623d944af4080befbb2c80">lowp_imat3x4</a>;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div>
<div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="a00294.html#ga26ec1a2ba08a1488f5f05336858a0f09">  173</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, int, lowp&gt;                            <a class="code" href="a00294.html#ga26ec1a2ba08a1488f5f05336858a0f09">lowp_imat4x2</a>;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div>
<div class="line"><a name="l00177"></a><span class="lineno"><a class="line" href="a00294.html#ga8f40483a3ae634ead8ad22272c543a33">  177</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, int, lowp&gt;                            <a class="code" href="a00294.html#ga8f40483a3ae634ead8ad22272c543a33">lowp_imat4x3</a>;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;</div>
<div class="line"><a name="l00181"></a><span class="lineno"><a class="line" href="a00294.html#gaf65677e53ac8e31a107399340d5e2451">  181</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, lowp&gt;                            <a class="code" href="a00294.html#gaf65677e53ac8e31a107399340d5e2451">lowp_imat4x4</a>;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div>
<div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="a00294.html#ga42cbce64c4c1cd121b8437daa6e110de">  186</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, uint, highp&gt;                          <a class="code" href="a00294.html#ga42cbce64c4c1cd121b8437daa6e110de">highp_umat2</a>;</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div>
<div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="a00294.html#gaa1143120339b7d2d469d327662e8a172">  190</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, uint, highp&gt;                          <a class="code" href="a00294.html#gaa1143120339b7d2d469d327662e8a172">highp_umat3</a>;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div>
<div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="a00294.html#gaf665e4e78c2cc32a54ab40325738f9c9">  194</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, uint, highp&gt;                          <a class="code" href="a00294.html#gaf665e4e78c2cc32a54ab40325738f9c9">highp_umat4</a>;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div>
<div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="a00294.html#ga5337b7bc95f9cbac08a0c00b3f936b28">  198</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, uint, highp&gt;                          <a class="code" href="a00294.html#ga5337b7bc95f9cbac08a0c00b3f936b28">highp_umat2x2</a>;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div>
<div class="line"><a name="l00202"></a><span class="lineno"><a class="line" href="a00294.html#ga90718c7128320b24b52f9ea70e643ad4">  202</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, uint, highp&gt;                          <a class="code" href="a00294.html#ga90718c7128320b24b52f9ea70e643ad4">highp_umat2x3</a>;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div>
<div class="line"><a name="l00206"></a><span class="lineno"><a class="line" href="a00294.html#gadca0a4724b4a6f56a2355b6f6e19248b">  206</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, uint, highp&gt;                          <a class="code" href="a00294.html#gadca0a4724b4a6f56a2355b6f6e19248b">highp_umat2x4</a>;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div>
<div class="line"><a name="l00210"></a><span class="lineno"><a class="line" href="a00294.html#ga844a5da2e7fc03fc7cccc7f1b70809c4">  210</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, uint, highp&gt;                          <a class="code" href="a00294.html#ga844a5da2e7fc03fc7cccc7f1b70809c4">highp_umat3x2</a>;</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div>
<div class="line"><a name="l00214"></a><span class="lineno"><a class="line" href="a00294.html#ga1f7d41c36b980774a4d2e7c1647fb4b2">  214</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, uint, highp&gt;                          <a class="code" href="a00294.html#ga1f7d41c36b980774a4d2e7c1647fb4b2">highp_umat3x3</a>;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div>
<div class="line"><a name="l00218"></a><span class="lineno"><a class="line" href="a00294.html#ga25ee15c323924f2d0fe9896d329e5086">  218</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, uint, highp&gt;                          <a class="code" href="a00294.html#ga25ee15c323924f2d0fe9896d329e5086">highp_umat3x4</a>;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div>
<div class="line"><a name="l00222"></a><span class="lineno"><a class="line" href="a00294.html#gae69eb82ec08b0dc9bf2ead2a339ff801">  222</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, uint, highp&gt;                          <a class="code" href="a00294.html#gae69eb82ec08b0dc9bf2ead2a339ff801">highp_umat4x2</a>;</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div>
<div class="line"><a name="l00226"></a><span class="lineno"><a class="line" href="a00294.html#ga45a8163d02c43216252056b0c120f3a5">  226</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, uint, highp&gt;                          <a class="code" href="a00294.html#ga45a8163d02c43216252056b0c120f3a5">highp_umat4x3</a>;</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div>
<div class="line"><a name="l00230"></a><span class="lineno"><a class="line" href="a00294.html#ga6a56cbb769aed334c95241664415f9ba">  230</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, uint, highp&gt;                          <a class="code" href="a00294.html#ga6a56cbb769aed334c95241664415f9ba">highp_umat4x4</a>;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div>
<div class="line"><a name="l00235"></a><span class="lineno"><a class="line" href="a00294.html#ga43041378b3410ea951b7de0dfd2bc7ee">  235</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, uint, mediump&gt;                        <a class="code" href="a00294.html#ga43041378b3410ea951b7de0dfd2bc7ee">mediump_umat2</a>;</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div>
<div class="line"><a name="l00239"></a><span class="lineno"><a class="line" href="a00294.html#ga1730dbe3c67801f53520b06d1aa0a34a">  239</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, uint, mediump&gt;                        <a class="code" href="a00294.html#ga1730dbe3c67801f53520b06d1aa0a34a">mediump_umat3</a>;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div>
<div class="line"><a name="l00243"></a><span class="lineno"><a class="line" href="a00294.html#ga5087c2beb26a11d9af87432e554cf9d1">  243</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, uint, mediump&gt;                        <a class="code" href="a00294.html#ga5087c2beb26a11d9af87432e554cf9d1">mediump_umat4</a>;</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div>
<div class="line"><a name="l00248"></a><span class="lineno"><a class="line" href="a00294.html#ga3b209b1b751f041422137e3c065dfa98">  248</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, uint, mediump&gt;                        <a class="code" href="a00294.html#ga3b209b1b751f041422137e3c065dfa98">mediump_umat2x2</a>;</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;</div>
<div class="line"><a name="l00252"></a><span class="lineno"><a class="line" href="a00294.html#gaee2c1f13b41f4c92ea5b3efe367a1306">  252</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, uint, mediump&gt;                        <a class="code" href="a00294.html#gaee2c1f13b41f4c92ea5b3efe367a1306">mediump_umat2x3</a>;</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;</div>
<div class="line"><a name="l00256"></a><span class="lineno"><a class="line" href="a00294.html#gae1317ddca16d01e119a40b7f0ee85f95">  256</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, uint, mediump&gt;                        <a class="code" href="a00294.html#gae1317ddca16d01e119a40b7f0ee85f95">mediump_umat2x4</a>;</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div>
<div class="line"><a name="l00260"></a><span class="lineno"><a class="line" href="a00294.html#gaadc28bfdc8ebca81ae85121b11994970">  260</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, uint, mediump&gt;                        <a class="code" href="a00294.html#gaadc28bfdc8ebca81ae85121b11994970">mediump_umat3x2</a>;</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div>
<div class="line"><a name="l00264"></a><span class="lineno"><a class="line" href="a00294.html#ga48f2fc38d3f7fab3cfbc961278ced53d">  264</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, uint, mediump&gt;                        <a class="code" href="a00294.html#ga48f2fc38d3f7fab3cfbc961278ced53d">mediump_umat3x3</a>;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;</div>
<div class="line"><a name="l00268"></a><span class="lineno"><a class="line" href="a00294.html#ga78009a1e4ca64217e46b418535e52546">  268</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, uint, mediump&gt;                        <a class="code" href="a00294.html#ga78009a1e4ca64217e46b418535e52546">mediump_umat3x4</a>;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div>
<div class="line"><a name="l00272"></a><span class="lineno"><a class="line" href="a00294.html#gaf35aefd81cc13718f6b059623f7425fa">  272</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, uint, mediump&gt;                        <a class="code" href="a00294.html#gaf35aefd81cc13718f6b059623f7425fa">mediump_umat4x2</a>;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div>
<div class="line"><a name="l00276"></a><span class="lineno"><a class="line" href="a00294.html#ga4e1bed14fbc7f4b376aaed064f89f0fb">  276</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, uint, mediump&gt;                        <a class="code" href="a00294.html#ga4e1bed14fbc7f4b376aaed064f89f0fb">mediump_umat4x3</a>;</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div>
<div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="a00294.html#gaa9428fc8430dc552aad920653f822ef3">  280</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, uint, mediump&gt;                        <a class="code" href="a00294.html#gaa9428fc8430dc552aad920653f822ef3">mediump_umat4x4</a>;</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;</div>
<div class="line"><a name="l00285"></a><span class="lineno"><a class="line" href="a00294.html#gaf2fba702d990437fc88ff3f3a76846ee">  285</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, uint, lowp&gt;                           <a class="code" href="a00294.html#gaf2fba702d990437fc88ff3f3a76846ee">lowp_umat2</a>;</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno"><a class="line" href="a00294.html#gaf1145f72bcdd590f5808c4bc170c2924">  289</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, uint, lowp&gt;                           <a class="code" href="a00294.html#gaf1145f72bcdd590f5808c4bc170c2924">lowp_umat3</a>;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;</div>
<div class="line"><a name="l00293"></a><span class="lineno"><a class="line" href="a00294.html#gac092c6105827bf9ea080db38074b78eb">  293</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, uint, lowp&gt;                           <a class="code" href="a00294.html#gac092c6105827bf9ea080db38074b78eb">lowp_umat4</a>;</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div>
<div class="line"><a name="l00298"></a><span class="lineno"><a class="line" href="a00294.html#ga7b2e9d89745f7175051284e54c81d81c">  298</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, uint, lowp&gt;                           <a class="code" href="a00294.html#ga7b2e9d89745f7175051284e54c81d81c">lowp_umat2x2</a>;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div>
<div class="line"><a name="l00302"></a><span class="lineno"><a class="line" href="a00294.html#ga3072f90fd86f17a862e21589fbb14c0f">  302</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, uint, lowp&gt;                           <a class="code" href="a00294.html#ga3072f90fd86f17a862e21589fbb14c0f">lowp_umat2x3</a>;</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div>
<div class="line"><a name="l00306"></a><span class="lineno"><a class="line" href="a00294.html#ga8bb45fec4bd77bd81b4ae7eb961a270d">  306</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, uint, lowp&gt;                           <a class="code" href="a00294.html#ga8bb45fec4bd77bd81b4ae7eb961a270d">lowp_umat2x4</a>;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;</div>
<div class="line"><a name="l00310"></a><span class="lineno"><a class="line" href="a00294.html#ga56ea68c6a6cba8d8c21d17bb14e69c6b">  310</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, uint, lowp&gt;                           <a class="code" href="a00294.html#ga56ea68c6a6cba8d8c21d17bb14e69c6b">lowp_umat3x2</a>;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div>
<div class="line"><a name="l00314"></a><span class="lineno"><a class="line" href="a00294.html#ga4f660a39a395cc14f018f985e7dfbeb5">  314</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, uint, lowp&gt;                           <a class="code" href="a00294.html#ga4f660a39a395cc14f018f985e7dfbeb5">lowp_umat3x3</a>;</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;</div>
<div class="line"><a name="l00318"></a><span class="lineno"><a class="line" href="a00294.html#gaec3d624306bd59649f021864709d56b5">  318</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, uint, lowp&gt;                           <a class="code" href="a00294.html#gaec3d624306bd59649f021864709d56b5">lowp_umat3x4</a>;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div>
<div class="line"><a name="l00322"></a><span class="lineno"><a class="line" href="a00294.html#ga7716c2b210d141846f1ac4e774adef5e">  322</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, uint, lowp&gt;                           <a class="code" href="a00294.html#ga7716c2b210d141846f1ac4e774adef5e">lowp_umat4x2</a>;</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div>
<div class="line"><a name="l00326"></a><span class="lineno"><a class="line" href="a00294.html#ga09ab33a2636f5f43f7fae29cfbc20fff">  326</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, uint, lowp&gt;                           <a class="code" href="a00294.html#ga09ab33a2636f5f43f7fae29cfbc20fff">lowp_umat4x3</a>;</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div>
<div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="a00294.html#ga10aafc66cf1a0ece336b1c5ae13d0cc0">  330</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, uint, lowp&gt;                           <a class="code" href="a00294.html#ga10aafc66cf1a0ece336b1c5ae13d0cc0">lowp_umat4x4</a>;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_HIGHP_INT))</span></div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;        <span class="keyword">typedef</span> highp_imat2                                                             <a class="code" href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">imat2</a>;</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;        <span class="keyword">typedef</span> highp_imat3                                                             <a class="code" href="a00294.html#ga038f68437155ffa3c2583a15264a8195">imat3</a>;</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;        <span class="keyword">typedef</span> highp_imat4                                                             <a class="code" href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">imat4</a>;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;        <span class="keyword">typedef</span> highp_imat2x2                                                   <a class="code" href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">imat2x2</a>;</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;        <span class="keyword">typedef</span> highp_imat2x3                                                   <a class="code" href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">imat2x3</a>;</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;        <span class="keyword">typedef</span> highp_imat2x4                                                   <a class="code" href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">imat2x4</a>;</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;        <span class="keyword">typedef</span> highp_imat3x2                                                   <a class="code" href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">imat3x2</a>;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;        <span class="keyword">typedef</span> highp_imat3x3                                                   <a class="code" href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">imat3x3</a>;</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;        <span class="keyword">typedef</span> highp_imat3x4                                                   <a class="code" href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">imat3x4</a>;</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;        <span class="keyword">typedef</span> highp_imat4x2                                                   <a class="code" href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">imat4x2</a>;</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;        <span class="keyword">typedef</span> highp_imat4x3                                                   <a class="code" href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">imat4x3</a>;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        <span class="keyword">typedef</span> highp_imat4x4                                                   <a class="code" href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">imat4x4</a>;</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_LOWP_INT))</span></div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;        <span class="keyword">typedef</span> lowp_imat2                                                              <a class="code" href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">imat2</a>;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;        <span class="keyword">typedef</span> lowp_imat3                                                              <a class="code" href="a00294.html#ga038f68437155ffa3c2583a15264a8195">imat3</a>;</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;        <span class="keyword">typedef</span> lowp_imat4                                                              <a class="code" href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">imat4</a>;</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;        <span class="keyword">typedef</span> lowp_imat2x2                                                    <a class="code" href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">imat2x2</a>;</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;        <span class="keyword">typedef</span> lowp_imat2x3                                                    <a class="code" href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">imat2x3</a>;</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;        <span class="keyword">typedef</span> lowp_imat2x4                                                    <a class="code" href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">imat2x4</a>;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;        <span class="keyword">typedef</span> lowp_imat3x2                                                    <a class="code" href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">imat3x2</a>;</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;        <span class="keyword">typedef</span> lowp_imat3x3                                                    <a class="code" href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">imat3x3</a>;</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;        <span class="keyword">typedef</span> lowp_imat3x4                                                    <a class="code" href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">imat3x4</a>;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;        <span class="keyword">typedef</span> lowp_imat4x2                                                    <a class="code" href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">imat4x2</a>;</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;        <span class="keyword">typedef</span> lowp_imat4x3                                                    <a class="code" href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">imat4x3</a>;</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;        <span class="keyword">typedef</span> lowp_imat4x4                                                    <a class="code" href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">imat4x4</a>;</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;<span class="preprocessor">#else //if(defined(GLM_PRECISION_MEDIUMP_INT))</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;</div>
<div class="line"><a name="l00362"></a><span class="lineno"><a class="line" href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">  362</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat2                                                   <a class="code" href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">imat2</a>;</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;</div>
<div class="line"><a name="l00366"></a><span class="lineno"><a class="line" href="a00294.html#ga038f68437155ffa3c2583a15264a8195">  366</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat3                                                   <a class="code" href="a00294.html#ga038f68437155ffa3c2583a15264a8195">imat3</a>;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;</div>
<div class="line"><a name="l00370"></a><span class="lineno"><a class="line" href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">  370</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat4                                                   <a class="code" href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">imat4</a>;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;</div>
<div class="line"><a name="l00374"></a><span class="lineno"><a class="line" href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">  374</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat2x2                                                 <a class="code" href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">imat2x2</a>;</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;</div>
<div class="line"><a name="l00378"></a><span class="lineno"><a class="line" href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">  378</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat2x3                                                 <a class="code" href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">imat2x3</a>;</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div>
<div class="line"><a name="l00382"></a><span class="lineno"><a class="line" href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">  382</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat2x4                                                 <a class="code" href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">imat2x4</a>;</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;</div>
<div class="line"><a name="l00386"></a><span class="lineno"><a class="line" href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">  386</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat3x2                                                 <a class="code" href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">imat3x2</a>;</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;</div>
<div class="line"><a name="l00390"></a><span class="lineno"><a class="line" href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">  390</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat3x3                                                 <a class="code" href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">imat3x3</a>;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;</div>
<div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">  394</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat3x4                                                 <a class="code" href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">imat3x4</a>;</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;</div>
<div class="line"><a name="l00398"></a><span class="lineno"><a class="line" href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">  398</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat4x2                                                 <a class="code" href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">imat4x2</a>;</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div>
<div class="line"><a name="l00402"></a><span class="lineno"><a class="line" href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">  402</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat4x3                                                 <a class="code" href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">imat4x3</a>;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div>
<div class="line"><a name="l00406"></a><span class="lineno"><a class="line" href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">  406</a></span>&#160;        <span class="keyword">typedef</span> mediump_imat4x4                                                 <a class="code" href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">imat4x4</a>;</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_HIGHP_UINT))</span></div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;        <span class="keyword">typedef</span> highp_umat2                                                             <a class="code" href="a00294.html#ga4cae85566f900debf930c41944b64691">umat2</a>;</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;        <span class="keyword">typedef</span> highp_umat3                                                             <a class="code" href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">umat3</a>;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;        <span class="keyword">typedef</span> highp_umat4                                                             <a class="code" href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">umat4</a>;</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;        <span class="keyword">typedef</span> highp_umat2x2                                                   <a class="code" href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">umat2x2</a>;</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;        <span class="keyword">typedef</span> highp_umat2x3                                                   <a class="code" href="a00294.html#ga1870da7578d5022b973a83155d386ab3">umat2x3</a>;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;        <span class="keyword">typedef</span> highp_umat2x4                                                   <a class="code" href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">umat2x4</a>;</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;        <span class="keyword">typedef</span> highp_umat3x2                                                   <a class="code" href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">umat3x2</a>;</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;        <span class="keyword">typedef</span> highp_umat3x3                                                   <a class="code" href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">umat3x3</a>;</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;        <span class="keyword">typedef</span> highp_umat3x4                                                   <a class="code" href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">umat3x4</a>;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;        <span class="keyword">typedef</span> highp_umat4x2                                                   <a class="code" href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">umat4x2</a>;</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;        <span class="keyword">typedef</span> highp_umat4x3                                                   <a class="code" href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">umat4x3</a>;</div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;        <span class="keyword">typedef</span> highp_umat4x4                                                   <a class="code" href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">umat4x4</a>;</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_LOWP_UINT))</span></div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;        <span class="keyword">typedef</span> lowp_umat2                                                              <a class="code" href="a00294.html#ga4cae85566f900debf930c41944b64691">umat2</a>;</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;        <span class="keyword">typedef</span> lowp_umat3                                                              <a class="code" href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">umat3</a>;</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;        <span class="keyword">typedef</span> lowp_umat4                                                              <a class="code" href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">umat4</a>;</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;        <span class="keyword">typedef</span> lowp_umat2x2                                                    <a class="code" href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">umat2x2</a>;</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;        <span class="keyword">typedef</span> lowp_umat2x3                                                    <a class="code" href="a00294.html#ga1870da7578d5022b973a83155d386ab3">umat2x3</a>;</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;        <span class="keyword">typedef</span> lowp_umat2x4                                                    <a class="code" href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">umat2x4</a>;</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;        <span class="keyword">typedef</span> lowp_umat3x2                                                    <a class="code" href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">umat3x2</a>;</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;        <span class="keyword">typedef</span> lowp_umat3x3                                                    <a class="code" href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">umat3x3</a>;</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;        <span class="keyword">typedef</span> lowp_umat3x4                                                    <a class="code" href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">umat3x4</a>;</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;        <span class="keyword">typedef</span> lowp_umat4x2                                                    <a class="code" href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">umat4x2</a>;</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;        <span class="keyword">typedef</span> lowp_umat4x3                                                    <a class="code" href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">umat4x3</a>;</div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;        <span class="keyword">typedef</span> lowp_umat4x4                                                    <a class="code" href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">umat4x4</a>;</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;<span class="preprocessor">#else //if(defined(GLM_PRECISION_MEDIUMP_UINT))</span></div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;</div>
<div class="line"><a name="l00439"></a><span class="lineno"><a class="line" href="a00294.html#ga4cae85566f900debf930c41944b64691">  439</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat2                                                   <a class="code" href="a00294.html#ga4cae85566f900debf930c41944b64691">umat2</a>;</div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div>
<div class="line"><a name="l00443"></a><span class="lineno"><a class="line" href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">  443</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat3                                                   <a class="code" href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">umat3</a>;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;</div>
<div class="line"><a name="l00447"></a><span class="lineno"><a class="line" href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">  447</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat4                                                   <a class="code" href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">umat4</a>;</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;</div>
<div class="line"><a name="l00451"></a><span class="lineno"><a class="line" href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">  451</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat2x2                                                 <a class="code" href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">umat2x2</a>;</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;</div>
<div class="line"><a name="l00455"></a><span class="lineno"><a class="line" href="a00294.html#ga1870da7578d5022b973a83155d386ab3">  455</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat2x3                                                 <a class="code" href="a00294.html#ga1870da7578d5022b973a83155d386ab3">umat2x3</a>;</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;</div>
<div class="line"><a name="l00459"></a><span class="lineno"><a class="line" href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">  459</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat2x4                                                 <a class="code" href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">umat2x4</a>;</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;</div>
<div class="line"><a name="l00463"></a><span class="lineno"><a class="line" href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">  463</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat3x2                                                 <a class="code" href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">umat3x2</a>;</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;</div>
<div class="line"><a name="l00467"></a><span class="lineno"><a class="line" href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">  467</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat3x3                                                 <a class="code" href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">umat3x3</a>;</div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;</div>
<div class="line"><a name="l00471"></a><span class="lineno"><a class="line" href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">  471</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat3x4                                                 <a class="code" href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">umat3x4</a>;</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;</div>
<div class="line"><a name="l00475"></a><span class="lineno"><a class="line" href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">  475</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat4x2                                                 <a class="code" href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">umat4x2</a>;</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;</div>
<div class="line"><a name="l00479"></a><span class="lineno"><a class="line" href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">  479</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat4x3                                                 <a class="code" href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">umat4x3</a>;</div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;</div>
<div class="line"><a name="l00483"></a><span class="lineno"><a class="line" href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">  483</a></span>&#160;        <span class="keyword">typedef</span> mediump_umat4x4                                                 <a class="code" href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">umat4x4</a>;</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="ttc" id="a00294_html_ga43ed5e4f475b6f4cad7cba78f29c405b"><div class="ttname"><a href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">glm::imat4x4</a></div><div class="ttdeci">mediump_imat4x4 imat4x4</div><div class="ttdoc">Signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00406">matrix_integer.hpp:406</a></div></div>
<div class="ttc" id="a00294_html_gaa4732a240522ad9bc28144fda2fc14ec"><div class="ttname"><a href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">glm::imat2x2</a></div><div class="ttdeci">mediump_imat2x2 imat2x2</div><div class="ttdoc">Signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00374">matrix_integer.hpp:374</a></div></div>
<div class="ttc" id="a00294_html_ga38bc7bb6494e344185df596deeb4544c"><div class="ttname"><a href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">glm::umat4</a></div><div class="ttdeci">mediump_umat4 umat4</div><div class="ttdoc">Unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00447">matrix_integer.hpp:447</a></div></div>
<div class="ttc" id="a00294_html_ga70fa2d05896aa83cbc8c07672a429b53"><div class="ttname"><a href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">glm::umat4x2</a></div><div class="ttdeci">mediump_umat4x2 umat4x2</div><div class="ttdoc">Unsigned integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00475">matrix_integer.hpp:475</a></div></div>
<div class="ttc" id="a00294_html_ga10aafc66cf1a0ece336b1c5ae13d0cc0"><div class="ttname"><a href="a00294.html#ga10aafc66cf1a0ece336b1c5ae13d0cc0">glm::lowp_umat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, uint, lowp &gt; lowp_umat4x4</div><div class="ttdoc">Low-qualifier unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00330">matrix_integer.hpp:330</a></div></div>
<div class="ttc" id="a00294_html_ga8b2d35816f7103f0f4c82dd2f27571fc"><div class="ttname"><a href="a00294.html#ga8b2d35816f7103f0f4c82dd2f27571fc">glm::mediump_imat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, int, mediump &gt; mediump_imat4x2</div><div class="ttdoc">Medium-qualifier signed integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00123">matrix_integer.hpp:123</a></div></div>
<div class="ttc" id="a00294_html_gac092c6105827bf9ea080db38074b78eb"><div class="ttname"><a href="a00294.html#gac092c6105827bf9ea080db38074b78eb">glm::lowp_umat4</a></div><div class="ttdeci">mat&lt; 4, 4, uint, lowp &gt; lowp_umat4</div><div class="ttdoc">Low-qualifier unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00293">matrix_integer.hpp:293</a></div></div>
<div class="ttc" id="a00294_html_ga91c671c3ff9706c2393e78b22fd84bcb"><div class="ttname"><a href="a00294.html#ga91c671c3ff9706c2393e78b22fd84bcb">glm::highp_imat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, int, highp &gt; highp_imat3x2</div><div class="ttdoc">High-qualifier signed integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00061">matrix_integer.hpp:61</a></div></div>
<div class="ttc" id="a00294_html_ga1f7d41c36b980774a4d2e7c1647fb4b2"><div class="ttname"><a href="a00294.html#ga1f7d41c36b980774a4d2e7c1647fb4b2">glm::highp_umat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, uint, highp &gt; highp_umat3x3</div><div class="ttdoc">High-qualifier unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00214">matrix_integer.hpp:214</a></div></div>
<div class="ttc" id="a00294_html_ga7b2e9d89745f7175051284e54c81d81c"><div class="ttname"><a href="a00294.html#ga7b2e9d89745f7175051284e54c81d81c">glm::lowp_umat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, uint, lowp &gt; lowp_umat2x2</div><div class="ttdoc">Low-qualifier unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00298">matrix_integer.hpp:298</a></div></div>
<div class="ttc" id="a00294_html_ga1f2cfcf3357db0cdf31fcb15e3c6bafb"><div class="ttname"><a href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">glm::umat3x3</a></div><div class="ttdeci">mediump_umat3x3 umat3x3</div><div class="ttdoc">Unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00467">matrix_integer.hpp:467</a></div></div>
<div class="ttc" id="a00294_html_gadca0a4724b4a6f56a2355b6f6e19248b"><div class="ttname"><a href="a00294.html#gadca0a4724b4a6f56a2355b6f6e19248b">glm::highp_umat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, uint, highp &gt; highp_umat2x4</div><div class="ttdoc">High-qualifier unsigned integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00206">matrix_integer.hpp:206</a></div></div>
<div class="ttc" id="a00294_html_ga9cd7fa637a4a6788337f45231fad9e1a"><div class="ttname"><a href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">glm::umat3x2</a></div><div class="ttdeci">mediump_umat3x2 umat3x2</div><div class="ttdoc">Unsigned integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00463">matrix_integer.hpp:463</a></div></div>
<div class="ttc" id="a00294_html_ga33db8f27491d30906cd37c0d86b3f432"><div class="ttname"><a href="a00294.html#ga33db8f27491d30906cd37c0d86b3f432">glm::lowp_imat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, int, lowp &gt; lowp_imat3x2</div><div class="ttdoc">Low-qualifier signed integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00161">matrix_integer.hpp:161</a></div></div>
<div class="ttc" id="a00294_html_gaa1143120339b7d2d469d327662e8a172"><div class="ttname"><a href="a00294.html#gaa1143120339b7d2d469d327662e8a172">glm::highp_umat3</a></div><div class="ttdeci">mat&lt; 3, 3, uint, highp &gt; highp_umat3</div><div class="ttdoc">High-qualifier unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00190">matrix_integer.hpp:190</a></div></div>
<div class="ttc" id="a00294_html_ga5b10acc696759e03f6ab918f4467e94c"><div class="ttname"><a href="a00294.html#ga5b10acc696759e03f6ab918f4467e94c">glm::mediump_imat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, int, mediump &gt; mediump_imat4x3</div><div class="ttdoc">Medium-qualifier signed integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00127">matrix_integer.hpp:127</a></div></div>
<div class="ttc" id="a00294_html_ga038f68437155ffa3c2583a15264a8195"><div class="ttname"><a href="a00294.html#ga038f68437155ffa3c2583a15264a8195">glm::imat3</a></div><div class="ttdeci">mediump_imat3 imat3</div><div class="ttdoc">Signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00366">matrix_integer.hpp:366</a></div></div>
<div class="ttc" id="a00294_html_ga20f4cc7ab23e2aa1f4db9fdb5496d378"><div class="ttname"><a href="a00294.html#ga20f4cc7ab23e2aa1f4db9fdb5496d378">glm::mediump_imat2</a></div><div class="ttdeci">mat&lt; 2, 2, int, mediump &gt; mediump_imat2</div><div class="ttdoc">Medium-qualifier signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00086">matrix_integer.hpp:86</a></div></div>
<div class="ttc" id="a00294_html_ga78009a1e4ca64217e46b418535e52546"><div class="ttname"><a href="a00294.html#ga78009a1e4ca64217e46b418535e52546">glm::mediump_umat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, uint, mediump &gt; mediump_umat3x4</div><div class="ttdoc">Medium-qualifier unsigned integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00268">matrix_integer.hpp:268</a></div></div>
<div class="ttc" id="a00294_html_gaf65677e53ac8e31a107399340d5e2451"><div class="ttname"><a href="a00294.html#gaf65677e53ac8e31a107399340d5e2451">glm::lowp_imat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, int, lowp &gt; lowp_imat4x4</div><div class="ttdoc">Low-qualifier signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00181">matrix_integer.hpp:181</a></div></div>
<div class="ttc" id="a00294_html_ga05a970fd4366dad6c8a0be676b1eae5b"><div class="ttname"><a href="a00294.html#ga05a970fd4366dad6c8a0be676b1eae5b">glm::highp_imat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int, highp &gt; highp_imat2x4</div><div class="ttdoc">High-qualifier signed integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00057">matrix_integer.hpp:57</a></div></div>
<div class="ttc" id="a00294_html_ga1870da7578d5022b973a83155d386ab3"><div class="ttname"><a href="a00294.html#ga1870da7578d5022b973a83155d386ab3">glm::umat2x3</a></div><div class="ttdeci">mediump_umat2x3 umat2x3</div><div class="ttdoc">Unsigned integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00455">matrix_integer.hpp:455</a></div></div>
<div class="ttc" id="a00294_html_ga8f40483a3ae634ead8ad22272c543a33"><div class="ttname"><a href="a00294.html#ga8f40483a3ae634ead8ad22272c543a33">glm::lowp_imat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, int, lowp &gt; lowp_imat4x3</div><div class="ttdoc">Low-qualifier signed integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00177">matrix_integer.hpp:177</a></div></div>
<div class="ttc" id="a00294_html_gaf1145f72bcdd590f5808c4bc170c2924"><div class="ttname"><a href="a00294.html#gaf1145f72bcdd590f5808c4bc170c2924">glm::lowp_umat3</a></div><div class="ttdeci">mat&lt; 3, 3, uint, lowp &gt; lowp_umat3</div><div class="ttdoc">Low-qualifier unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00289">matrix_integer.hpp:289</a></div></div>
<div class="ttc" id="a00294_html_gaa9428fc8430dc552aad920653f822ef3"><div class="ttname"><a href="a00294.html#gaa9428fc8430dc552aad920653f822ef3">glm::mediump_umat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, uint, mediump &gt; mediump_umat4x4</div><div class="ttdoc">Medium-qualifier unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00280">matrix_integer.hpp:280</a></div></div>
<div class="ttc" id="a00294_html_gaadc28bfdc8ebca81ae85121b11994970"><div class="ttname"><a href="a00294.html#gaadc28bfdc8ebca81ae85121b11994970">glm::mediump_umat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, uint, mediump &gt; mediump_umat3x2</div><div class="ttdoc">Medium-qualifier unsigned integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00260">matrix_integer.hpp:260</a></div></div>
<div class="ttc" id="a00294_html_gae1317ddca16d01e119a40b7f0ee85f95"><div class="ttname"><a href="a00294.html#gae1317ddca16d01e119a40b7f0ee85f95">glm::mediump_umat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, uint, mediump &gt; mediump_umat2x4</div><div class="ttdoc">Medium-qualifier unsigned integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00256">matrix_integer.hpp:256</a></div></div>
<div class="ttc" id="a00294_html_ga2c783ee6f8f040ab37df2f70392c8b44"><div class="ttname"><a href="a00294.html#ga2c783ee6f8f040ab37df2f70392c8b44">glm::highp_imat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, int, highp &gt; highp_imat4x4</div><div class="ttdoc">High-qualifier signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00081">matrix_integer.hpp:81</a></div></div>
<div class="ttc" id="a00294_html_ga8bb45fec4bd77bd81b4ae7eb961a270d"><div class="ttname"><a href="a00294.html#ga8bb45fec4bd77bd81b4ae7eb961a270d">glm::lowp_umat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, uint, lowp &gt; lowp_umat2x4</div><div class="ttdoc">Low-qualifier unsigned integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00306">matrix_integer.hpp:306</a></div></div>
<div class="ttc" id="a00294_html_ga705ee0bf49d6c3de4404ce2481bf0df5"><div class="ttname"><a href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">glm::imat4x3</a></div><div class="ttdeci">mediump_imat4x3 imat4x3</div><div class="ttdoc">Signed integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00402">matrix_integer.hpp:402</a></div></div>
<div class="ttc" id="a00294_html_ga48f2fc38d3f7fab3cfbc961278ced53d"><div class="ttname"><a href="a00294.html#ga48f2fc38d3f7fab3cfbc961278ced53d">glm::mediump_umat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, uint, mediump &gt; mediump_umat3x3</div><div class="ttdoc">Medium-qualifier unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00264">matrix_integer.hpp:264</a></div></div>
<div class="ttc" id="a00294_html_ga8499cc3b016003f835314c1c756e9db9"><div class="ttname"><a href="a00294.html#ga8499cc3b016003f835314c1c756e9db9">glm::highp_imat2</a></div><div class="ttdeci">mat&lt; 2, 2, int, highp &gt; highp_imat2</div><div class="ttdoc">High-qualifier signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00037">matrix_integer.hpp:37</a></div></div>
<div class="ttc" id="a00294_html_ga4cae85566f900debf930c41944b64691"><div class="ttname"><a href="a00294.html#ga4cae85566f900debf930c41944b64691">glm::umat2</a></div><div class="ttdeci">mediump_umat2 umat2</div><div class="ttdoc">Unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00439">matrix_integer.hpp:439</a></div></div>
<div class="ttc" id="a00294_html_gaec3d624306bd59649f021864709d56b5"><div class="ttname"><a href="a00294.html#gaec3d624306bd59649f021864709d56b5">glm::lowp_umat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, uint, lowp &gt; lowp_umat3x4</div><div class="ttdoc">Low-qualifier unsigned integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00318">matrix_integer.hpp:318</a></div></div>
<div class="ttc" id="a00294_html_gaf35aefd81cc13718f6b059623f7425fa"><div class="ttname"><a href="a00294.html#gaf35aefd81cc13718f6b059623f7425fa">glm::mediump_umat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, uint, mediump &gt; mediump_umat4x2</div><div class="ttdoc">Medium-qualifier unsigned integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00272">matrix_integer.hpp:272</a></div></div>
<div class="ttc" id="a00294_html_ga8ce7ef51d8b2c1901fa5414deccbc3fa"><div class="ttname"><a href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">glm::imat4x2</a></div><div class="ttdeci">mediump_imat4x2 imat4x2</div><div class="ttdoc">Signed integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00398">matrix_integer.hpp:398</a></div></div>
<div class="ttc" id="a00294_html_ga74362470ba99843ac70aee5ac38cc674"><div class="ttname"><a href="a00294.html#ga74362470ba99843ac70aee5ac38cc674">glm::mediump_imat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, int, mediump &gt; mediump_imat2x3</div><div class="ttdoc">Medium-qualifier signed integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00103">matrix_integer.hpp:103</a></div></div>
<div class="ttc" id="a00294_html_ga43041378b3410ea951b7de0dfd2bc7ee"><div class="ttname"><a href="a00294.html#ga43041378b3410ea951b7de0dfd2bc7ee">glm::mediump_umat2</a></div><div class="ttdeci">mat&lt; 2, 2, uint, mediump &gt; mediump_umat2</div><div class="ttdoc">Medium-qualifier unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00235">matrix_integer.hpp:235</a></div></div>
<div class="ttc" id="a00294_html_gaabe04f9948d4a213bb1c20137de03e01"><div class="ttname"><a href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">glm::imat2</a></div><div class="ttdeci">mediump_imat2 imat2</div><div class="ttdoc">Signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00362">matrix_integer.hpp:362</a></div></div>
<div class="ttc" id="a00294_html_ga4e1bed14fbc7f4b376aaed064f89f0fb"><div class="ttname"><a href="a00294.html#ga4e1bed14fbc7f4b376aaed064f89f0fb">glm::mediump_umat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, uint, mediump &gt; mediump_umat4x3</div><div class="ttdoc">Medium-qualifier unsigned integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00276">matrix_integer.hpp:276</a></div></div>
<div class="ttc" id="a00294_html_ga6c63bdc736efd3466e0730de0251cb71"><div class="ttname"><a href="a00294.html#ga6c63bdc736efd3466e0730de0251cb71">glm::mediump_imat3</a></div><div class="ttdeci">mat&lt; 3, 3, int, mediump &gt; mediump_imat3</div><div class="ttdoc">Medium-qualifier signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00090">matrix_integer.hpp:90</a></div></div>
<div class="ttc" id="a00294_html_ga42cbce64c4c1cd121b8437daa6e110de"><div class="ttname"><a href="a00294.html#ga42cbce64c4c1cd121b8437daa6e110de">glm::highp_umat2</a></div><div class="ttdeci">mat&lt; 2, 2, uint, highp &gt; highp_umat2</div><div class="ttdoc">High-qualifier unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00186">matrix_integer.hpp:186</a></div></div>
<div class="ttc" id="a00294_html_ga6e9ce23496d8b08dfc302d4039694b58"><div class="ttname"><a href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">glm::imat3x4</a></div><div class="ttdeci">mediump_imat3x4 imat3x4</div><div class="ttdoc">Signed integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00394">matrix_integer.hpp:394</a></div></div>
<div class="ttc" id="a00294_html_ga844a5da2e7fc03fc7cccc7f1b70809c4"><div class="ttname"><a href="a00294.html#ga844a5da2e7fc03fc7cccc7f1b70809c4">glm::highp_umat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, uint, highp &gt; highp_umat3x2</div><div class="ttdoc">High-qualifier unsigned integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00210">matrix_integer.hpp:210</a></div></div>
<div class="ttc" id="a00294_html_gaa389e2d1c3b10941cae870bc0aeba5b3"><div class="ttname"><a href="a00294.html#gaa389e2d1c3b10941cae870bc0aeba5b3">glm::highp_imat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, int, highp &gt; highp_imat2x2</div><div class="ttdoc">High-qualifier signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00049">matrix_integer.hpp:49</a></div></div>
<div class="ttc" id="a00294_html_ga25ee15c323924f2d0fe9896d329e5086"><div class="ttname"><a href="a00294.html#ga25ee15c323924f2d0fe9896d329e5086">glm::highp_umat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, uint, highp &gt; highp_umat3x4</div><div class="ttdoc">High-qualifier unsigned integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00218">matrix_integer.hpp:218</a></div></div>
<div class="ttc" id="a00294_html_gad99cc2aad8fc57f068cfa7719dbbea12"><div class="ttname"><a href="a00294.html#gad99cc2aad8fc57f068cfa7719dbbea12">glm::mediump_imat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int, mediump &gt; mediump_imat3x3</div><div class="ttdoc">Medium-qualifier signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00115">matrix_integer.hpp:115</a></div></div>
<div class="ttc" id="a00294_html_ga6a56cbb769aed334c95241664415f9ba"><div class="ttname"><a href="a00294.html#ga6a56cbb769aed334c95241664415f9ba">glm::highp_umat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, uint, highp &gt; highp_umat4x4</div><div class="ttdoc">High-qualifier unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00230">matrix_integer.hpp:230</a></div></div>
<div class="ttc" id="a00294_html_ga9d8faafdca42583d67e792dd038fc668"><div class="ttname"><a href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">glm::imat2x4</a></div><div class="ttdeci">mediump_imat2x4 imat2x4</div><div class="ttdoc">Signed integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00382">matrix_integer.hpp:382</a></div></div>
<div class="ttc" id="a00294_html_ga57936a3998e992370e59a223e0ee4fd4"><div class="ttname"><a href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">glm::umat2x4</a></div><div class="ttdeci">mediump_umat2x4 umat2x4</div><div class="ttdoc">Unsigned integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00459">matrix_integer.hpp:459</a></div></div>
<div class="ttc" id="a00294_html_ga8da25cd380ba30fc5b68a4687deb3e09"><div class="ttname"><a href="a00294.html#ga8da25cd380ba30fc5b68a4687deb3e09">glm::mediump_imat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int, mediump &gt; mediump_imat2x4</div><div class="ttdoc">Medium-qualifier signed integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00107">matrix_integer.hpp:107</a></div></div>
<div class="ttc" id="a00294_html_gaa0bff0be804142bb16d441aec0a7962e"><div class="ttname"><a href="a00294.html#gaa0bff0be804142bb16d441aec0a7962e">glm::lowp_imat2</a></div><div class="ttdeci">mat&lt; 2, 2, int, lowp &gt; lowp_imat2</div><div class="ttdoc">Low-qualifier signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00136">matrix_integer.hpp:136</a></div></div>
<div class="ttc" id="a00294_html_ga26ec1a2ba08a1488f5f05336858a0f09"><div class="ttname"><a href="a00294.html#ga26ec1a2ba08a1488f5f05336858a0f09">glm::lowp_imat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, int, lowp &gt; lowp_imat4x2</div><div class="ttdoc">Low-qualifier signed integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00173">matrix_integer.hpp:173</a></div></div>
<div class="ttc" id="a00294_html_ga09ab33a2636f5f43f7fae29cfbc20fff"><div class="ttname"><a href="a00294.html#ga09ab33a2636f5f43f7fae29cfbc20fff">glm::lowp_umat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, uint, lowp &gt; lowp_umat4x3</div><div class="ttdoc">Low-qualifier unsigned integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00326">matrix_integer.hpp:326</a></div></div>
<div class="ttc" id="a00294_html_ga96b0d26a33b81bb6a60ca0f39682f7eb"><div class="ttname"><a href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">glm::imat4</a></div><div class="ttdeci">mediump_imat4 imat4</div><div class="ttdoc">Signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00370">matrix_integer.hpp:370</a></div></div>
<div class="ttc" id="a00294_html_ga7b33bbe4f12c060892bd3cc8d4cd737f"><div class="ttname"><a href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">glm::imat3x2</a></div><div class="ttdeci">mediump_imat3x2 imat3x2</div><div class="ttdoc">Signed integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00386">matrix_integer.hpp:386</a></div></div>
<div class="ttc" id="a00294_html_ga3072f90fd86f17a862e21589fbb14c0f"><div class="ttname"><a href="a00294.html#ga3072f90fd86f17a862e21589fbb14c0f">glm::lowp_umat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, uint, lowp &gt; lowp_umat2x3</div><div class="ttdoc">Low-qualifier unsigned integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00302">matrix_integer.hpp:302</a></div></div>
<div class="ttc" id="a00294_html_gac0b4e42d648fb3eaf4bb88da82ecc809"><div class="ttname"><a href="a00294.html#gac0b4e42d648fb3eaf4bb88da82ecc809">glm::mediump_imat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, int, mediump &gt; mediump_imat3x2</div><div class="ttdoc">Medium-qualifier signed integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00111">matrix_integer.hpp:111</a></div></div>
<div class="ttc" id="a00294_html_gaf72e6d399c42985db6872c50f53d7eb8"><div class="ttname"><a href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">glm::umat4x4</a></div><div class="ttdeci">mediump_umat4x4 umat4x4</div><div class="ttdoc">Unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00483">matrix_integer.hpp:483</a></div></div>
<div class="ttc" id="a00294_html_ga6be0b80ae74bb309bc5b964d93d68fc5"><div class="ttname"><a href="a00294.html#ga6be0b80ae74bb309bc5b964d93d68fc5">glm::highp_imat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, int, highp &gt; highp_imat4x3</div><div class="ttdoc">High-qualifier signed integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00077">matrix_integer.hpp:77</a></div></div>
<div class="ttc" id="a00294_html_ga87581417945411f75cb31dd6ca1dba98"><div class="ttname"><a href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">glm::umat4x3</a></div><div class="ttdeci">mediump_umat4x3 umat4x3</div><div class="ttdoc">Unsigned integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00479">matrix_integer.hpp:479</a></div></div>
<div class="ttc" id="a00294_html_ga7716c2b210d141846f1ac4e774adef5e"><div class="ttname"><a href="a00294.html#ga7716c2b210d141846f1ac4e774adef5e">glm::lowp_umat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, uint, lowp &gt; lowp_umat4x2</div><div class="ttdoc">Low-qualifier unsigned integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00322">matrix_integer.hpp:322</a></div></div>
<div class="ttc" id="a00294_html_ga56ea68c6a6cba8d8c21d17bb14e69c6b"><div class="ttname"><a href="a00294.html#ga56ea68c6a6cba8d8c21d17bb14e69c6b">glm::lowp_umat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, uint, lowp &gt; lowp_umat3x2</div><div class="ttdoc">Low-qualifier unsigned integer 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00310">matrix_integer.hpp:310</a></div></div>
<div class="ttc" id="a00294_html_ga5337b7bc95f9cbac08a0c00b3f936b28"><div class="ttname"><a href="a00294.html#ga5337b7bc95f9cbac08a0c00b3f936b28">glm::highp_umat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, uint, highp &gt; highp_umat2x2</div><div class="ttdoc">High-qualifier unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00198">matrix_integer.hpp:198</a></div></div>
<div class="ttc" id="a00294_html_ga664f061df00020048c3f8530329ace45"><div class="ttname"><a href="a00294.html#ga664f061df00020048c3f8530329ace45">glm::lowp_imat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int, lowp &gt; lowp_imat3x3</div><div class="ttdoc">Low-qualifier signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00165">matrix_integer.hpp:165</a></div></div>
<div class="ttc" id="a00294_html_ga07d7b7173e2a6f843ff5f1c615a95b41"><div class="ttname"><a href="a00294.html#ga07d7b7173e2a6f843ff5f1c615a95b41">glm::highp_imat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int, highp &gt; highp_imat3x3</div><div class="ttdoc">High-qualifier signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00065">matrix_integer.hpp:65</a></div></div>
<div class="ttc" id="a00294_html_gaee2c1f13b41f4c92ea5b3efe367a1306"><div class="ttname"><a href="a00294.html#gaee2c1f13b41f4c92ea5b3efe367a1306">glm::mediump_umat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, uint, mediump &gt; mediump_umat2x3</div><div class="ttdoc">Medium-qualifier unsigned integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00252">matrix_integer.hpp:252</a></div></div>
<div class="ttc" id="a00294_html_gae69eb82ec08b0dc9bf2ead2a339ff801"><div class="ttname"><a href="a00294.html#gae69eb82ec08b0dc9bf2ead2a339ff801">glm::highp_umat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, uint, highp &gt; highp_umat4x2</div><div class="ttdoc">High-qualifier unsigned integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00222">matrix_integer.hpp:222</a></div></div>
<div class="ttc" id="a00294_html_ga4f660a39a395cc14f018f985e7dfbeb5"><div class="ttname"><a href="a00294.html#ga4f660a39a395cc14f018f985e7dfbeb5">glm::lowp_umat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, uint, lowp &gt; lowp_umat3x3</div><div class="ttdoc">Low-qualifier unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00314">matrix_integer.hpp:314</a></div></div>
<div class="ttc" id="a00294_html_ga3f42dd3d5d94a0fd5706f7ec8dd0c605"><div class="ttname"><a href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">glm::imat2x3</a></div><div class="ttdeci">mediump_imat2x3 imat2x3</div><div class="ttdoc">Signed integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00378">matrix_integer.hpp:378</a></div></div>
<div class="ttc" id="a00294_html_ga8c9e7a388f8e7c52f1e6857dee8afb65"><div class="ttname"><a href="a00294.html#ga8c9e7a388f8e7c52f1e6857dee8afb65">glm::lowp_imat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, int, lowp &gt; lowp_imat2x3</div><div class="ttdoc">Low-qualifier signed integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00153">matrix_integer.hpp:153</a></div></div>
<div class="ttc" id="a00294_html_gaf665e4e78c2cc32a54ab40325738f9c9"><div class="ttname"><a href="a00294.html#gaf665e4e78c2cc32a54ab40325738f9c9">glm::highp_umat4</a></div><div class="ttdeci">mat&lt; 4, 4, uint, highp &gt; highp_umat4</div><div class="ttdoc">High-qualifier unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00194">matrix_integer.hpp:194</a></div></div>
<div class="ttc" id="a00294_html_gaca4506a3efa679eff7c006d9826291fd"><div class="ttname"><a href="a00294.html#gaca4506a3efa679eff7c006d9826291fd">glm::highp_imat3</a></div><div class="ttdeci">mat&lt; 3, 3, int, highp &gt; highp_imat3</div><div class="ttdoc">High-qualifier signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00041">matrix_integer.hpp:41</a></div></div>
<div class="ttc" id="a00294_html_ga1730dbe3c67801f53520b06d1aa0a34a"><div class="ttname"><a href="a00294.html#ga1730dbe3c67801f53520b06d1aa0a34a">glm::mediump_umat3</a></div><div class="ttdeci">mat&lt; 3, 3, uint, mediump &gt; mediump_umat3</div><div class="ttdoc">Medium-qualifier unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00239">matrix_integer.hpp:239</a></div></div>
<div class="ttc" id="a00294_html_ga4b2aeb11a329940721dda9583e71f856"><div class="ttname"><a href="a00294.html#ga4b2aeb11a329940721dda9583e71f856">glm::mediump_imat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, int, mediump &gt; mediump_imat2x2</div><div class="ttdoc">Medium-qualifier signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00099">matrix_integer.hpp:99</a></div></div>
<div class="ttc" id="a00294_html_gaba49d890e06c9444795f5a133fbf1336"><div class="ttname"><a href="a00294.html#gaba49d890e06c9444795f5a133fbf1336">glm::highp_imat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, int, highp &gt; highp_imat2x3</div><div class="ttdoc">High-qualifier signed integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00053">matrix_integer.hpp:53</a></div></div>
<div class="ttc" id="a00294_html_ga1858820fb292cae396408b2034407f72"><div class="ttname"><a href="a00294.html#ga1858820fb292cae396408b2034407f72">glm::highp_imat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, int, highp &gt; highp_imat4x2</div><div class="ttdoc">High-qualifier signed integer 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00073">matrix_integer.hpp:73</a></div></div>
<div class="ttc" id="a00294_html_ga9273faab33623d944af4080befbb2c80"><div class="ttname"><a href="a00294.html#ga9273faab33623d944af4080befbb2c80">glm::lowp_imat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, int, lowp &gt; lowp_imat3x4</div><div class="ttdoc">Low-qualifier signed integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00169">matrix_integer.hpp:169</a></div></div>
<div class="ttc" id="a00294_html_ga5085e3ff02abbac5e537eb7b89ab63b6"><div class="ttname"><a href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">glm::umat3</a></div><div class="ttdeci">mediump_umat3 umat3</div><div class="ttdoc">Unsigned integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00443">matrix_integer.hpp:443</a></div></div>
<div class="ttc" id="a00294_html_ga92b95b679975d408645547ab45a8dcd8"><div class="ttname"><a href="a00294.html#ga92b95b679975d408645547ab45a8dcd8">glm::lowp_imat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, int, lowp &gt; lowp_imat2x2</div><div class="ttdoc">Low-qualifier signed integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00149">matrix_integer.hpp:149</a></div></div>
<div class="ttc" id="a00294_html_ga90718c7128320b24b52f9ea70e643ad4"><div class="ttname"><a href="a00294.html#ga90718c7128320b24b52f9ea70e643ad4">glm::highp_umat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, uint, highp &gt; highp_umat2x3</div><div class="ttdoc">High-qualifier unsigned integer 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00202">matrix_integer.hpp:202</a></div></div>
<div class="ttc" id="a00294_html_ga7cfb09b34e0fcf73eaf6512d6483ef56"><div class="ttname"><a href="a00294.html#ga7cfb09b34e0fcf73eaf6512d6483ef56">glm::highp_imat4</a></div><div class="ttdeci">mat&lt; 4, 4, int, highp &gt; highp_imat4</div><div class="ttdoc">High-qualifier signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00045">matrix_integer.hpp:45</a></div></div>
<div class="ttc" id="a00294_html_ga9cc13bd1f8dd2933e9fa31fe3f70e16e"><div class="ttname"><a href="a00294.html#ga9cc13bd1f8dd2933e9fa31fe3f70e16e">glm::lowp_imat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int, lowp &gt; lowp_imat2x4</div><div class="ttdoc">Low-qualifier signed integer 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00157">matrix_integer.hpp:157</a></div></div>
<div class="ttc" id="a00294_html_ga67689a518b181a26540bc44a163525cd"><div class="ttname"><a href="a00294.html#ga67689a518b181a26540bc44a163525cd">glm::mediump_imat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, int, mediump &gt; mediump_imat3x4</div><div class="ttdoc">Medium-qualifier signed integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00119">matrix_integer.hpp:119</a></div></div>
<div class="ttc" id="a00294_html_ga2596869d154dec1180beadbb9df80501"><div class="ttname"><a href="a00294.html#ga2596869d154dec1180beadbb9df80501">glm::mediump_imat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, int, mediump &gt; mediump_imat4x4</div><div class="ttdoc">Medium-qualifier signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00131">matrix_integer.hpp:131</a></div></div>
<div class="ttc" id="a00294_html_gaf348552978553630d2a00b78eb887ced"><div class="ttname"><a href="a00294.html#gaf348552978553630d2a00b78eb887ced">glm::mediump_imat4</a></div><div class="ttdeci">mat&lt; 4, 4, int, mediump &gt; mediump_imat4</div><div class="ttdoc">Medium-qualifier signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00094">matrix_integer.hpp:94</a></div></div>
<div class="ttc" id="a00294_html_ga6aacc960f62e8f7d2fe9d32d5050e7a4"><div class="ttname"><a href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">glm::imat3x3</a></div><div class="ttdeci">mediump_imat3x3 imat3x3</div><div class="ttdoc">Signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00390">matrix_integer.hpp:390</a></div></div>
<div class="ttc" id="a00294_html_ga69bfe668f4170379fc1f35d82b060c43"><div class="ttname"><a href="a00294.html#ga69bfe668f4170379fc1f35d82b060c43">glm::lowp_imat3</a></div><div class="ttdeci">mat&lt; 3, 3, int, lowp &gt; lowp_imat3</div><div class="ttdoc">Low-qualifier signed integer 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00140">matrix_integer.hpp:140</a></div></div>
<div class="ttc" id="a00294_html_gaf2fba702d990437fc88ff3f3a76846ee"><div class="ttname"><a href="a00294.html#gaf2fba702d990437fc88ff3f3a76846ee">glm::lowp_umat2</a></div><div class="ttdeci">mat&lt; 2, 2, uint, lowp &gt; lowp_umat2</div><div class="ttdoc">Low-qualifier unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00285">matrix_integer.hpp:285</a></div></div>
<div class="ttc" id="a00294_html_ga45a8163d02c43216252056b0c120f3a5"><div class="ttname"><a href="a00294.html#ga45a8163d02c43216252056b0c120f3a5">glm::highp_umat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, uint, highp &gt; highp_umat4x3</div><div class="ttdoc">High-qualifier unsigned integer 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00226">matrix_integer.hpp:226</a></div></div>
<div class="ttc" id="a00294_html_gabf8acdd33ce8951051edbca5200898aa"><div class="ttname"><a href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">glm::umat2x2</a></div><div class="ttdeci">mediump_umat2x2 umat2x2</div><div class="ttdoc">Unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00451">matrix_integer.hpp:451</a></div></div>
<div class="ttc" id="a00294_html_ga5087c2beb26a11d9af87432e554cf9d1"><div class="ttname"><a href="a00294.html#ga5087c2beb26a11d9af87432e554cf9d1">glm::mediump_umat4</a></div><div class="ttdeci">mat&lt; 4, 4, uint, mediump &gt; mediump_umat4</div><div class="ttdoc">Medium-qualifier unsigned integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00243">matrix_integer.hpp:243</a></div></div>
<div class="ttc" id="a00294_html_gad1e77f7270cad461ca4fcb4c3ec2e98c"><div class="ttname"><a href="a00294.html#gad1e77f7270cad461ca4fcb4c3ec2e98c">glm::lowp_imat4</a></div><div class="ttdeci">mat&lt; 4, 4, int, lowp &gt; lowp_imat4</div><div class="ttdoc">Low-qualifier signed integer 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00144">matrix_integer.hpp:144</a></div></div>
<div class="ttc" id="a00294_html_gae7c78ff3fc4309605ab0fa186c8d48ba"><div class="ttname"><a href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">glm::umat3x4</a></div><div class="ttdeci">mediump_umat3x4 umat3x4</div><div class="ttdoc">Unsigned integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00471">matrix_integer.hpp:471</a></div></div>
<div class="ttc" id="a00294_html_ga53008f580be99018a17b357b5a4ffc0d"><div class="ttname"><a href="a00294.html#ga53008f580be99018a17b357b5a4ffc0d">glm::highp_imat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, int, highp &gt; highp_imat3x4</div><div class="ttdoc">High-qualifier signed integer 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00069">matrix_integer.hpp:69</a></div></div>
<div class="ttc" id="a00294_html_ga3b209b1b751f041422137e3c065dfa98"><div class="ttname"><a href="a00294.html#ga3b209b1b751f041422137e3c065dfa98">glm::mediump_umat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, uint, mediump &gt; mediump_umat2x2</div><div class="ttdoc">Medium-qualifier unsigned integer 2x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00100_source.html#l00248">matrix_integer.hpp:248</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
