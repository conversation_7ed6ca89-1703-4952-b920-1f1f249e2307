<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_relational.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">matrix_relational.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00246.html">GLM_EXT_matrix_relational</a>  
<a href="#details">More...</a></p>

<p><a href="a00107_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga27e90dcb7941c9b70e295dc3f6f6369f"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga27e90dcb7941c9b70e295dc3f6f6369f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#ga27e90dcb7941c9b70e295dc3f6f6369f">equal</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga27e90dcb7941c9b70e295dc3f6f6369f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Perform a component-wise equal-to comparison of two matrices.  <a href="a00246.html#ga27e90dcb7941c9b70e295dc3f6f6369f">More...</a><br /></td></tr>
<tr class="separator:ga27e90dcb7941c9b70e295dc3f6f6369f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5d687d70d11708b68c36c6db5777040"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf5d687d70d11708b68c36c6db5777040"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#gaf5d687d70d11708b68c36c6db5777040">equal</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:gaf5d687d70d11708b68c36c6db5777040"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00246.html#gaf5d687d70d11708b68c36c6db5777040">More...</a><br /></td></tr>
<tr class="separator:gaf5d687d70d11708b68c36c6db5777040"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa6a053e81179fa4292b35651c83c3fb"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafa6a053e81179fa4292b35651c83c3fb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#gafa6a053e81179fa4292b35651c83c3fb">equal</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, T, Q &gt; const &amp;epsilon)</td></tr>
<tr class="memdesc:gafa6a053e81179fa4292b35651c83c3fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00246.html#gafa6a053e81179fa4292b35651c83c3fb">More...</a><br /></td></tr>
<tr class="separator:gafa6a053e81179fa4292b35651c83c3fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3a93f19e72e9141f50527c9de21d0c0"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab3a93f19e72e9141f50527c9de21d0c0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#gab3a93f19e72e9141f50527c9de21d0c0">equal</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, int ULPs)</td></tr>
<tr class="memdesc:gab3a93f19e72e9141f50527c9de21d0c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00246.html#gab3a93f19e72e9141f50527c9de21d0c0">More...</a><br /></td></tr>
<tr class="separator:gab3a93f19e72e9141f50527c9de21d0c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5305af376173f1902719fa309bbae671"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5305af376173f1902719fa309bbae671"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#ga5305af376173f1902719fa309bbae671">equal</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:ga5305af376173f1902719fa309bbae671"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00246.html#ga5305af376173f1902719fa309bbae671">More...</a><br /></td></tr>
<tr class="separator:ga5305af376173f1902719fa309bbae671"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8504f18a7e2bf315393032c2137dad83"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8504f18a7e2bf315393032c2137dad83"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#ga8504f18a7e2bf315393032c2137dad83">notEqual</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga8504f18a7e2bf315393032c2137dad83"><td class="mdescLeft">&#160;</td><td class="mdescRight">Perform a component-wise not-equal-to comparison of two matrices.  <a href="a00246.html#ga8504f18a7e2bf315393032c2137dad83">More...</a><br /></td></tr>
<tr class="separator:ga8504f18a7e2bf315393032c2137dad83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga29071147d118569344d10944b7d5c378"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga29071147d118569344d10944b7d5c378"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#ga29071147d118569344d10944b7d5c378">notEqual</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:ga29071147d118569344d10944b7d5c378"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00246.html#ga29071147d118569344d10944b7d5c378">More...</a><br /></td></tr>
<tr class="separator:ga29071147d118569344d10944b7d5c378"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7959e14fbc35b4ed2617daf4d67f6cd"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad7959e14fbc35b4ed2617daf4d67f6cd"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#gad7959e14fbc35b4ed2617daf4d67f6cd">notEqual</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, T, Q &gt; const &amp;epsilon)</td></tr>
<tr class="memdesc:gad7959e14fbc35b4ed2617daf4d67f6cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00246.html#gad7959e14fbc35b4ed2617daf4d67f6cd">More...</a><br /></td></tr>
<tr class="separator:gad7959e14fbc35b4ed2617daf4d67f6cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa1cd7fc228ef6e26c73583fd0d9c6552"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa1cd7fc228ef6e26c73583fd0d9c6552"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#gaa1cd7fc228ef6e26c73583fd0d9c6552">notEqual</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, int ULPs)</td></tr>
<tr class="memdesc:gaa1cd7fc228ef6e26c73583fd0d9c6552"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00246.html#gaa1cd7fc228ef6e26c73583fd0d9c6552">More...</a><br /></td></tr>
<tr class="separator:gaa1cd7fc228ef6e26c73583fd0d9c6552"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5517341754149ffba742d230afd1f32"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa5517341754149ffba742d230afd1f32"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; C, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00246.html#gaa5517341754149ffba742d230afd1f32">notEqual</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:gaa5517341754149ffba742d230afd1f32"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00246.html#gaa5517341754149ffba742d230afd1f32">More...</a><br /></td></tr>
<tr class="separator:gaa5517341754149ffba742d230afd1f32"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00246.html">GLM_EXT_matrix_relational</a> </p>

<p>Definition in file <a class="el" href="a00107_source.html">matrix_relational.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
