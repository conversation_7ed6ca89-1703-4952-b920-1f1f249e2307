<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: scalar_common.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">scalar_common.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00258.html">GLM_EXT_scalar_common</a>  
<a href="#details">More...</a></p>

<p><a href="a00144_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga36920478565cf608e93064283ce06421"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga36920478565cf608e93064283ce06421"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga36920478565cf608e93064283ce06421">fmax</a> (T a, T b)</td></tr>
<tr class="memdesc:ga36920478565cf608e93064283ce06421"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 2 inputs.  <a href="a00258.html#ga36920478565cf608e93064283ce06421">More...</a><br /></td></tr>
<tr class="separator:ga36920478565cf608e93064283ce06421"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga0007bba71ca451ac70e99d28dfbeaab9">fmax</a> (T a, T b, T C)</td></tr>
<tr class="memdesc:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 3 inputs.  <a href="a00258.html#ga0007bba71ca451ac70e99d28dfbeaab9">More...</a><br /></td></tr>
<tr class="separator:ga0007bba71ca451ac70e99d28dfbeaab9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">fmax</a> (T a, T b, T C, T D)</td></tr>
<tr class="memdesc:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 4 inputs.  <a href="a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08">More...</a><br /></td></tr>
<tr class="separator:ga27e260b1ff4d04c3ad4b864d26cbaf08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b2b438a765e2a62098c79eb212f28f0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga7b2b438a765e2a62098c79eb212f28f0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga7b2b438a765e2a62098c79eb212f28f0">fmin</a> (T a, T b)</td></tr>
<tr class="memdesc:ga7b2b438a765e2a62098c79eb212f28f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 2 inputs.  <a href="a00258.html#ga7b2b438a765e2a62098c79eb212f28f0">More...</a><br /></td></tr>
<tr class="separator:ga7b2b438a765e2a62098c79eb212f28f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga1a95fe4cf5437e8133f1093fe9726a64">fmin</a> (T a, T b, T c)</td></tr>
<tr class="memdesc:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 3 inputs.  <a href="a00258.html#ga1a95fe4cf5437e8133f1093fe9726a64">More...</a><br /></td></tr>
<tr class="separator:ga1a95fe4cf5437e8133f1093fe9726a64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">fmin</a> (T a, T b, T c, T d)</td></tr>
<tr class="memdesc:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 4 inputs.  <a href="a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661">More...</a><br /></td></tr>
<tr class="separator:ga3d6f9c6c16bfd6f38f2c4f8076e8b661"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa20839d9ab14514f8966f69877ea0de8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa20839d9ab14514f8966f69877ea0de8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#gaa20839d9ab14514f8966f69877ea0de8">max</a> (T a, T b, T c)</td></tr>
<tr class="memdesc:gaa20839d9ab14514f8966f69877ea0de8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 3 inputs.  <a href="a00258.html#gaa20839d9ab14514f8966f69877ea0de8">More...</a><br /></td></tr>
<tr class="separator:gaa20839d9ab14514f8966f69877ea0de8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">max</a> (T a, T b, T c, T d)</td></tr>
<tr class="memdesc:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the maximum component-wise values of 4 inputs.  <a href="a00258.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67">More...</a><br /></td></tr>
<tr class="separator:ga2274b5e75ed84b0b1e50d8d22f1f2f67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga420b37cbd98c395b93dab0278305cd46"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga420b37cbd98c395b93dab0278305cd46"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga420b37cbd98c395b93dab0278305cd46">min</a> (T a, T b, T c)</td></tr>
<tr class="memdesc:ga420b37cbd98c395b93dab0278305cd46"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 3 inputs.  <a href="a00258.html#ga420b37cbd98c395b93dab0278305cd46">More...</a><br /></td></tr>
<tr class="separator:ga420b37cbd98c395b93dab0278305cd46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00258.html#ga0d24a9acb8178df77e4aff90cbb2010d">min</a> (T a, T b, T c, T d)</td></tr>
<tr class="memdesc:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the minimum component-wise values of 4 inputs.  <a href="a00258.html#ga0d24a9acb8178df77e4aff90cbb2010d">More...</a><br /></td></tr>
<tr class="separator:ga0d24a9acb8178df77e4aff90cbb2010d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00258.html">GLM_EXT_scalar_common</a> </p>

<p>Definition in file <a class="el" href="a00144_source.html">scalar_common.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
