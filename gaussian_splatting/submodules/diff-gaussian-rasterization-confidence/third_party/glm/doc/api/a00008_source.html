<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: bit.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">bit.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00008.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../gtc/bitfield.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_bit is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_bit extension included&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00309.html#ga898ef075ccf809a1e480faab48fe96bf">highestBitValue</a>(genIUType Value);</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00309.html#ga2ff6568089f3a9b67f5c30918855fc6f">lowestBitValue</a>(genIUType Value);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00309.html#ga898ef075ccf809a1e480faab48fe96bf">highestBitValue</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL genIUType <a class="code" href="a00309.html#ga2bbded187c5febfefc1e524ba31b3fab">powerOfTwoAbove</a>(genIUType Value);</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00309.html#ga2bbded187c5febfefc1e524ba31b3fab">powerOfTwoAbove</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL genIUType <a class="code" href="a00309.html#gaf78ddcc4152c051b2a21e68fecb10980">powerOfTwoBelow</a>(genIUType Value);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00309.html#gaf78ddcc4152c051b2a21e68fecb10980">powerOfTwoBelow</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL genIUType <a class="code" href="a00309.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">powerOfTwoNearest</a>(genIUType Value);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;        GLM_DEPRECATED GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00309.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">powerOfTwoNearest</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; value);</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor">#include &quot;bit.inl&quot;</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div>
<div class="ttc" id="a00309_html_ga898ef075ccf809a1e480faab48fe96bf"><div class="ttname"><a href="a00309.html#ga898ef075ccf809a1e480faab48fe96bf">glm::highestBitValue</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; highestBitValue(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Find the highest bit set to 1 in a integer variable and return its value. </div></div>
<div class="ttc" id="a00309_html_gaf78ddcc4152c051b2a21e68fecb10980"><div class="ttname"><a href="a00309.html#gaf78ddcc4152c051b2a21e68fecb10980">glm::powerOfTwoBelow</a></div><div class="ttdeci">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt; powerOfTwoBelow(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Return the power of two number which value is just lower the input value. </div></div>
<div class="ttc" id="a00309_html_ga2bbded187c5febfefc1e524ba31b3fab"><div class="ttname"><a href="a00309.html#ga2bbded187c5febfefc1e524ba31b3fab">glm::powerOfTwoAbove</a></div><div class="ttdeci">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt; powerOfTwoAbove(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Return the power of two number which value is just higher the input value. </div></div>
<div class="ttc" id="a00309_html_gac87e65d11e16c3d6b91c3bcfaef7da0b"><div class="ttname"><a href="a00309.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">glm::powerOfTwoNearest</a></div><div class="ttdeci">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt; powerOfTwoNearest(vec&lt; L, T, Q &gt; const &amp;value)</div><div class="ttdoc">Return the power of two number which value is the closet to the input value. </div></div>
<div class="ttc" id="a00309_html_ga2ff6568089f3a9b67f5c30918855fc6f"><div class="ttname"><a href="a00309.html#ga2ff6568089f3a9b67f5c30918855fc6f">glm::lowestBitValue</a></div><div class="ttdeci">GLM_FUNC_DECL genIUType lowestBitValue(genIUType Value)</div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
