<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_clip_space.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_clip_space.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00059.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_constants.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../geometric.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../trigonometric.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_matrix_clip_space extension included&quot;)</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;{</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga6615d8a9d39432e279c4575313ecb456">ortho</a>(</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                T left, T right, T bottom, T top);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gab37ac3eec8d61f22fceda7775e836afa">orthoLH_ZO</a>(</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga526416735ea7c5c5cd255bf99d051bd8">orthoLH_NO</a>(</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga9aea2e515b08fd7dce47b7b6ec34d588">orthoRH_ZO</a>(</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaa2f7a1373170bf0a4a2ddef9b0706780">orthoRH_NO</a>(</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaea11a70817af2c0801c869dea0b7a5bc">orthoZO</a>(</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gab219d28a8f178d4517448fcd6395a073">orthoNO</a>(</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gad122a79aadaa5529cec4ac197203db7f">orthoLH</a>(</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga16264c9b838edeb9dd1de7a1010a13a4">orthoRH</a>(</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga6615d8a9d39432e279c4575313ecb456">ortho</a>(</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                T left, T right, T bottom, T top, T zNear, T zFar);</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga94218b094862d17798370242680b9030">frustumLH_ZO</a>(</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga259520cad03b3f8bca9417920035ed01">frustumLH_NO</a>(</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga7654a9227f14d5382786b9fc0eb5692d">frustumRH_ZO</a>(</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga9236c8439f21be186b79c97b588836b9">frustumRH_NO</a>(</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaa73322e152edf50cf30a6edac342a757">frustumZO</a>(</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gae34ec664ad44860bf4b5ba631f0e0e90">frustumNO</a>(</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gae4277c37f61d81da01bc9db14ea90296">frustumLH</a>(</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga4366ab45880c6c5f8b3e8c371ca4b136">frustumRH</a>(</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga0bcd4542e0affc63a0b8c08fcb839ea9">frustum</a>(</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;                T left, T right, T bottom, T top, T near, T far);</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a">perspectiveRH_ZO</a>(</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gad1526cb2cbe796095284e8f34b01c582">perspectiveRH_NO</a>(</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaca32af88c2719005c02817ad1142986c">perspectiveLH_ZO</a>(</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaead4d049d1feab463b700b5641aa590e">perspectiveLH_NO</a>(</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaa9dfba5c2322da54f72b1eb7c7c11b47">perspectiveZO</a>(</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaf497e6bca61e7c87088370b126a93758">perspectiveNO</a>(</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga26b88757fbd90601b80768a7e1ad3aa1">perspectiveRH</a>(</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga9bd34951dc7022ac256fcb51d7f6fc2f">perspectiveLH</a>(</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga747c8cf99458663dd7ad1bb3a2f07787">perspective</a>(</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;                T fovy, T aspect, T near, T far);</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga7dcbb25331676f5b0795aced1a905c44">perspectiveFovRH_ZO</a>(</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga257b733ff883c9a065801023cf243eb2">perspectiveFovRH_NO</a>(</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gabdd37014f529e25b2fa1b3ba06c10d5c">perspectiveFovLH_ZO</a>(</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gad18a4495b77530317327e8d466488c1a">perspectiveFovLH_NO</a>(</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga4bc69fa1d1f95128430aa3d2a712390b">perspectiveFovZO</a>(</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaf30e7bd3b1387a6776433dd5383e6633">perspectiveFovNO</a>(</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaf32bf563f28379c68554a44ee60c6a85">perspectiveFovRH</a>(</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga6aebe16c164bd8e52554cbe0304ef4aa">perspectiveFovLH</a>(</div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaebd02240fd36e85ad754f02ddd9a560d">perspectiveFov</a>(</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;                T fov, T width, T height, T near, T far);</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga3201b30f5b3ea0f933246d87bfb992a9">infinitePerspectiveLH</a>(</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;                T fovy, T aspect, T near);</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga99672ffe5714ef478dab2437255fe7e1">infinitePerspectiveRH</a>(</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;                T fovy, T aspect, T near);</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#ga44fa38a18349450325cae2661bb115ca">infinitePerspective</a>(</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;                T fovy, T aspect, T near);</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaf5b3c85ff6737030a1d2214474ffa7a8">tweakedInfinitePerspective</a>(</div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;                T fovy, T aspect, T near);</div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;</div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00243.html#gaf5b3c85ff6737030a1d2214474ffa7a8">tweakedInfinitePerspective</a>(</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;                T fovy, T aspect, T near, T ep);</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;</div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;</div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;<span class="preprocessor">#include &quot;matrix_clip_space.inl&quot;</span></div>
<div class="ttc" id="a00243_html_ga9236c8439f21be186b79c97b588836b9"><div class="ttname"><a href="a00243.html#ga9236c8439f21be186b79c97b588836b9">glm::frustumRH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumRH_NO(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a right handed frustum matrix. </div></div>
<div class="ttc" id="a00243_html_ga44fa38a18349450325cae2661bb115ca"><div class="ttname"><a href="a00243.html#ga44fa38a18349450325cae2661bb115ca">glm::infinitePerspective</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; infinitePerspective(T fovy, T aspect, T near)</div><div class="ttdoc">Creates a matrix for a symmetric perspective-view frustum with far plane at infinite with default han...</div></div>
<div class="ttc" id="a00243_html_gaea11a70817af2c0801c869dea0b7a5bc"><div class="ttname"><a href="a00243.html#gaea11a70817af2c0801c869dea0b7a5bc">glm::orthoZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoZO(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_gaf5b3c85ff6737030a1d2214474ffa7a8"><div class="ttname"><a href="a00243.html#gaf5b3c85ff6737030a1d2214474ffa7a8">glm::tweakedInfinitePerspective</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; tweakedInfinitePerspective(T fovy, T aspect, T near, T ep)</div><div class="ttdoc">Creates a matrix for a symmetric perspective-view frustum with far plane at infinite for graphics har...</div></div>
<div class="ttc" id="a00243_html_ga16264c9b838edeb9dd1de7a1010a13a4"><div class="ttname"><a href="a00243.html#ga16264c9b838edeb9dd1de7a1010a13a4">glm::orthoRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoRH(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using right-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_ga6aebe16c164bd8e52554cbe0304ef4aa"><div class="ttname"><a href="a00243.html#ga6aebe16c164bd8e52554cbe0304ef4aa">glm::perspectiveFovLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovLH(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a left handed perspective projection matrix based on a field of view. </div></div>
<div class="ttc" id="a00243_html_ga94218b094862d17798370242680b9030"><div class="ttname"><a href="a00243.html#ga94218b094862d17798370242680b9030">glm::frustumLH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumLH_ZO(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a left handed frustum matrix. </div></div>
<div class="ttc" id="a00243_html_ga259520cad03b3f8bca9417920035ed01"><div class="ttname"><a href="a00243.html#ga259520cad03b3f8bca9417920035ed01">glm::frustumLH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumLH_NO(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a left handed frustum matrix. </div></div>
<div class="ttc" id="a00243_html_gae34ec664ad44860bf4b5ba631f0e0e90"><div class="ttname"><a href="a00243.html#gae34ec664ad44860bf4b5ba631f0e0e90">glm::frustumNO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumNO(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a frustum matrix using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-h...</div></div>
<div class="ttc" id="a00243_html_ga4366ab45880c6c5f8b3e8c371ca4b136"><div class="ttname"><a href="a00243.html#ga4366ab45880c6c5f8b3e8c371ca4b136">glm::frustumRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumRH(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a right handed frustum matrix. </div></div>
<div class="ttc" id="a00243_html_gae4277c37f61d81da01bc9db14ea90296"><div class="ttname"><a href="a00243.html#gae4277c37f61d81da01bc9db14ea90296">glm::frustumLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumLH(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a left handed frustum matrix. </div></div>
<div class="ttc" id="a00243_html_ga257b733ff883c9a065801023cf243eb2"><div class="ttname"><a href="a00243.html#ga257b733ff883c9a065801023cf243eb2">glm::perspectiveFovRH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovRH_NO(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view using right-handed coordinates...</div></div>
<div class="ttc" id="a00243_html_gaebd02240fd36e85ad754f02ddd9a560d"><div class="ttname"><a href="a00243.html#gaebd02240fd36e85ad754f02ddd9a560d">glm::perspectiveFov</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFov(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view and the default handedness and defaul...</div></div>
<div class="ttc" id="a00243_html_gaf32bf563f28379c68554a44ee60c6a85"><div class="ttname"><a href="a00243.html#gaf32bf563f28379c68554a44ee60c6a85">glm::perspectiveFovRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovRH(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a right handed perspective projection matrix based on a field of view. </div></div>
<div class="ttc" id="a00243_html_ga7654a9227f14d5382786b9fc0eb5692d"><div class="ttname"><a href="a00243.html#ga7654a9227f14d5382786b9fc0eb5692d">glm::frustumRH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumRH_ZO(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a right handed frustum matrix. </div></div>
<div class="ttc" id="a00243_html_gab37ac3eec8d61f22fceda7775e836afa"><div class="ttname"><a href="a00243.html#gab37ac3eec8d61f22fceda7775e836afa">glm::orthoLH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoLH_ZO(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_gaca32af88c2719005c02817ad1142986c"><div class="ttname"><a href="a00243.html#gaca32af88c2719005c02817ad1142986c">glm::perspectiveLH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveLH_ZO(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a left handed, symetric perspective-view frustum. </div></div>
<div class="ttc" id="a00243_html_gad1526cb2cbe796095284e8f34b01c582"><div class="ttname"><a href="a00243.html#gad1526cb2cbe796095284e8f34b01c582">glm::perspectiveRH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveRH_NO(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a right handed, symetric perspective-view frustum. </div></div>
<div class="ttc" id="a00243_html_gaa2f7a1373170bf0a4a2ddef9b0706780"><div class="ttname"><a href="a00243.html#gaa2f7a1373170bf0a4a2ddef9b0706780">glm::orthoRH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoRH_NO(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using right-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_gaead4d049d1feab463b700b5641aa590e"><div class="ttname"><a href="a00243.html#gaead4d049d1feab463b700b5641aa590e">glm::perspectiveLH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveLH_NO(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a left handed, symetric perspective-view frustum. </div></div>
<div class="ttc" id="a00243_html_ga6615d8a9d39432e279c4575313ecb456"><div class="ttname"><a href="a00243.html#ga6615d8a9d39432e279c4575313ecb456">glm::ortho</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; ortho(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using the default handedness and defaul...</div></div>
<div class="ttc" id="a00243_html_gabdd37014f529e25b2fa1b3ba06c10d5c"><div class="ttname"><a href="a00243.html#gabdd37014f529e25b2fa1b3ba06c10d5c">glm::perspectiveFovLH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovLH_ZO(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view using left-handed coordinates...</div></div>
<div class="ttc" id="a00243_html_gaa73322e152edf50cf30a6edac342a757"><div class="ttname"><a href="a00243.html#gaa73322e152edf50cf30a6edac342a757">glm::frustumZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustumZO(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a frustum matrix using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-h...</div></div>
<div class="ttc" id="a00243_html_gad122a79aadaa5529cec4ac197203db7f"><div class="ttname"><a href="a00243.html#gad122a79aadaa5529cec4ac197203db7f">glm::orthoLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoLH(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_gab219d28a8f178d4517448fcd6395a073"><div class="ttname"><a href="a00243.html#gab219d28a8f178d4517448fcd6395a073">glm::orthoNO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoNO(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates if GLM_FO...</div></div>
<div class="ttc" id="a00243_html_ga526416735ea7c5c5cd255bf99d051bd8"><div class="ttname"><a href="a00243.html#ga526416735ea7c5c5cd255bf99d051bd8">glm::orthoLH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoLH_NO(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume using right-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_gaf30e7bd3b1387a6776433dd5383e6633"><div class="ttname"><a href="a00243.html#gaf30e7bd3b1387a6776433dd5383e6633">glm::perspectiveFovNO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovNO(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view using left-handed coordinates if GLM_...</div></div>
<div class="ttc" id="a00243_html_ga747c8cf99458663dd7ad1bb3a2f07787"><div class="ttname"><a href="a00243.html#ga747c8cf99458663dd7ad1bb3a2f07787">glm::perspective</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspective(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a symetric perspective-view frustum based on the default handedness and default ...</div></div>
<div class="ttc" id="a00243_html_ga9aea2e515b08fd7dce47b7b6ec34d588"><div class="ttname"><a href="a00243.html#ga9aea2e515b08fd7dce47b7b6ec34d588">glm::orthoRH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; orthoRH_ZO(T left, T right, T bottom, T top, T zNear, T zFar)</div><div class="ttdoc">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </div></div>
<div class="ttc" id="a00243_html_ga4bc69fa1d1f95128430aa3d2a712390b"><div class="ttname"><a href="a00243.html#ga4bc69fa1d1f95128430aa3d2a712390b">glm::perspectiveFovZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovZO(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view using left-handed coordinates if GLM_...</div></div>
<div class="ttc" id="a00243_html_ga99672ffe5714ef478dab2437255fe7e1"><div class="ttname"><a href="a00243.html#ga99672ffe5714ef478dab2437255fe7e1">glm::infinitePerspectiveRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; infinitePerspectiveRH(T fovy, T aspect, T near)</div><div class="ttdoc">Creates a matrix for a right handed, symmetric perspective-view frustum with far plane at infinite...</div></div>
<div class="ttc" id="a00243_html_gaf497e6bca61e7c87088370b126a93758"><div class="ttname"><a href="a00243.html#gaf497e6bca61e7c87088370b126a93758">glm::perspectiveNO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveNO(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a symetric perspective-view frustum using left-handed coordinates if GLM_FORCE_L...</div></div>
<div class="ttc" id="a00243_html_gad18a4495b77530317327e8d466488c1a"><div class="ttname"><a href="a00243.html#gad18a4495b77530317327e8d466488c1a">glm::perspectiveFovLH_NO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovLH_NO(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view using left-handed coordinates...</div></div>
<div class="ttc" id="a00243_html_ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a"><div class="ttname"><a href="a00243.html#ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a">glm::perspectiveRH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveRH_ZO(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a right handed, symetric perspective-view frustum. </div></div>
<div class="ttc" id="a00243_html_gaa9dfba5c2322da54f72b1eb7c7c11b47"><div class="ttname"><a href="a00243.html#gaa9dfba5c2322da54f72b1eb7c7c11b47">glm::perspectiveZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveZO(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a symetric perspective-view frustum using left-handed coordinates if GLM_FORCE_L...</div></div>
<div class="ttc" id="a00243_html_ga3201b30f5b3ea0f933246d87bfb992a9"><div class="ttname"><a href="a00243.html#ga3201b30f5b3ea0f933246d87bfb992a9">glm::infinitePerspectiveLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; infinitePerspectiveLH(T fovy, T aspect, T near)</div><div class="ttdoc">Creates a matrix for a left handed, symmetric perspective-view frustum with far plane at infinite...</div></div>
<div class="ttc" id="a00243_html_ga9bd34951dc7022ac256fcb51d7f6fc2f"><div class="ttname"><a href="a00243.html#ga9bd34951dc7022ac256fcb51d7f6fc2f">glm::perspectiveLH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveLH(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a left handed, symetric perspective-view frustum. </div></div>
<div class="ttc" id="a00243_html_ga7dcbb25331676f5b0795aced1a905c44"><div class="ttname"><a href="a00243.html#ga7dcbb25331676f5b0795aced1a905c44">glm::perspectiveFovRH_ZO</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveFovRH_ZO(T fov, T width, T height, T near, T far)</div><div class="ttdoc">Builds a perspective projection matrix based on a field of view using right-handed coordinates...</div></div>
<div class="ttc" id="a00243_html_ga0bcd4542e0affc63a0b8c08fcb839ea9"><div class="ttname"><a href="a00243.html#ga0bcd4542e0affc63a0b8c08fcb839ea9">glm::frustum</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; frustum(T left, T right, T bottom, T top, T near, T far)</div><div class="ttdoc">Creates a frustum matrix with default handedness, using the default handedness and default near and f...</div></div>
<div class="ttc" id="a00243_html_ga26b88757fbd90601b80768a7e1ad3aa1"><div class="ttname"><a href="a00243.html#ga26b88757fbd90601b80768a7e1ad3aa1">glm::perspectiveRH</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; perspectiveRH(T fovy, T aspect, T near, T far)</div><div class="ttdoc">Creates a matrix for a right handed, symetric perspective-view frustum. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
