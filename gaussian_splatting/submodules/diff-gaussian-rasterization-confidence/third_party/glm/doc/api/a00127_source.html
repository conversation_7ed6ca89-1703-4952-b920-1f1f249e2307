<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: quaternion_common.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">quaternion_common.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00127.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_constants.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../ext/quaternion_geometric.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;../trigonometric.hpp&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;../exponential.hpp&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_quaternion_common extension included&quot;)</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;{</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00248.html#gafbfe587b8da11fb89a30c3d67dd5ccc2">mix</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y, T a);</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00248.html#ga6033dc0741051fa463a0a147ba29f293">lerp</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y, T a);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00248.html#gae7fc3c945be366b9942b842f55da428a">slerp</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x, qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; y, T a);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00248.html#ga10d7bda73201788ac2ab28cd8d0d409b">conjugate</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;        GLM_FUNC_DECL qua&lt;T, Q&gt; <a class="code" href="a00248.html#gab41da854ae678e23e114b598cbca4065">inverse</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; q);</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00248.html#ga1bb55f8963616502e96dc564384d8a03">isnan</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00248.html#ga45722741ea266b4e861938b365c5f362">isinf</a>(qua&lt;T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="preprocessor">#include &quot;quaternion_common.inl&quot;</span></div>
<div class="ttc" id="a00248_html_ga45722741ea266b4e861938b365c5f362"><div class="ttname"><a href="a00248.html#ga45722741ea266b4e861938b365c5f362">glm::isinf</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; isinf(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns true if x holds a positive infinity or negative infinity representation in the underlying imp...</div></div>
<div class="ttc" id="a00248_html_ga1bb55f8963616502e96dc564384d8a03"><div class="ttname"><a href="a00248.html#ga1bb55f8963616502e96dc564384d8a03">glm::isnan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; isnan(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns true if x holds a NaN (not a number) representation in the underlying implementation&#39;s set of...</div></div>
<div class="ttc" id="a00248_html_ga10d7bda73201788ac2ab28cd8d0d409b"><div class="ttname"><a href="a00248.html#ga10d7bda73201788ac2ab28cd8d0d409b">glm::conjugate</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; conjugate(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the q conjugate. </div></div>
<div class="ttc" id="a00248_html_gae7fc3c945be366b9942b842f55da428a"><div class="ttname"><a href="a00248.html#gae7fc3c945be366b9942b842f55da428a">glm::slerp</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; slerp(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)</div><div class="ttdoc">Spherical linear interpolation of two quaternions. </div></div>
<div class="ttc" id="a00248_html_gab41da854ae678e23e114b598cbca4065"><div class="ttname"><a href="a00248.html#gab41da854ae678e23e114b598cbca4065">glm::inverse</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; inverse(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the q inverse. </div></div>
<div class="ttc" id="a00248_html_ga6033dc0741051fa463a0a147ba29f293"><div class="ttname"><a href="a00248.html#ga6033dc0741051fa463a0a147ba29f293">glm::lerp</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; lerp(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)</div><div class="ttdoc">Linear interpolation of two quaternions. </div></div>
<div class="ttc" id="a00248_html_gafbfe587b8da11fb89a30c3d67dd5ccc2"><div class="ttname"><a href="a00248.html#gafbfe587b8da11fb89a30c3d67dd5ccc2">glm::mix</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, Q &gt; mix(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)</div><div class="ttdoc">Spherical linear interpolation of two quaternions. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
