<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: norm.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">norm.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00113.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../geometric.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../gtx/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../gtx/component_wise.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_norm is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_norm extension included&quot;)</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;{</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#ga8d1789651050adb7024917984b41c3de">length2</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#ga85660f1b79f66c09c7b5a6f80e68c89f">distance2</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p0, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; p1);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">l1Norm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">l1Norm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">l2Norm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">l2Norm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">lxNorm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; y, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> Depth);</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">lxNorm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> Depth);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#ga6968a324837a8e899396d44de23d5aae">lMaxNorm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00343.html#ga6968a324837a8e899396d44de23d5aae">lMaxNorm</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor">#include &quot;norm.inl&quot;</span></div>
<div class="ttc" id="a00343_html_ga8d1789651050adb7024917984b41c3de"><div class="ttname"><a href="a00343.html#ga8d1789651050adb7024917984b41c3de">glm::length2</a></div><div class="ttdeci">GLM_FUNC_DECL T length2(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the squared length of x. </div></div>
<div class="ttc" id="a00343_html_ga1a7491e2037ceeb37f83ce41addfc0be"><div class="ttname"><a href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">glm::l1Norm</a></div><div class="ttdeci">GLM_FUNC_DECL T l1Norm(vec&lt; 3, T, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the L1 norm of v. </div></div>
<div class="ttc" id="a00343_html_ga85660f1b79f66c09c7b5a6f80e68c89f"><div class="ttname"><a href="a00343.html#ga85660f1b79f66c09c7b5a6f80e68c89f">glm::distance2</a></div><div class="ttdeci">GLM_FUNC_DECL T distance2(vec&lt; L, T, Q &gt; const &amp;p0, vec&lt; L, T, Q &gt; const &amp;p1)</div><div class="ttdoc">Returns the squared distance between p0 and p1, i.e., length2(p0 - p1). </div></div>
<div class="ttc" id="a00343_html_ga6968a324837a8e899396d44de23d5aae"><div class="ttname"><a href="a00343.html#ga6968a324837a8e899396d44de23d5aae">glm::lMaxNorm</a></div><div class="ttdeci">GLM_FUNC_DECL T lMaxNorm(vec&lt; 3, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the LMax norm of v. </div></div>
<div class="ttc" id="a00343_html_gac61b6d81d796d6eb4d4183396a19ab91"><div class="ttname"><a href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">glm::lxNorm</a></div><div class="ttdeci">GLM_FUNC_DECL T lxNorm(vec&lt; 3, T, Q &gt; const &amp;x, unsigned int Depth)</div><div class="ttdoc">Returns the L norm of v. </div></div>
<div class="ttc" id="a00343_html_gae288bde8f0e41fb4ed62e65137b18cba"><div class="ttname"><a href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">glm::l2Norm</a></div><div class="ttdeci">GLM_FUNC_DECL T l2Norm(vec&lt; 3, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the L2 norm of v. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
