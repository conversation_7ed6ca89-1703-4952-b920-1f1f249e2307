<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_double3x3_precision.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix_double3x3_precision.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00072.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;</div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;{</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="a00284.html#ga0cab80beee64a5f8d2ae4e823983063a">   16</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, lowp&gt;         <a class="code" href="a00284.html#ga0cab80beee64a5f8d2ae4e823983063a">lowp_dmat3</a>;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno"><a class="line" href="a00284.html#ga939fbf9c53008a8e84c7dd7cf8de29e2">   22</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, mediump&gt;      <a class="code" href="a00284.html#ga939fbf9c53008a8e84c7dd7cf8de29e2">mediump_dmat3</a>;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno"><a class="line" href="a00284.html#ga86d6d4dbad92ffdcc759773340e15a97">   28</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, highp&gt;        <a class="code" href="a00284.html#ga86d6d4dbad92ffdcc759773340e15a97">highp_dmat3</a>;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="a00284.html#gac017848a9df570f60916a21a297b1e8e">   34</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, lowp&gt;         <a class="code" href="a00284.html#gac017848a9df570f60916a21a297b1e8e">lowp_dmat3x3</a>;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="a00284.html#ga47bd2aae4701ee2fc865674a9df3d7a6">   40</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, mediump&gt;      <a class="code" href="a00284.html#ga47bd2aae4701ee2fc865674a9df3d7a6">mediump_dmat3x3</a>;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00284.html#gae367ea93c4ad8a7c101dd27b8b2b04ce">   46</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, highp&gt;        <a class="code" href="a00284.html#gae367ea93c4ad8a7c101dd27b8b2b04ce">highp_dmat3x3</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="ttc" id="a00284_html_ga0cab80beee64a5f8d2ae4e823983063a"><div class="ttname"><a href="a00284.html#ga0cab80beee64a5f8d2ae4e823983063a">glm::lowp_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, lowp &gt; lowp_dmat3</div><div class="ttdoc">3 columns of 3 components matrix of double-precision floating-point numbers using low precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00072_source.html#l00016">matrix_double3x3_precision.hpp:16</a></div></div>
<div class="ttc" id="a00284_html_gac017848a9df570f60916a21a297b1e8e"><div class="ttname"><a href="a00284.html#gac017848a9df570f60916a21a297b1e8e">glm::lowp_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, lowp &gt; lowp_dmat3x3</div><div class="ttdoc">3 columns of 3 components matrix of double-precision floating-point numbers using low precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00072_source.html#l00034">matrix_double3x3_precision.hpp:34</a></div></div>
<div class="ttc" id="a00284_html_ga86d6d4dbad92ffdcc759773340e15a97"><div class="ttname"><a href="a00284.html#ga86d6d4dbad92ffdcc759773340e15a97">glm::highp_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, highp &gt; highp_dmat3</div><div class="ttdoc">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision ar...</div><div class="ttdef"><b>Definition:</b> <a href="a00072_source.html#l00028">matrix_double3x3_precision.hpp:28</a></div></div>
<div class="ttc" id="a00284_html_gae367ea93c4ad8a7c101dd27b8b2b04ce"><div class="ttname"><a href="a00284.html#gae367ea93c4ad8a7c101dd27b8b2b04ce">glm::highp_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, highp &gt; highp_dmat3x3</div><div class="ttdoc">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision ar...</div><div class="ttdef"><b>Definition:</b> <a href="a00072_source.html#l00046">matrix_double3x3_precision.hpp:46</a></div></div>
<div class="ttc" id="a00284_html_ga47bd2aae4701ee2fc865674a9df3d7a6"><div class="ttname"><a href="a00284.html#ga47bd2aae4701ee2fc865674a9df3d7a6">glm::mediump_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, mediump &gt; mediump_dmat3x3</div><div class="ttdoc">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision ar...</div><div class="ttdef"><b>Definition:</b> <a href="a00072_source.html#l00040">matrix_double3x3_precision.hpp:40</a></div></div>
<div class="ttc" id="a00284_html_ga939fbf9c53008a8e84c7dd7cf8de29e2"><div class="ttname"><a href="a00284.html#ga939fbf9c53008a8e84c7dd7cf8de29e2">glm::mediump_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, mediump &gt; mediump_dmat3</div><div class="ttdoc">3 columns of 3 components matrix of double-precision floating-point numbers using medium precision ar...</div><div class="ttdef"><b>Definition:</b> <a href="a00072_source.html#l00022">matrix_double3x3_precision.hpp:22</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
