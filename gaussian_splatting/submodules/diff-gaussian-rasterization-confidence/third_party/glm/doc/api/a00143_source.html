<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: round.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">round.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00143.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../detail/_vectorize.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../vector_relational.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_round extension included&quot;)</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;{</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00302.html#gab53d4a97c0d3e297be5f693cdfdfe5d2">ceilPowerOfTwo</a>(genIUType v);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00302.html#gab53d4a97c0d3e297be5f693cdfdfe5d2">ceilPowerOfTwo</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00302.html#gaf0d591a8fca8ddb9289cdeb44b989c2d">floorPowerOfTwo</a>(genIUType v);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00302.html#gaf0d591a8fca8ddb9289cdeb44b989c2d">floorPowerOfTwo</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00302.html#ga258802a7d55c03c918f28cf4d241c4d0">roundPowerOfTwo</a>(genIUType v);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00302.html#ga258802a7d55c03c918f28cf4d241c4d0">roundPowerOfTwo</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00302.html#gab77fdcc13f8e92d2e0b1b7d7aeab8e9d">ceilMultiple</a>(genType v, genType Multiple);</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00302.html#gab77fdcc13f8e92d2e0b1b7d7aeab8e9d">ceilMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Multiple);</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00302.html#gacdd8901448f51f0b192380e422fae3e4">floorMultiple</a>(genType v, genType Multiple);</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00302.html#gacdd8901448f51f0b192380e422fae3e4">floorMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Multiple);</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00302.html#ga2f1a68332d761804c054460a612e3a4b">roundMultiple</a>(genType v, genType Multiple);</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00302.html#ga2f1a68332d761804c054460a612e3a4b">roundMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Multiple);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="preprocessor">#include &quot;round.inl&quot;</span></div>
<div class="ttc" id="a00302_html_ga258802a7d55c03c918f28cf4d241c4d0"><div class="ttname"><a href="a00302.html#ga258802a7d55c03c918f28cf4d241c4d0">glm::roundPowerOfTwo</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; roundPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Return the power of two number which value is the closet to the input value. </div></div>
<div class="ttc" id="a00302_html_gab77fdcc13f8e92d2e0b1b7d7aeab8e9d"><div class="ttname"><a href="a00302.html#gab77fdcc13f8e92d2e0b1b7d7aeab8e9d">glm::ceilMultiple</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; ceilMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</div><div class="ttdoc">Higher multiple number of Source. </div></div>
<div class="ttc" id="a00302_html_gaf0d591a8fca8ddb9289cdeb44b989c2d"><div class="ttname"><a href="a00302.html#gaf0d591a8fca8ddb9289cdeb44b989c2d">glm::floorPowerOfTwo</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; floorPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Return the power of two number which value is just lower the input value, round down to a power of tw...</div></div>
<div class="ttc" id="a00302_html_ga2f1a68332d761804c054460a612e3a4b"><div class="ttname"><a href="a00302.html#ga2f1a68332d761804c054460a612e3a4b">glm::roundMultiple</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; roundMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</div><div class="ttdoc">Lower multiple number of Source. </div></div>
<div class="ttc" id="a00302_html_gab53d4a97c0d3e297be5f693cdfdfe5d2"><div class="ttname"><a href="a00302.html#gab53d4a97c0d3e297be5f693cdfdfe5d2">glm::ceilPowerOfTwo</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; ceilPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Return the power of two number which value is just higher the input value, round up to a power of two...</div></div>
<div class="ttc" id="a00302_html_gacdd8901448f51f0b192380e422fae3e4"><div class="ttname"><a href="a00302.html#gacdd8901448f51f0b192380e422fae3e4">glm::floorMultiple</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; floorMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</div><div class="ttdoc">Lower multiple number of Source. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
