<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix_projection.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">matrix_projection.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00245.html">GLM_EXT_matrix_projection</a>  
<a href="#details">More...</a></p>

<p><a href="a00105_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q, typename U &gt; </td></tr>
<tr class="memitem:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gaf6b21eadb7ac2ecbbe258a9a233b4c82">pickMatrix</a> (vec&lt; 2, T, Q &gt; const &amp;center, vec&lt; 2, T, Q &gt; const &amp;delta, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define a picking region.  <a href="a00245.html#gaf6b21eadb7ac2ecbbe258a9a233b4c82">More...</a><br /></td></tr>
<tr class="separator:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf36e96033f456659e6705472a06b6e11"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf36e96033f456659e6705472a06b6e11"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gaf36e96033f456659e6705472a06b6e11">project</a> (vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gaf36e96033f456659e6705472a06b6e11"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates using default near and far clip planes definition.  <a href="a00245.html#gaf36e96033f456659e6705472a06b6e11">More...</a><br /></td></tr>
<tr class="separator:gaf36e96033f456659e6705472a06b6e11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05249751f48d14cb282e4979802b8111"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga05249751f48d14cb282e4979802b8111"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#ga05249751f48d14cb282e4979802b8111">projectNO</a> (vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:ga05249751f48d14cb282e4979802b8111"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates.  <a href="a00245.html#ga05249751f48d14cb282e4979802b8111">More...</a><br /></td></tr>
<tr class="separator:ga05249751f48d14cb282e4979802b8111"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77d157525063dec83a557186873ee080"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga77d157525063dec83a557186873ee080"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#ga77d157525063dec83a557186873ee080">projectZO</a> (vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:ga77d157525063dec83a557186873ee080"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates.  <a href="a00245.html#ga77d157525063dec83a557186873ee080">More...</a><br /></td></tr>
<tr class="separator:ga77d157525063dec83a557186873ee080"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga36641e5d60f994e01c3d8f56b10263d2"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga36641e5d60f994e01c3d8f56b10263d2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#ga36641e5d60f994e01c3d8f56b10263d2">unProject</a> (vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:ga36641e5d60f994e01c3d8f56b10263d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates using default near and far clip planes definition.  <a href="a00245.html#ga36641e5d60f994e01c3d8f56b10263d2">More...</a><br /></td></tr>
<tr class="separator:ga36641e5d60f994e01c3d8f56b10263d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae089ba9fc150ff69c252a20e508857b5"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gae089ba9fc150ff69c252a20e508857b5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gae089ba9fc150ff69c252a20e508857b5">unProjectNO</a> (vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gae089ba9fc150ff69c252a20e508857b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates.  <a href="a00245.html#gae089ba9fc150ff69c252a20e508857b5">More...</a><br /></td></tr>
<tr class="separator:gae089ba9fc150ff69c252a20e508857b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade5136413ce530f8e606124d570fba32"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gade5136413ce530f8e606124d570fba32"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gade5136413ce530f8e606124d570fba32">unProjectZO</a> (vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gade5136413ce530f8e606124d570fba32"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates.  <a href="a00245.html#gade5136413ce530f8e606124d570fba32">More...</a><br /></td></tr>
<tr class="separator:gade5136413ce530f8e606124d570fba32"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00245.html">GLM_EXT_matrix_projection</a> </p>

<p>Definition in file <a class="el" href="a00105_source.html">matrix_projection.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
