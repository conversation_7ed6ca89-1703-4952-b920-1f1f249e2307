<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: packing.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtc/packing.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00119.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00174.html">type_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_packing extension included&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;{</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_DECL uint8 <a class="code" href="a00298.html#ga4b2fa60df3460403817d28b082ee0736">packUnorm1x8</a>(<span class="keywordtype">float</span> v);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00298.html#ga1319207e30874fb4931a9ee913983ee1">unpackUnorm1x8</a>(uint8 p);</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#ga9a666b1c688ab54100061ed06526de6e">packUnorm2x8</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <a class="code" href="a00298.html#ga637cbe3913dd95c6e7b4c99c61bd611f">unpackUnorm2x8</a>(uint16 p);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        GLM_FUNC_DECL uint8 <a class="code" href="a00298.html#gae3592e0795e62aaa1865b3a10496a7a1">packSnorm1x8</a>(<span class="keywordtype">float</span> s);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00298.html#ga4851ff86678aa1c7ace9d67846894285">unpackSnorm1x8</a>(uint8 p);</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#ga6be3cfb2cce3702f03e91bbeb5286d7e">packSnorm2x8</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <a class="code" href="a00298.html#ga8b128e89be449fc71336968a66bf6e1a">unpackSnorm2x8</a>(uint16 p);</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#ga9f82737bf2a44bedff1d286b76837886">packUnorm1x16</a>(<span class="keywordtype">float</span> v);</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00298.html#ga83d34160a5cb7bcb5339823210fc7501">unpackUnorm1x16</a>(uint16 p);</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00298.html#ga1f63c264e7ab63264e2b2a99fd393897">packUnorm4x16</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#ga2ae149c5d2473ac1e5f347bb654a242d">unpackUnorm4x16</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> p);</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#gab22f8bcfdb5fc65af4701b25f143c1af">packSnorm1x16</a>(<span class="keywordtype">float</span> v);</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00298.html#ga96dd15002370627a443c835ab03a766c">unpackSnorm1x16</a>(uint16 p);</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00298.html#ga358943934d21da947d5bcc88c2ab7832">packSnorm4x16</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#gaaddf9c353528fe896106f7181219c7f4">unpackSnorm4x16</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> p);</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#ga43f2093b6ff192a79058ff7834fc3528">packHalf1x16</a>(<span class="keywordtype">float</span> v);</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">float</span> <a class="code" href="a00298.html#gac37dedaba24b00adb4ec6e8f92c19dbf">unpackHalf1x16</a>(uint16 v);</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00298.html#gafe2f7b39caf8f5ec555e1c059ec530e6">packHalf4x16</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#ga57dfc41b2eb20b0ac00efae7d9c49dcd">unpackHalf4x16</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> p);</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#ga06ecb6afb902dba45419008171db9023">packI3x10_1x2</a>(<a class="code" href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">ivec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">ivec4</a> <a class="code" href="a00298.html#ga9a05330e5490be0908d3b117d82aff56">unpackI3x10_1x2</a>(uint32 p);</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#gada3d88d59f0f458f9c51a9fd359a4bc0">packU3x10_1x2</a>(<a class="code" href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">uvec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">uvec4</a> <a class="code" href="a00298.html#ga48df3042a7d079767f5891a1bfd8a60a">unpackU3x10_1x2</a>(uint32 p);</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#gab997545661877d2c7362a5084d3897d3">packSnorm3x10_1x2</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#ga7a4fbf79be9740e3c57737bc2af05e5b">unpackSnorm3x10_1x2</a>(uint32 p);</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#ga8a1ee625d2707c60530fb3fca2980b19">packUnorm3x10_1x2</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#ga5156d3060355fe332865da2c7f78815f">unpackUnorm3x10_1x2</a>(uint32 p);</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;</div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#ga4944ad465ff950e926d49621f916c78d">packF2x11_1x10</a>(<a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <a class="code" href="a00298.html#ga2b1fd1e854705b1345e98409e0a25e50">unpackF2x11_1x10</a>(uint32 p);</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#ga3f648fc205467792dc6d8c59c748f8a6">packF3x9_E1x5</a>(<a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <a class="code" href="a00298.html#gab9e60ebe3ad3eeced6a9ec6eb876d74e">unpackF3x9_E1x5</a>(uint32 p);</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;</div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00298.html#ga0466daf4c90f76cc64b3f105ce727295">packRGBM</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; rgb);</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;</div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00298.html#ga5c1ec97894b05ea21a05aea4f0204a02">unpackRGBM</a>(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; rgbm);</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;</div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;        GLM_FUNC_DECL vec&lt;L, uint16, Q&gt; <a class="code" href="a00298.html#ga2d8bbce673ebc04831c1fb05c47f5251">packHalf</a>(vec&lt;L, float, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;        GLM_FUNC_DECL vec&lt;L, float, Q&gt; <a class="code" href="a00298.html#ga30d6b2f1806315bcd6047131f547d33b">unpackHalf</a>(vec&lt;L, uint16, Q&gt; <span class="keyword">const</span>&amp; p);</div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> u<span class="keywordtype">int</span>Type, length_t L, <span class="keyword">typename</span> <span class="keywordtype">float</span>Type, qualifier Q&gt;</div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;        GLM_FUNC_DECL vec&lt;L, uintType, Q&gt; <a class="code" href="a00298.html#gaccd3f27e6ba5163eb7aa9bc8ff96251a">packUnorm</a>(vec&lt;L, floatType, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;</div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> <span class="keywordtype">float</span>Type, length_t L, <span class="keyword">typename</span> u<span class="keywordtype">int</span>Type, qualifier Q&gt;</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;        GLM_FUNC_DECL vec&lt;L, floatType, Q&gt; <a class="code" href="a00298.html#ga3e6ac9178b59f0b1b2f7599f2183eb7f">unpackUnorm</a>(vec&lt;L, uintType, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;</div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> <span class="keywordtype">int</span>Type, length_t L, <span class="keyword">typename</span> <span class="keywordtype">float</span>Type, qualifier Q&gt;</div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;        GLM_FUNC_DECL vec&lt;L, intType, Q&gt; <a class="code" href="a00298.html#gaa54b5855a750d6aeb12c1c902f5939b8">packSnorm</a>(vec&lt;L, floatType, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> <span class="keywordtype">float</span>Type, length_t L, <span class="keyword">typename</span> <span class="keywordtype">int</span>Type, qualifier Q&gt;</div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;        GLM_FUNC_DECL vec&lt;L, floatType, Q&gt; <a class="code" href="a00298.html#ga6d49b31e5c3f9df8e1f99ab62b999482">unpackSnorm</a>(vec&lt;L, intType, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;        GLM_FUNC_DECL uint8 <a class="code" href="a00298.html#gab6bbd5be3b8e6db538ecb33a7844481c">packUnorm2x4</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> <a class="code" href="a00298.html#ga2e50476132fe5f27f08e273d9c70d85b">unpackUnorm2x4</a>(uint8 p);</div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#gad3e7e3ce521513584a53aedc5f9765c1">packUnorm4x4</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;</div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#gac58ee89d0e224bb6df5e8bbb18843a2d">unpackUnorm4x4</a>(uint16 p);</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;</div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#ga768e0337dd6246773f14aa0a421fe9a8">packUnorm1x5_1x6_1x5</a>(<a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;</div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <a class="code" href="a00298.html#gab3bc08ecfc0f3339be93fb2b3b56d88a">unpackUnorm1x5_1x6_1x5</a>(uint16 p);</div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;</div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#gaec4112086d7fb133bea104a7c237de52">packUnorm3x5_1x1</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;</div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> <a class="code" href="a00298.html#ga5ff95ff5bc16f396432ab67243dbae4d">unpackUnorm3x5_1x1</a>(uint16 p);</div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;</div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;        GLM_FUNC_DECL uint8 <a class="code" href="a00298.html#ga7f9abdb50f9be1aa1c14912504a0d98d">packUnorm2x3_1x2</a>(<a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;</div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> <a class="code" href="a00298.html#ga6abd5a9014df3b5ce4059008d2491260">unpackUnorm2x3_1x2</a>(uint8 p);</div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;</div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;</div>
<div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;        GLM_FUNC_DECL int16 <a class="code" href="a00298.html#ga8884b1f2292414f36d59ef3be5d62914">packInt2x8</a>(<a class="code" href="a00304.html#gad06935764d78f43f9d542c784c2212ec">i8vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;</div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#gad06935764d78f43f9d542c784c2212ec">i8vec2</a> <a class="code" href="a00298.html#gab0c59f1e259fca9e68adb2207a6b665e">unpackInt2x8</a>(int16 p);</div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;</div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00298.html#ga3c3c9fb53ae7823b10fa083909357590">packUint2x8</a>(<a class="code" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;</div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a> <a class="code" href="a00298.html#gaa7600a6c71784b637a410869d2a5adcd">unpackUint2x8</a>(uint16 p);</div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;        GLM_FUNC_DECL int32 <a class="code" href="a00298.html#gaf2238401d5ce2aaade1a44ba19709072">packInt4x8</a>(<a class="code" href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">i8vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;</div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">i8vec4</a> <a class="code" href="a00298.html#ga1cd8d2038cdd33a860801aa155a26221">unpackInt4x8</a>(int32 p);</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;</div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00298.html#gaa0fe2f09aeb403cd66c1a062f58861ab">packUint4x8</a>(<a class="code" href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">u8vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;</div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">u8vec4</a> <a class="code" href="a00298.html#gaf6dc0e4341810a641c7ed08f10e335d1">unpackUint4x8</a>(uint32 p);</div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;</div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">int</span> <a class="code" href="a00298.html#ga3644163cf3a47bf1d4af1f4b03013a7e">packInt2x16</a>(<a class="code" href="a00304.html#ga2996630ba7b10535af8e065cf326f761">i16vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;</div>
<div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga2996630ba7b10535af8e065cf326f761">i16vec2</a> <a class="code" href="a00298.html#gaccde055882918a3175de82f4ca8b7d8e">unpackInt2x16</a>(<span class="keywordtype">int</span> p);</div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;</div>
<div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> <a class="code" href="a00298.html#ga1989f093a27ae69cf9207145be48b3d7">packInt4x16</a>(<a class="code" href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">i16vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;</div>
<div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">i16vec4</a> <a class="code" href="a00298.html#ga52c154a9b232b62c22517a700cc0c78c">unpackInt4x16</a>(<a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> p);</div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;</div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;        GLM_FUNC_DECL uint <a class="code" href="a00298.html#ga5eecc9e8cbaf51ac6cf57501e670ee19">packUint2x16</a>(<a class="code" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;</div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a> <a class="code" href="a00298.html#ga035bbbeab7ec2b28c0529757395b645b">unpackUint2x16</a>(uint p);</div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;</div>
<div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00298.html#ga2ceb62cca347d8ace42ee90317a3f1f9">packUint4x16</a>(<a class="code" href="a00304.html#ga529496d75775fb656a07993ea9af2450">u16vec4</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;</div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga529496d75775fb656a07993ea9af2450">u16vec4</a> <a class="code" href="a00298.html#gab173834ef14cfc23a96a959f3ff4b8dc">unpackUint4x16</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> p);</div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;</div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> <a class="code" href="a00298.html#gad1e4c8a9e67d86b61a6eec86703a827a">packInt2x32</a>(<a class="code" href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">i32vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;</div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">i32vec2</a> <a class="code" href="a00298.html#gab297c0bfd38433524791eb0584d8f08d">unpackInt2x32</a>(<a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> p);</div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;</div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00298.html#gaa864081097b86e83d8e4a4d79c382b22">packUint2x32</a>(<a class="code" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;</div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a> <a class="code" href="a00298.html#gaf942ff11b65e83eb5f77e68329ebc6ab">unpackUint2x32</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> p);</div>
<div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;</div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;</div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;}<span class="comment">// namespace glm</span></div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;</div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;<span class="preprocessor">#include &quot;packing.inl&quot;</span></div>
<div class="ttc" id="a00298_html_gad3e7e3ce521513584a53aedc5f9765c1"><div class="ttname"><a href="a00298.html#gad3e7e3ce521513584a53aedc5f9765c1">glm::packUnorm4x4</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packUnorm4x4(vec4 const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into unsigned integer values...</div></div>
<div class="ttc" id="a00298_html_ga3c3c9fb53ae7823b10fa083909357590"><div class="ttname"><a href="a00298.html#ga3c3c9fb53ae7823b10fa083909357590">glm::packUint2x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packUint2x8(u8vec2 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed unsigned integer. </div></div>
<div class="ttc" id="a00298_html_gab6bbd5be3b8e6db538ecb33a7844481c"><div class="ttname"><a href="a00298.html#gab6bbd5be3b8e6db538ecb33a7844481c">glm::packUnorm2x4</a></div><div class="ttdeci">GLM_FUNC_DECL uint8 packUnorm2x4(vec2 const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into unsigned integer values...</div></div>
<div class="ttc" id="a00298_html_ga2ae149c5d2473ac1e5f347bb654a242d"><div class="ttname"><a href="a00298.html#ga2ae149c5d2473ac1e5f347bb654a242d">glm::unpackUnorm4x16</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackUnorm4x16(uint64 p)</div><div class="ttdoc">First, unpacks a single 64-bit unsigned integer p into four 16-bit unsigned integers. </div></div>
<div class="ttc" id="a00298_html_gaccde055882918a3175de82f4ca8b7d8e"><div class="ttname"><a href="a00298.html#gaccde055882918a3175de82f4ca8b7d8e">glm::unpackInt2x16</a></div><div class="ttdeci">GLM_FUNC_DECL i16vec2 unpackInt2x16(int p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga1cd8d2038cdd33a860801aa155a26221"><div class="ttname"><a href="a00298.html#ga1cd8d2038cdd33a860801aa155a26221">glm::unpackInt4x8</a></div><div class="ttdeci">GLM_FUNC_DECL i8vec4 unpackInt4x8(int32 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00281_html_gabe65c061834f61b4f7cb6037b19006a4"><div class="ttname"><a href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">glm::vec2</a></div><div class="ttdeci">vec&lt; 2, float, defaultp &gt; vec2</div><div class="ttdoc">2 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00208_source.html#l00015">vector_float2.hpp:15</a></div></div>
<div class="ttc" id="a00298_html_gab173834ef14cfc23a96a959f3ff4b8dc"><div class="ttname"><a href="a00298.html#gab173834ef14cfc23a96a959f3ff4b8dc">glm::unpackUint4x16</a></div><div class="ttdeci">GLM_FUNC_DECL u16vec4 unpackUint4x16(uint64 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00304_html_gad06935764d78f43f9d542c784c2212ec"><div class="ttname"><a href="a00304.html#gad06935764d78f43f9d542c784c2212ec">glm::i8vec2</a></div><div class="ttdeci">vec&lt; 2, i8, defaultp &gt; i8vec2</div><div class="ttdoc">8 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00238">fwd.hpp:238</a></div></div>
<div class="ttc" id="a00298_html_gaf942ff11b65e83eb5f77e68329ebc6ab"><div class="ttname"><a href="a00298.html#gaf942ff11b65e83eb5f77e68329ebc6ab">glm::unpackUint2x32</a></div><div class="ttdeci">GLM_FUNC_DECL u32vec2 unpackUint2x32(uint64 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga30d6b2f1806315bcd6047131f547d33b"><div class="ttname"><a href="a00298.html#ga30d6b2f1806315bcd6047131f547d33b">glm::unpackHalf</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, float, Q &gt; unpackHalf(vec&lt; L, uint16, Q &gt; const &amp;p)</div><div class="ttdoc">Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bi...</div></div>
<div class="ttc" id="a00298_html_ga1f63c264e7ab63264e2b2a99fd393897"><div class="ttname"><a href="a00298.html#ga1f63c264e7ab63264e2b2a99fd393897">glm::packUnorm4x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint64 packUnorm4x16(vec4 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 16-bit integer values...</div></div>
<div class="ttc" id="a00298_html_gaccd3f27e6ba5163eb7aa9bc8ff96251a"><div class="ttname"><a href="a00298.html#gaccd3f27e6ba5163eb7aa9bc8ff96251a">glm::packUnorm</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, uintType, Q &gt; packUnorm(vec&lt; L, floatType, Q &gt; const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into unsigned integer values...</div></div>
<div class="ttc" id="a00298_html_ga2b1fd1e854705b1345e98409e0a25e50"><div class="ttname"><a href="a00298.html#ga2b1fd1e854705b1345e98409e0a25e50">glm::unpackF2x11_1x10</a></div><div class="ttdeci">GLM_FUNC_DECL vec3 unpackF2x11_1x10(uint32 p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and ...</div></div>
<div class="ttc" id="a00298_html_ga4b2fa60df3460403817d28b082ee0736"><div class="ttname"><a href="a00298.html#ga4b2fa60df3460403817d28b082ee0736">glm::packUnorm1x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint8 packUnorm1x8(float v)</div><div class="ttdoc">First, converts the normalized floating-point value v into a 8-bit integer value. ...</div></div>
<div class="ttc" id="a00298_html_gaa7600a6c71784b637a410869d2a5adcd"><div class="ttname"><a href="a00298.html#gaa7600a6c71784b637a410869d2a5adcd">glm::unpackUint2x8</a></div><div class="ttdeci">GLM_FUNC_DECL u8vec2 unpackUint2x8(uint16 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga5156d3060355fe332865da2c7f78815f"><div class="ttname"><a href="a00298.html#ga5156d3060355fe332865da2c7f78815f">glm::unpackUnorm3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackUnorm3x10_1x2(uint32 p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers. </div></div>
<div class="ttc" id="a00298_html_ga2e50476132fe5f27f08e273d9c70d85b"><div class="ttname"><a href="a00298.html#ga2e50476132fe5f27f08e273d9c70d85b">glm::unpackUnorm2x4</a></div><div class="ttdeci">GLM_FUNC_DECL vec2 unpackUnorm2x4(uint8 p)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00298_html_ga0466daf4c90f76cc64b3f105ce727295"><div class="ttname"><a href="a00298.html#ga0466daf4c90f76cc64b3f105ce727295">glm::packRGBM</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, Q &gt; packRGBM(vec&lt; 3, T, Q &gt; const &amp;rgb)</div><div class="ttdoc">Returns an unsigned integer vector obtained by converting the components of a floating-point vector t...</div></div>
<div class="ttc" id="a00304_html_ga550831bfc26d1e0101c1cb3d79938c06"><div class="ttname"><a href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">glm::i16vec4</a></div><div class="ttdeci">vec&lt; 4, i16, defaultp &gt; i16vec4</div><div class="ttdoc">16 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00260">fwd.hpp:260</a></div></div>
<div class="ttc" id="a00298_html_gaaddf9c353528fe896106f7181219c7f4"><div class="ttname"><a href="a00298.html#gaaddf9c353528fe896106f7181219c7f4">glm::unpackSnorm4x16</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackSnorm4x16(uint64 p)</div><div class="ttdoc">First, unpacks a single 64-bit unsigned integer p into four 16-bit signed integers. </div></div>
<div class="ttc" id="a00298_html_ga8a1ee625d2707c60530fb3fca2980b19"><div class="ttname"><a href="a00298.html#ga8a1ee625d2707c60530fb3fca2980b19">glm::packUnorm3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packUnorm3x10_1x2(vec4 const &amp;v)</div><div class="ttdoc">First, converts the first three components of the normalized floating-point value v into 10-bit unsig...</div></div>
<div class="ttc" id="a00304_html_ga20779a61de2fd526a17f12fe53ec46b1"><div class="ttname"><a href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">glm::u8vec4</a></div><div class="ttdeci">vec&lt; 4, u8, defaultp &gt; u8vec4</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00342">fwd.hpp:342</a></div></div>
<div class="ttc" id="a00304_html_ga4177a44206121dabc8c4ff1c0f544574"><div class="ttname"><a href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">glm::i8vec4</a></div><div class="ttdeci">vec&lt; 4, i8, defaultp &gt; i8vec4</div><div class="ttdoc">8 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00240">fwd.hpp:240</a></div></div>
<div class="ttc" id="a00281_html_ga5abb4603dae0ce58c595e66d9123d812"><div class="ttname"><a href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">glm::ivec4</a></div><div class="ttdeci">vec&lt; 4, int, defaultp &gt; ivec4</div><div class="ttdoc">4 components vector of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00220_source.html#l00015">vector_int4.hpp:15</a></div></div>
<div class="ttc" id="a00298_html_gab297c0bfd38433524791eb0584d8f08d"><div class="ttname"><a href="a00298.html#gab297c0bfd38433524791eb0584d8f08d">glm::unpackInt2x32</a></div><div class="ttdeci">GLM_FUNC_DECL i32vec2 unpackInt2x32(int64 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga7f9abdb50f9be1aa1c14912504a0d98d"><div class="ttname"><a href="a00298.html#ga7f9abdb50f9be1aa1c14912504a0d98d">glm::packUnorm2x3_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL uint8 packUnorm2x3_1x2(vec3 const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into unsigned integer values...</div></div>
<div class="ttc" id="a00298_html_ga9f82737bf2a44bedff1d286b76837886"><div class="ttname"><a href="a00298.html#ga9f82737bf2a44bedff1d286b76837886">glm::packUnorm1x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packUnorm1x16(float v)</div><div class="ttdoc">First, converts the normalized floating-point value v into a 16-bit integer value. </div></div>
<div class="ttc" id="a00281_html_gac215a35481a6597d1bf622a382e9d6e2"><div class="ttname"><a href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">glm::vec4</a></div><div class="ttdeci">vec&lt; 4, float, defaultp &gt; vec4</div><div class="ttdoc">4 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00212_source.html#l00015">vector_float4.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga8b44026374982dcd1e52d22bac99247e"><div class="ttname"><a href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">glm::i32vec2</a></div><div class="ttdeci">vec&lt; 2, i32, defaultp &gt; i32vec2</div><div class="ttdoc">32 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00278">fwd.hpp:278</a></div></div>
<div class="ttc" id="a00298_html_ga1319207e30874fb4931a9ee913983ee1"><div class="ttname"><a href="a00298.html#ga1319207e30874fb4931a9ee913983ee1">glm::unpackUnorm1x8</a></div><div class="ttdeci">GLM_FUNC_DECL float unpackUnorm1x8(uint8 p)</div><div class="ttdoc">Convert a single 8-bit integer to a normalized floating-point value. </div></div>
<div class="ttc" id="a00298_html_ga4851ff86678aa1c7ace9d67846894285"><div class="ttname"><a href="a00298.html#ga4851ff86678aa1c7ace9d67846894285">glm::unpackSnorm1x8</a></div><div class="ttdeci">GLM_FUNC_DECL float unpackSnorm1x8(uint8 p)</div><div class="ttdoc">First, unpacks a single 8-bit unsigned integer p into a single 8-bit signed integers. </div></div>
<div class="ttc" id="a00298_html_gac37dedaba24b00adb4ec6e8f92c19dbf"><div class="ttname"><a href="a00298.html#gac37dedaba24b00adb4ec6e8f92c19dbf">glm::unpackHalf1x16</a></div><div class="ttdeci">GLM_FUNC_DECL float unpackHalf1x16(uint16 v)</div><div class="ttdoc">Returns a floating-point scalar with components obtained by unpacking a 16-bit unsigned integer into ...</div></div>
<div class="ttc" id="a00298_html_ga52c154a9b232b62c22517a700cc0c78c"><div class="ttname"><a href="a00298.html#ga52c154a9b232b62c22517a700cc0c78c">glm::unpackInt4x16</a></div><div class="ttdeci">GLM_FUNC_DECL i16vec4 unpackInt4x16(int64 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga83d34160a5cb7bcb5339823210fc7501"><div class="ttname"><a href="a00298.html#ga83d34160a5cb7bcb5339823210fc7501">glm::unpackUnorm1x16</a></div><div class="ttdeci">GLM_FUNC_DECL float unpackUnorm1x16(uint16 p)</div><div class="ttdoc">First, unpacks a single 16-bit unsigned integer p into a of 16-bit unsigned integers. </div></div>
<div class="ttc" id="a00298_html_ga8b128e89be449fc71336968a66bf6e1a"><div class="ttname"><a href="a00298.html#ga8b128e89be449fc71336968a66bf6e1a">glm::unpackSnorm2x8</a></div><div class="ttdeci">GLM_FUNC_DECL vec2 unpackSnorm2x8(uint16 p)</div><div class="ttdoc">First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit signed integers. </div></div>
<div class="ttc" id="a00304_html_ga2996630ba7b10535af8e065cf326f761"><div class="ttname"><a href="a00304.html#ga2996630ba7b10535af8e065cf326f761">glm::i16vec2</a></div><div class="ttdeci">vec&lt; 2, i16, defaultp &gt; i16vec2</div><div class="ttdoc">16 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00258">fwd.hpp:258</a></div></div>
<div class="ttc" id="a00298_html_ga9a05330e5490be0908d3b117d82aff56"><div class="ttname"><a href="a00298.html#ga9a05330e5490be0908d3b117d82aff56">glm::unpackI3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL ivec4 unpackI3x10_1x2(uint32 p)</div><div class="ttdoc">Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit signed integers...</div></div>
<div class="ttc" id="a00298_html_gab22f8bcfdb5fc65af4701b25f143c1af"><div class="ttname"><a href="a00298.html#gab22f8bcfdb5fc65af4701b25f143c1af">glm::packSnorm1x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packSnorm1x16(float v)</div><div class="ttdoc">First, converts the normalized floating-point value v into 16-bit integer value. </div></div>
<div class="ttc" id="a00298_html_gab9e60ebe3ad3eeced6a9ec6eb876d74e"><div class="ttname"><a href="a00298.html#gab9e60ebe3ad3eeced6a9ec6eb876d74e">glm::unpackF3x9_E1x5</a></div><div class="ttdeci">GLM_FUNC_DECL vec3 unpackF3x9_E1x5(uint32 p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and ...</div></div>
<div class="ttc" id="a00298_html_ga3e6ac9178b59f0b1b2f7599f2183eb7f"><div class="ttname"><a href="a00298.html#ga3e6ac9178b59f0b1b2f7599f2183eb7f">glm::unpackUnorm</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt; unpackUnorm(vec&lt; L, uintType, Q &gt; const &amp;v)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00298_html_gaa54b5855a750d6aeb12c1c902f5939b8"><div class="ttname"><a href="a00298.html#gaa54b5855a750d6aeb12c1c902f5939b8">glm::packSnorm</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, intType, Q &gt; packSnorm(vec&lt; L, floatType, Q &gt; const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into signed integer values...</div></div>
<div class="ttc" id="a00298_html_ga768e0337dd6246773f14aa0a421fe9a8"><div class="ttname"><a href="a00298.html#ga768e0337dd6246773f14aa0a421fe9a8">glm::packUnorm1x5_1x6_1x5</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packUnorm1x5_1x6_1x5(vec3 const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into unsigned integer values...</div></div>
<div class="ttc" id="a00298_html_ga5ff95ff5bc16f396432ab67243dbae4d"><div class="ttname"><a href="a00298.html#ga5ff95ff5bc16f396432ab67243dbae4d">glm::unpackUnorm3x5_1x1</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackUnorm3x5_1x1(uint16 p)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00304_html_ga518b8d948a6b4ddb72f84d5c3b7b6611"><div class="ttname"><a href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, defaultp &gt; u8vec2</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00340">fwd.hpp:340</a></div></div>
<div class="ttc" id="a00298_html_ga7a4fbf79be9740e3c57737bc2af05e5b"><div class="ttname"><a href="a00298.html#ga7a4fbf79be9740e3c57737bc2af05e5b">glm::unpackSnorm3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackSnorm3x10_1x2(uint32 p)</div><div class="ttdoc">First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers. </div></div>
<div class="ttc" id="a00298_html_gab997545661877d2c7362a5084d3897d3"><div class="ttname"><a href="a00298.html#gab997545661877d2c7362a5084d3897d3">glm::packSnorm3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packSnorm3x10_1x2(vec4 const &amp;v)</div><div class="ttdoc">First, converts the first three components of the normalized floating-point value v into 10-bit signe...</div></div>
<div class="ttc" id="a00298_html_ga1989f093a27ae69cf9207145be48b3d7"><div class="ttname"><a href="a00298.html#ga1989f093a27ae69cf9207145be48b3d7">glm::packInt4x16</a></div><div class="ttdeci">GLM_FUNC_DECL int64 packInt4x16(i16vec4 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed integer. </div></div>
<div class="ttc" id="a00298_html_gab3bc08ecfc0f3339be93fb2b3b56d88a"><div class="ttname"><a href="a00298.html#gab3bc08ecfc0f3339be93fb2b3b56d88a">glm::unpackUnorm1x5_1x6_1x5</a></div><div class="ttdeci">GLM_FUNC_DECL vec3 unpackUnorm1x5_1x6_1x5(uint16 p)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00298_html_ga3f648fc205467792dc6d8c59c748f8a6"><div class="ttname"><a href="a00298.html#ga3f648fc205467792dc6d8c59c748f8a6">glm::packF3x9_E1x5</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packF3x9_E1x5(vec3 const &amp;v)</div><div class="ttdoc">First, converts the first two components of the normalized floating-point value v into 11-bit signles...</div></div>
<div class="ttc" id="a00298_html_ga9a666b1c688ab54100061ed06526de6e"><div class="ttname"><a href="a00298.html#ga9a666b1c688ab54100061ed06526de6e">glm::packUnorm2x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packUnorm2x8(vec2 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 8-bit integer values...</div></div>
<div class="ttc" id="a00298_html_ga2ceb62cca347d8ace42ee90317a3f1f9"><div class="ttname"><a href="a00298.html#ga2ceb62cca347d8ace42ee90317a3f1f9">glm::packUint4x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint64 packUint4x16(u16vec4 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed unsigned integer. </div></div>
<div class="ttc" id="a00281_html_ga9c3019b13faf179e4ad3626ea66df334"><div class="ttname"><a href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">glm::vec3</a></div><div class="ttdeci">vec&lt; 3, float, defaultp &gt; vec3</div><div class="ttdoc">3 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00210_source.html#l00015">vector_float3.hpp:15</a></div></div>
<div class="ttc" id="a00298_html_gaec4112086d7fb133bea104a7c237de52"><div class="ttname"><a href="a00298.html#gaec4112086d7fb133bea104a7c237de52">glm::packUnorm3x5_1x1</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packUnorm3x5_1x1(vec4 const &amp;v)</div><div class="ttdoc">Convert each component of the normalized floating-point vector into unsigned integer values...</div></div>
<div class="ttc" id="a00298_html_ga637cbe3913dd95c6e7b4c99c61bd611f"><div class="ttname"><a href="a00298.html#ga637cbe3913dd95c6e7b4c99c61bd611f">glm::unpackUnorm2x8</a></div><div class="ttdeci">GLM_FUNC_DECL vec2 unpackUnorm2x8(uint16 p)</div><div class="ttdoc">First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit unsigned integers. </div></div>
<div class="ttc" id="a00298_html_ga035bbbeab7ec2b28c0529757395b645b"><div class="ttname"><a href="a00298.html#ga035bbbeab7ec2b28c0529757395b645b">glm::unpackUint2x16</a></div><div class="ttdeci">GLM_FUNC_DECL u16vec2 unpackUint2x16(uint p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga5eecc9e8cbaf51ac6cf57501e670ee19"><div class="ttname"><a href="a00298.html#ga5eecc9e8cbaf51ac6cf57501e670ee19">glm::packUint2x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint packUint2x16(u16vec2 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed unsigned integer. </div></div>
<div class="ttc" id="a00298_html_ga358943934d21da947d5bcc88c2ab7832"><div class="ttname"><a href="a00298.html#ga358943934d21da947d5bcc88c2ab7832">glm::packSnorm4x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint64 packSnorm4x16(vec4 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 16-bit integer values...</div></div>
<div class="ttc" id="a00298_html_gaa864081097b86e83d8e4a4d79c382b22"><div class="ttname"><a href="a00298.html#gaa864081097b86e83d8e4a4d79c382b22">glm::packUint2x32</a></div><div class="ttdeci">GLM_FUNC_DECL uint64 packUint2x32(u32vec2 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed unsigned integer. </div></div>
<div class="ttc" id="a00281_html_gaa57e96bb337867329d5f43bcc27c1095"><div class="ttname"><a href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">glm::uvec4</a></div><div class="ttdeci">vec&lt; 4, unsigned int, defaultp &gt; uvec4</div><div class="ttdoc">4 components vector of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00232_source.html#l00015">vector_uint4.hpp:15</a></div></div>
<div class="ttc" id="a00298_html_ga57dfc41b2eb20b0ac00efae7d9c49dcd"><div class="ttname"><a href="a00298.html#ga57dfc41b2eb20b0ac00efae7d9c49dcd">glm::unpackHalf4x16</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackHalf4x16(uint64 p)</div><div class="ttdoc">Returns a four-component floating-point vector with components obtained by unpacking a 64-bit unsigne...</div></div>
<div class="ttc" id="a00263_html_gab630f76c26b50298187f7889104d4b9c"><div class="ttname"><a href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a></div><div class="ttdeci">detail::uint64 uint64</div><div class="ttdoc">64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00067">scalar_uint_sized.hpp:67</a></div></div>
<div class="ttc" id="a00298_html_gac58ee89d0e224bb6df5e8bbb18843a2d"><div class="ttname"><a href="a00298.html#gac58ee89d0e224bb6df5e8bbb18843a2d">glm::unpackUnorm4x4</a></div><div class="ttdeci">GLM_FUNC_DECL vec4 unpackUnorm4x4(uint16 p)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00298_html_gafe2f7b39caf8f5ec555e1c059ec530e6"><div class="ttname"><a href="a00298.html#gafe2f7b39caf8f5ec555e1c059ec530e6">glm::packHalf4x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint64 packHalf4x16(vec4 const &amp;v)</div><div class="ttdoc">Returns an unsigned integer obtained by converting the components of a four-component floating-point ...</div></div>
<div class="ttc" id="a00174_html"><div class="ttname"><a href="a00174.html">type_precision.hpp</a></div><div class="ttdoc">GLM_GTC_type_precision </div></div>
<div class="ttc" id="a00298_html_gab0c59f1e259fca9e68adb2207a6b665e"><div class="ttname"><a href="a00298.html#gab0c59f1e259fca9e68adb2207a6b665e">glm::unpackInt2x8</a></div><div class="ttdeci">GLM_FUNC_DECL i8vec2 unpackInt2x8(int16 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00260_html_gaff5189f97f9e842d9636a0f240001b2e"><div class="ttname"><a href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">glm::int64</a></div><div class="ttdeci">detail::int64 int64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00067">scalar_int_sized.hpp:67</a></div></div>
<div class="ttc" id="a00298_html_ga6abd5a9014df3b5ce4059008d2491260"><div class="ttname"><a href="a00298.html#ga6abd5a9014df3b5ce4059008d2491260">glm::unpackUnorm2x3_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL vec3 unpackUnorm2x3_1x2(uint8 p)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00298_html_ga4944ad465ff950e926d49621f916c78d"><div class="ttname"><a href="a00298.html#ga4944ad465ff950e926d49621f916c78d">glm::packF2x11_1x10</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packF2x11_1x10(vec3 const &amp;v)</div><div class="ttdoc">First, converts the first two components of the normalized floating-point value v into 11-bit signles...</div></div>
<div class="ttc" id="a00298_html_ga48df3042a7d079767f5891a1bfd8a60a"><div class="ttname"><a href="a00298.html#ga48df3042a7d079767f5891a1bfd8a60a">glm::unpackU3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL uvec4 unpackU3x10_1x2(uint32 p)</div><div class="ttdoc">Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit unsigned integers...</div></div>
<div class="ttc" id="a00298_html_ga43f2093b6ff192a79058ff7834fc3528"><div class="ttname"><a href="a00298.html#ga43f2093b6ff192a79058ff7834fc3528">glm::packHalf1x16</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packHalf1x16(float v)</div><div class="ttdoc">Returns an unsigned integer obtained by converting the components of a floating-point scalar to the 1...</div></div>
<div class="ttc" id="a00298_html_ga6d49b31e5c3f9df8e1f99ab62b999482"><div class="ttname"><a href="a00298.html#ga6d49b31e5c3f9df8e1f99ab62b999482">glm::unpackSnorm</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt; unpackSnorm(vec&lt; L, intType, Q &gt; const &amp;v)</div><div class="ttdoc">Convert a packed integer to a normalized floating-point vector. </div></div>
<div class="ttc" id="a00298_html_ga5c1ec97894b05ea21a05aea4f0204a02"><div class="ttname"><a href="a00298.html#ga5c1ec97894b05ea21a05aea4f0204a02">glm::unpackRGBM</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, Q &gt; unpackRGBM(vec&lt; 4, T, Q &gt; const &amp;rgbm)</div><div class="ttdoc">Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bi...</div></div>
<div class="ttc" id="a00298_html_ga6be3cfb2cce3702f03e91bbeb5286d7e"><div class="ttname"><a href="a00298.html#ga6be3cfb2cce3702f03e91bbeb5286d7e">glm::packSnorm2x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint16 packSnorm2x8(vec2 const &amp;v)</div><div class="ttdoc">First, converts each component of the normalized floating-point value v into 8-bit integer values...</div></div>
<div class="ttc" id="a00298_html_gae3592e0795e62aaa1865b3a10496a7a1"><div class="ttname"><a href="a00298.html#gae3592e0795e62aaa1865b3a10496a7a1">glm::packSnorm1x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint8 packSnorm1x8(float s)</div><div class="ttdoc">First, converts the normalized floating-point value v into 8-bit integer value. </div></div>
<div class="ttc" id="a00298_html_gaf6dc0e4341810a641c7ed08f10e335d1"><div class="ttname"><a href="a00298.html#gaf6dc0e4341810a641c7ed08f10e335d1">glm::unpackUint4x8</a></div><div class="ttdeci">GLM_FUNC_DECL u8vec4 unpackUint4x8(uint32 p)</div><div class="ttdoc">Convert a packed integer into an integer vector. </div></div>
<div class="ttc" id="a00298_html_ga06ecb6afb902dba45419008171db9023"><div class="ttname"><a href="a00298.html#ga06ecb6afb902dba45419008171db9023">glm::packI3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packI3x10_1x2(ivec4 const &amp;v)</div><div class="ttdoc">Returns an unsigned integer obtained by converting the components of a four-component signed integer ...</div></div>
<div class="ttc" id="a00298_html_ga8884b1f2292414f36d59ef3be5d62914"><div class="ttname"><a href="a00298.html#ga8884b1f2292414f36d59ef3be5d62914">glm::packInt2x8</a></div><div class="ttdeci">GLM_FUNC_DECL int16 packInt2x8(i8vec2 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed integer. </div></div>
<div class="ttc" id="a00298_html_gad1e4c8a9e67d86b61a6eec86703a827a"><div class="ttname"><a href="a00298.html#gad1e4c8a9e67d86b61a6eec86703a827a">glm::packInt2x32</a></div><div class="ttdeci">GLM_FUNC_DECL int64 packInt2x32(i32vec2 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed integer. </div></div>
<div class="ttc" id="a00298_html_gaa0fe2f09aeb403cd66c1a062f58861ab"><div class="ttname"><a href="a00298.html#gaa0fe2f09aeb403cd66c1a062f58861ab">glm::packUint4x8</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packUint4x8(u8vec4 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed unsigned integer. </div></div>
<div class="ttc" id="a00298_html_gada3d88d59f0f458f9c51a9fd359a4bc0"><div class="ttname"><a href="a00298.html#gada3d88d59f0f458f9c51a9fd359a4bc0">glm::packU3x10_1x2</a></div><div class="ttdeci">GLM_FUNC_DECL uint32 packU3x10_1x2(uvec4 const &amp;v)</div><div class="ttdoc">Returns an unsigned integer obtained by converting the components of a four-component unsigned intege...</div></div>
<div class="ttc" id="a00304_html_ga2a266e46ee218d0c680f12b35c500cc0"><div class="ttname"><a href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, defaultp &gt; u32vec2</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00380">fwd.hpp:380</a></div></div>
<div class="ttc" id="a00298_html_ga3644163cf3a47bf1d4af1f4b03013a7e"><div class="ttname"><a href="a00298.html#ga3644163cf3a47bf1d4af1f4b03013a7e">glm::packInt2x16</a></div><div class="ttdeci">GLM_FUNC_DECL int packInt2x16(i16vec2 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed integer. </div></div>
<div class="ttc" id="a00298_html_ga2d8bbce673ebc04831c1fb05c47f5251"><div class="ttname"><a href="a00298.html#ga2d8bbce673ebc04831c1fb05c47f5251">glm::packHalf</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, uint16, Q &gt; packHalf(vec&lt; L, float, Q &gt; const &amp;v)</div><div class="ttdoc">Returns an unsigned integer vector obtained by converting the components of a floating-point vector t...</div></div>
<div class="ttc" id="a00304_html_ga529496d75775fb656a07993ea9af2450"><div class="ttname"><a href="a00304.html#ga529496d75775fb656a07993ea9af2450">glm::u16vec4</a></div><div class="ttdeci">vec&lt; 4, u16, defaultp &gt; u16vec4</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00362">fwd.hpp:362</a></div></div>
<div class="ttc" id="a00298_html_gaf2238401d5ce2aaade1a44ba19709072"><div class="ttname"><a href="a00298.html#gaf2238401d5ce2aaade1a44ba19709072">glm::packInt4x8</a></div><div class="ttdeci">GLM_FUNC_DECL int32 packInt4x8(i8vec4 const &amp;v)</div><div class="ttdoc">Convert each component from an integer vector into a packed integer. </div></div>
<div class="ttc" id="a00304_html_ga2a78447eb9d66a114b193f4a25899c16"><div class="ttname"><a href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, defaultp &gt; u16vec2</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00360">fwd.hpp:360</a></div></div>
<div class="ttc" id="a00298_html_ga96dd15002370627a443c835ab03a766c"><div class="ttname"><a href="a00298.html#ga96dd15002370627a443c835ab03a766c">glm::unpackSnorm1x16</a></div><div class="ttdeci">GLM_FUNC_DECL float unpackSnorm1x16(uint16 p)</div><div class="ttdoc">First, unpacks a single 16-bit unsigned integer p into a single 16-bit signed integers. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
