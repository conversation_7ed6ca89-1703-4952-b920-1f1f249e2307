<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: bitfield.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">bitfield.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00009.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_int_sized.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_uint_sized.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;../detail/_vectorize.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00174.html">type_precision.hpp</a>&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_bitfield extension included&quot;)</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;{</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00288.html#ga2e64e3b922a296033b825311e7f5fff1">mask</a>(genIUType Bits);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00288.html#ga2e64e3b922a296033b825311e7f5fff1">mask</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00288.html#ga590488e1fc00a6cfe5d3bcaf93fbfe88">bitfieldRotateRight</a>(genIUType In, <span class="keywordtype">int</span> Shift);</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00288.html#ga590488e1fc00a6cfe5d3bcaf93fbfe88">bitfieldRotateRight</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; In, <span class="keywordtype">int</span> Shift);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00288.html#gae186317091b1a39214ebf79008d44a1e">bitfieldRotateLeft</a>(genIUType In, <span class="keywordtype">int</span> Shift);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00288.html#gae186317091b1a39214ebf79008d44a1e">bitfieldRotateLeft</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; In, <span class="keywordtype">int</span> Shift);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00288.html#ga3e96dd1f0a4bc892f063251ed118c0c1">bitfieldFillOne</a>(genIUType Value, <span class="keywordtype">int</span> FirstBit, <span class="keywordtype">int</span> BitCount);</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00288.html#ga3e96dd1f0a4bc892f063251ed118c0c1">bitfieldFillOne</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Value, <span class="keywordtype">int</span> FirstBit, <span class="keywordtype">int</span> BitCount);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genIUType&gt;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        GLM_FUNC_DECL genIUType <a class="code" href="a00288.html#ga0d16c9acef4be79ea9b47c082a0cf7c2">bitfieldFillZero</a>(genIUType Value, <span class="keywordtype">int</span> FirstBit, <span class="keywordtype">int</span> BitCount);</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00288.html#ga0d16c9acef4be79ea9b47c082a0cf7c2">bitfieldFillZero</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Value, <span class="keywordtype">int</span> FirstBit, <span class="keywordtype">int</span> BitCount);</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        GLM_FUNC_DECL int16 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int8 x, int8 y);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint8 x, uint8 y);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        GLM_FUNC_DECL uint16 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(<a class="code" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a> <a class="code" href="a00288.html#ga8dbb8c87092f33bd815dd8a840be5d60">bitfieldDeinterleave</a>(<a class="code" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">glm::uint16</a> x);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        GLM_FUNC_DECL int32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int16 x, int16 y);</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint16 x, uint16 y);</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(<a class="code" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a> <a class="code" href="a00288.html#ga8dbb8c87092f33bd815dd8a840be5d60">bitfieldDeinterleave</a>(<a class="code" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">glm::uint32</a> x);</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int32 x, int32 y);</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint32 x, uint32 y);</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(<a class="code" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a> <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a> <a class="code" href="a00288.html#ga8dbb8c87092f33bd815dd8a840be5d60">bitfieldDeinterleave</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a> x);</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;        GLM_FUNC_DECL int32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int8 x, int8 y, int8 z);</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint8 x, uint8 y, uint8 z);</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int16 x, int16 y, int16 z);</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint16 x, uint16 y, uint16 z);</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int32 x, int32 y, int32 z);</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint32 x, uint32 y, uint32 z);</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        GLM_FUNC_DECL int32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int8 x, int8 y, int8 z, int8 w);</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;        GLM_FUNC_DECL uint32 <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint8 x, uint8 y, uint8 z, uint8 w);</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(int16 x, int16 y, int16 z, int16 w);</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;        GLM_FUNC_DECL <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> <a class="code" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a>(uint16 x, uint16 y, uint16 z, uint16 w);</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;<span class="preprocessor">#include &quot;bitfield.inl&quot;</span></div>
<div class="ttc" id="a00263_html_ga1134b580f8da4de94ca6b1de4d37975e"><div class="ttname"><a href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">glm::uint32</a></div><div class="ttdeci">detail::uint32 uint32</div><div class="ttdoc">32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00064">scalar_uint_sized.hpp:64</a></div></div>
<div class="ttc" id="a00288_html_gafca8768671a14c8016facccb66a89f26"><div class="ttname"><a href="a00288.html#gafca8768671a14c8016facccb66a89f26">glm::bitfieldInterleave</a></div><div class="ttdeci">GLM_FUNC_DECL uint64 bitfieldInterleave(uint16 x, uint16 y, uint16 z, uint16 w)</div><div class="ttdoc">Interleaves the bits of x, y, z and w. </div></div>
<div class="ttc" id="a00288_html_ga8dbb8c87092f33bd815dd8a840be5d60"><div class="ttname"><a href="a00288.html#ga8dbb8c87092f33bd815dd8a840be5d60">glm::bitfieldDeinterleave</a></div><div class="ttdeci">GLM_FUNC_DECL glm::u32vec2 bitfieldDeinterleave(glm::uint64 x)</div><div class="ttdoc">Deinterleaves the bits of x. </div></div>
<div class="ttc" id="a00288_html_ga0d16c9acef4be79ea9b47c082a0cf7c2"><div class="ttname"><a href="a00288.html#ga0d16c9acef4be79ea9b47c082a0cf7c2">glm::bitfieldFillZero</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldFillZero(vec&lt; L, T, Q &gt; const &amp;Value, int FirstBit, int BitCount)</div><div class="ttdoc">Set to 0 a range of bits. </div></div>
<div class="ttc" id="a00263_html_ga05f6b0ae8f6a6e135b0e290c25fe0e4e"><div class="ttname"><a href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">glm::uint16</a></div><div class="ttdeci">detail::uint16 uint16</div><div class="ttdoc">16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00061">scalar_uint_sized.hpp:61</a></div></div>
<div class="ttc" id="a00304_html_ga518b8d948a6b4ddb72f84d5c3b7b6611"><div class="ttname"><a href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, defaultp &gt; u8vec2</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00340">fwd.hpp:340</a></div></div>
<div class="ttc" id="a00288_html_gae186317091b1a39214ebf79008d44a1e"><div class="ttname"><a href="a00288.html#gae186317091b1a39214ebf79008d44a1e">glm::bitfieldRotateLeft</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldRotateLeft(vec&lt; L, T, Q &gt; const &amp;In, int Shift)</div><div class="ttdoc">Rotate all bits to the left. </div></div>
<div class="ttc" id="a00288_html_ga2e64e3b922a296033b825311e7f5fff1"><div class="ttname"><a href="a00288.html#ga2e64e3b922a296033b825311e7f5fff1">glm::mask</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; mask(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a mask of &#39;count&#39; bits. </div></div>
<div class="ttc" id="a00263_html_gab630f76c26b50298187f7889104d4b9c"><div class="ttname"><a href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a></div><div class="ttdeci">detail::uint64 uint64</div><div class="ttdoc">64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00067">scalar_uint_sized.hpp:67</a></div></div>
<div class="ttc" id="a00288_html_ga3e96dd1f0a4bc892f063251ed118c0c1"><div class="ttname"><a href="a00288.html#ga3e96dd1f0a4bc892f063251ed118c0c1">glm::bitfieldFillOne</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldFillOne(vec&lt; L, T, Q &gt; const &amp;Value, int FirstBit, int BitCount)</div><div class="ttdoc">Set to 1 a range of bits. </div></div>
<div class="ttc" id="a00174_html"><div class="ttname"><a href="a00174.html">type_precision.hpp</a></div><div class="ttdoc">GLM_GTC_type_precision </div></div>
<div class="ttc" id="a00260_html_gaff5189f97f9e842d9636a0f240001b2e"><div class="ttname"><a href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">glm::int64</a></div><div class="ttdeci">detail::int64 int64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00067">scalar_int_sized.hpp:67</a></div></div>
<div class="ttc" id="a00304_html_ga2a266e46ee218d0c680f12b35c500cc0"><div class="ttname"><a href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, defaultp &gt; u32vec2</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00380">fwd.hpp:380</a></div></div>
<div class="ttc" id="a00288_html_ga590488e1fc00a6cfe5d3bcaf93fbfe88"><div class="ttname"><a href="a00288.html#ga590488e1fc00a6cfe5d3bcaf93fbfe88">glm::bitfieldRotateRight</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; bitfieldRotateRight(vec&lt; L, T, Q &gt; const &amp;In, int Shift)</div><div class="ttdoc">Rotate all bits to the right. </div></div>
<div class="ttc" id="a00304_html_ga2a78447eb9d66a114b193f4a25899c16"><div class="ttname"><a href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, defaultp &gt; u16vec2</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00360">fwd.hpp:360</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
