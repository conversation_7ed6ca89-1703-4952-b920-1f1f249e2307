<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: common.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">common.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00280.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00015_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga439e60a72eadecfeda2df5449c613a64"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga439e60a72eadecfeda2df5449c613a64"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga439e60a72eadecfeda2df5449c613a64">abs</a> (genType x)</td></tr>
<tr class="memdesc:ga439e60a72eadecfeda2df5449c613a64"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x if x &gt;= 0; otherwise, it returns -x.  <a href="a00241.html#ga439e60a72eadecfeda2df5449c613a64">More...</a><br /></td></tr>
<tr class="separator:ga439e60a72eadecfeda2df5449c613a64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81d3abddd0ef0c8de579bc541ecadab6"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga81d3abddd0ef0c8de579bc541ecadab6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga81d3abddd0ef0c8de579bc541ecadab6">abs</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga81d3abddd0ef0c8de579bc541ecadab6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x if x &gt;= 0; otherwise, it returns -x.  <a href="a00241.html#ga81d3abddd0ef0c8de579bc541ecadab6">More...</a><br /></td></tr>
<tr class="separator:ga81d3abddd0ef0c8de579bc541ecadab6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb9d2a645a23aca12d4d6de0104b7657"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafb9d2a645a23aca12d4d6de0104b7657"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gafb9d2a645a23aca12d4d6de0104b7657">ceil</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gafb9d2a645a23aca12d4d6de0104b7657"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer that is greater than or equal to x.  <a href="a00241.html#gafb9d2a645a23aca12d4d6de0104b7657">More...</a><br /></td></tr>
<tr class="separator:gafb9d2a645a23aca12d4d6de0104b7657"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cd77683da6361e297c56443fc70806d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga7cd77683da6361e297c56443fc70806d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga7cd77683da6361e297c56443fc70806d">clamp</a> (genType x, genType minVal, genType maxVal)</td></tr>
<tr class="memdesc:ga7cd77683da6361e297c56443fc70806d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x using the floating-point values minVal and maxVal.  <a href="a00241.html#ga7cd77683da6361e297c56443fc70806d">More...</a><br /></td></tr>
<tr class="separator:ga7cd77683da6361e297c56443fc70806d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafba2e0674deb5953878d89483cd6323d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafba2e0674deb5953878d89483cd6323d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gafba2e0674deb5953878d89483cd6323d">clamp</a> (vec&lt; L, T, Q &gt; const &amp;x, T minVal, T maxVal)</td></tr>
<tr class="memdesc:gafba2e0674deb5953878d89483cd6323d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x using the floating-point values minVal and maxVal.  <a href="a00241.html#gafba2e0674deb5953878d89483cd6323d">More...</a><br /></td></tr>
<tr class="separator:gafba2e0674deb5953878d89483cd6323d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0f2f12e9108b09e22a3f0b2008a0b5d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa0f2f12e9108b09e22a3f0b2008a0b5d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gaa0f2f12e9108b09e22a3f0b2008a0b5d">clamp</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;minVal, vec&lt; L, T, Q &gt; const &amp;maxVal)</td></tr>
<tr class="memdesc:gaa0f2f12e9108b09e22a3f0b2008a0b5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x using the floating-point values minVal and maxVal.  <a href="a00241.html#gaa0f2f12e9108b09e22a3f0b2008a0b5d">More...</a><br /></td></tr>
<tr class="separator:gaa0f2f12e9108b09e22a3f0b2008a0b5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1425c1c3160ec51214b03a0469a3013d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00241.html#ga1425c1c3160ec51214b03a0469a3013d">floatBitsToInt</a> (float const &amp;v)</td></tr>
<tr class="memdesc:ga1425c1c3160ec51214b03a0469a3013d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a signed integer value representing the encoding of a floating-point value.  <a href="a00241.html#ga1425c1c3160ec51214b03a0469a3013d">More...</a><br /></td></tr>
<tr class="separator:ga1425c1c3160ec51214b03a0469a3013d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99f7d62f78ac5ea3b49bae715c9488ed"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga99f7d62f78ac5ea3b49bae715c9488ed"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga99f7d62f78ac5ea3b49bae715c9488ed">floatBitsToInt</a> (vec&lt; L, float, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga99f7d62f78ac5ea3b49bae715c9488ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a signed integer value representing the encoding of a floating-point value.  <a href="a00241.html#ga99f7d62f78ac5ea3b49bae715c9488ed">More...</a><br /></td></tr>
<tr class="separator:ga99f7d62f78ac5ea3b49bae715c9488ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70e0271c34af52f3100c7960e18c3f2b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00241.html#ga70e0271c34af52f3100c7960e18c3f2b">floatBitsToUint</a> (float const &amp;v)</td></tr>
<tr class="memdesc:ga70e0271c34af52f3100c7960e18c3f2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a unsigned integer value representing the encoding of a floating-point value.  <a href="a00241.html#ga70e0271c34af52f3100c7960e18c3f2b">More...</a><br /></td></tr>
<tr class="separator:ga70e0271c34af52f3100c7960e18c3f2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49418ba4c8a60fbbb5d57b705f3e26db"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga49418ba4c8a60fbbb5d57b705f3e26db"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uint, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga49418ba4c8a60fbbb5d57b705f3e26db">floatBitsToUint</a> (vec&lt; L, float, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga49418ba4c8a60fbbb5d57b705f3e26db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a unsigned integer value representing the encoding of a floating-point value.  <a href="a00241.html#ga49418ba4c8a60fbbb5d57b705f3e26db">More...</a><br /></td></tr>
<tr class="separator:ga49418ba4c8a60fbbb5d57b705f3e26db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa9d0742639e85b29c7c5de11cfd6840d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa9d0742639e85b29c7c5de11cfd6840d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d">floor</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gaa9d0742639e85b29c7c5de11cfd6840d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer that is less then or equal to x.  <a href="a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d">More...</a><br /></td></tr>
<tr class="separator:gaa9d0742639e85b29c7c5de11cfd6840d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0f444d4b81cc53c3b6edf5aa25078c2"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gad0f444d4b81cc53c3b6edf5aa25078c2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gad0f444d4b81cc53c3b6edf5aa25078c2">fma</a> (genType const &amp;a, genType const &amp;b, genType const &amp;c)</td></tr>
<tr class="memdesc:gad0f444d4b81cc53c3b6edf5aa25078c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes and returns a * b + c.  <a href="a00241.html#gad0f444d4b81cc53c3b6edf5aa25078c2">More...</a><br /></td></tr>
<tr class="separator:gad0f444d4b81cc53c3b6edf5aa25078c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8ba89e40e55ae5cdf228548f9b7639c7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga8ba89e40e55ae5cdf228548f9b7639c7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga8ba89e40e55ae5cdf228548f9b7639c7">fract</a> (genType x)</td></tr>
<tr class="memdesc:ga8ba89e40e55ae5cdf228548f9b7639c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return x - floor(x).  <a href="a00241.html#ga8ba89e40e55ae5cdf228548f9b7639c7">More...</a><br /></td></tr>
<tr class="separator:ga8ba89e40e55ae5cdf228548f9b7639c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2df623004f634b440d61e018d62c751b"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2df623004f634b440d61e018d62c751b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga2df623004f634b440d61e018d62c751b">fract</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga2df623004f634b440d61e018d62c751b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return x - floor(x).  <a href="a00241.html#ga2df623004f634b440d61e018d62c751b">More...</a><br /></td></tr>
<tr class="separator:ga2df623004f634b440d61e018d62c751b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaddf5ef73283c171730e0bcc11833fa81"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaddf5ef73283c171730e0bcc11833fa81"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gaddf5ef73283c171730e0bcc11833fa81">frexp</a> (genType x, int &amp;exp)</td></tr>
<tr class="memdesc:gaddf5ef73283c171730e0bcc11833fa81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Splits x into a floating-point significand in the range [0.5, 1.0) and an integral exponent of two, such that: x = significand * exp(2, exponent)  <a href="a00241.html#gaddf5ef73283c171730e0bcc11833fa81">More...</a><br /></td></tr>
<tr class="separator:gaddf5ef73283c171730e0bcc11833fa81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4fb7c21c2dce064b26fd9ccdaf9adcd4"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00241.html#ga4fb7c21c2dce064b26fd9ccdaf9adcd4">intBitsToFloat</a> (int const &amp;v)</td></tr>
<tr class="memdesc:ga4fb7c21c2dce064b26fd9ccdaf9adcd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point value corresponding to a signed integer encoding of a floating-point value.  <a href="a00241.html#ga4fb7c21c2dce064b26fd9ccdaf9adcd4">More...</a><br /></td></tr>
<tr class="separator:ga4fb7c21c2dce064b26fd9ccdaf9adcd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a0a8291a1cf3e1c2aee33030a1bd7b0"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga7a0a8291a1cf3e1c2aee33030a1bd7b0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, float, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0">intBitsToFloat</a> (vec&lt; L, int, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga7a0a8291a1cf3e1c2aee33030a1bd7b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point value corresponding to a signed integer encoding of a floating-point value.  <a href="a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0">More...</a><br /></td></tr>
<tr class="separator:ga7a0a8291a1cf3e1c2aee33030a1bd7b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2885587c23a106301f20443896365b62"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2885587c23a106301f20443896365b62"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga2885587c23a106301f20443896365b62">isinf</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga2885587c23a106301f20443896365b62"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if x holds a positive infinity or negative infinity representation in the underlying implementation's set of floating point representations.  <a href="a00241.html#ga2885587c23a106301f20443896365b62">More...</a><br /></td></tr>
<tr class="separator:ga2885587c23a106301f20443896365b62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga29ef934c00306490de837b4746b4e14d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga29ef934c00306490de837b4746b4e14d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga29ef934c00306490de837b4746b4e14d">isnan</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga29ef934c00306490de837b4746b4e14d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if x holds a NaN (not a number) representation in the underlying implementation's set of floating point representations.  <a href="a00241.html#ga29ef934c00306490de837b4746b4e14d">More...</a><br /></td></tr>
<tr class="separator:ga29ef934c00306490de837b4746b4e14d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3010e0a0c35a1b514540f2fb579c58c"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gac3010e0a0c35a1b514540f2fb579c58c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gac3010e0a0c35a1b514540f2fb579c58c">ldexp</a> (genType const &amp;x, int const &amp;exp)</td></tr>
<tr class="memdesc:gac3010e0a0c35a1b514540f2fb579c58c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a floating-point number from x and the corresponding integral exponent of two in exp, returning: significand * exp(2, exponent)  <a href="a00241.html#gac3010e0a0c35a1b514540f2fb579c58c">More...</a><br /></td></tr>
<tr class="separator:gac3010e0a0c35a1b514540f2fb579c58c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae02d42887fc5570451f880e3c624b9ac"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gae02d42887fc5570451f880e3c624b9ac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gae02d42887fc5570451f880e3c624b9ac">max</a> (genType x, genType y)</td></tr>
<tr class="memdesc:gae02d42887fc5570451f880e3c624b9ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00241.html#gae02d42887fc5570451f880e3c624b9ac">More...</a><br /></td></tr>
<tr class="separator:gae02d42887fc5570451f880e3c624b9ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e45d6e60d1c36edb00c52edeea0f31"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga03e45d6e60d1c36edb00c52edeea0f31"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga03e45d6e60d1c36edb00c52edeea0f31">max</a> (vec&lt; L, T, Q &gt; const &amp;x, T y)</td></tr>
<tr class="memdesc:ga03e45d6e60d1c36edb00c52edeea0f31"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00241.html#ga03e45d6e60d1c36edb00c52edeea0f31">More...</a><br /></td></tr>
<tr class="separator:ga03e45d6e60d1c36edb00c52edeea0f31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1fec0c3303b572a6d4697a637213870"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac1fec0c3303b572a6d4697a637213870"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gac1fec0c3303b572a6d4697a637213870">max</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gac1fec0c3303b572a6d4697a637213870"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00241.html#gac1fec0c3303b572a6d4697a637213870">More...</a><br /></td></tr>
<tr class="separator:gac1fec0c3303b572a6d4697a637213870"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6cf8098827054a270ee36b18e30d471d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga6cf8098827054a270ee36b18e30d471d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga6cf8098827054a270ee36b18e30d471d">min</a> (genType x, genType y)</td></tr>
<tr class="memdesc:ga6cf8098827054a270ee36b18e30d471d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00241.html#ga6cf8098827054a270ee36b18e30d471d">More...</a><br /></td></tr>
<tr class="separator:ga6cf8098827054a270ee36b18e30d471d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7d015eba1f9f48519251f4abe69b14d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa7d015eba1f9f48519251f4abe69b14d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gaa7d015eba1f9f48519251f4abe69b14d">min</a> (vec&lt; L, T, Q &gt; const &amp;x, T y)</td></tr>
<tr class="memdesc:gaa7d015eba1f9f48519251f4abe69b14d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00241.html#gaa7d015eba1f9f48519251f4abe69b14d">More...</a><br /></td></tr>
<tr class="separator:gaa7d015eba1f9f48519251f4abe69b14d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31f49ef9e7d1beb003160c5e009b0c48"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga31f49ef9e7d1beb003160c5e009b0c48"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48">min</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga31f49ef9e7d1beb003160c5e009b0c48"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48">More...</a><br /></td></tr>
<tr class="separator:ga31f49ef9e7d1beb003160c5e009b0c48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e93f374aae27d1a88b921860351f8d4"><td class="memTemplParams" colspan="2">template&lt;typename genTypeT , typename genTypeU &gt; </td></tr>
<tr class="memitem:ga8e93f374aae27d1a88b921860351f8d4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genTypeT&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a> (genTypeT x, genTypeT y, genTypeU a)</td></tr>
<tr class="memdesc:ga8e93f374aae27d1a88b921860351f8d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">If genTypeU is a floating scalar or vector: Returns x * (1.0 - a) + y * a, i.e., the linear blend of x and y using the floating-point value a.  <a href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">More...</a><br /></td></tr>
<tr class="separator:ga8e93f374aae27d1a88b921860351f8d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b197a452cd52db3c5c18bac72bd7798"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga9b197a452cd52db3c5c18bac72bd7798"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga9b197a452cd52db3c5c18bac72bd7798">mod</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga9b197a452cd52db3c5c18bac72bd7798"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modulus.  <a href="a00241.html#ga9b197a452cd52db3c5c18bac72bd7798">More...</a><br /></td></tr>
<tr class="separator:ga9b197a452cd52db3c5c18bac72bd7798"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85e33f139b8db1b39b590a5713b9e679"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga85e33f139b8db1b39b590a5713b9e679"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga85e33f139b8db1b39b590a5713b9e679">modf</a> (genType x, genType &amp;i)</td></tr>
<tr class="memdesc:ga85e33f139b8db1b39b590a5713b9e679"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the fractional part of x and sets i to the integer part (as a whole number floating point value).  <a href="a00241.html#ga85e33f139b8db1b39b590a5713b9e679">More...</a><br /></td></tr>
<tr class="separator:ga85e33f139b8db1b39b590a5713b9e679"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa03aca8c4713e1cc892aa92ca135a7e"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafa03aca8c4713e1cc892aa92ca135a7e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gafa03aca8c4713e1cc892aa92ca135a7e">round</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gafa03aca8c4713e1cc892aa92ca135a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x.  <a href="a00241.html#gafa03aca8c4713e1cc892aa92ca135a7e">More...</a><br /></td></tr>
<tr class="separator:gafa03aca8c4713e1cc892aa92ca135a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76b81785045a057989a84d99aeeb1578"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga76b81785045a057989a84d99aeeb1578"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga76b81785045a057989a84d99aeeb1578">roundEven</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga76b81785045a057989a84d99aeeb1578"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x.  <a href="a00241.html#ga76b81785045a057989a84d99aeeb1578">More...</a><br /></td></tr>
<tr class="separator:ga76b81785045a057989a84d99aeeb1578"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1e2e5cfff800056540e32f6c9b604b28"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1e2e5cfff800056540e32f6c9b604b28"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga1e2e5cfff800056540e32f6c9b604b28">sign</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga1e2e5cfff800056540e32f6c9b604b28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 1.0 if x &gt; 0, 0.0 if x == 0, or -1.0 if x &lt; 0.  <a href="a00241.html#ga1e2e5cfff800056540e32f6c9b604b28">More...</a><br /></td></tr>
<tr class="separator:ga1e2e5cfff800056540e32f6c9b604b28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga562edf7eca082cc5b7a0aaf180436daf"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga562edf7eca082cc5b7a0aaf180436daf"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga562edf7eca082cc5b7a0aaf180436daf">smoothstep</a> (genType edge0, genType edge1, genType x)</td></tr>
<tr class="memdesc:ga562edf7eca082cc5b7a0aaf180436daf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 0.0 if x &lt;= edge0 and 1.0 if x &gt;= edge1 and performs smooth Hermite interpolation between 0 and 1 when edge0 &lt; x &lt; edge1.  <a href="a00241.html#ga562edf7eca082cc5b7a0aaf180436daf">More...</a><br /></td></tr>
<tr class="separator:ga562edf7eca082cc5b7a0aaf180436daf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga015a1261ff23e12650211aa872863cce"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga015a1261ff23e12650211aa872863cce"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga015a1261ff23e12650211aa872863cce">step</a> (genType edge, genType x)</td></tr>
<tr class="memdesc:ga015a1261ff23e12650211aa872863cce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 0.0 if x &lt; edge, otherwise it returns 1.0 for each component of a genType.  <a href="a00241.html#ga015a1261ff23e12650211aa872863cce">More...</a><br /></td></tr>
<tr class="separator:ga015a1261ff23e12650211aa872863cce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f9a911a48ef244b51654eaefc81c551"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8f9a911a48ef244b51654eaefc81c551"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga8f9a911a48ef244b51654eaefc81c551">step</a> (T edge, vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga8f9a911a48ef244b51654eaefc81c551"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 0.0 if x &lt; edge, otherwise it returns 1.0.  <a href="a00241.html#ga8f9a911a48ef244b51654eaefc81c551">More...</a><br /></td></tr>
<tr class="separator:ga8f9a911a48ef244b51654eaefc81c551"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4a5fc81619c7d3e8b22f53d4a098c7f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf4a5fc81619c7d3e8b22f53d4a098c7f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f">step</a> (vec&lt; L, T, Q &gt; const &amp;edge, vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gaf4a5fc81619c7d3e8b22f53d4a098c7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 0.0 if x &lt; edge, otherwise it returns 1.0.  <a href="a00241.html#gaf4a5fc81619c7d3e8b22f53d4a098c7f">More...</a><br /></td></tr>
<tr class="separator:gaf4a5fc81619c7d3e8b22f53d4a098c7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf9375e3e06173271d49e6ffa3a334259"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf9375e3e06173271d49e6ffa3a334259"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#gaf9375e3e06173271d49e6ffa3a334259">trunc</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gaf9375e3e06173271d49e6ffa3a334259"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x whose absolute value is not larger than the absolute value of x.  <a href="a00241.html#gaf9375e3e06173271d49e6ffa3a334259">More...</a><br /></td></tr>
<tr class="separator:gaf9375e3e06173271d49e6ffa3a334259"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2bae0d15dcdca6093f88f76b3975d97"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00241.html#gab2bae0d15dcdca6093f88f76b3975d97">uintBitsToFloat</a> (uint const &amp;v)</td></tr>
<tr class="memdesc:gab2bae0d15dcdca6093f88f76b3975d97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point value corresponding to a unsigned integer encoding of a floating-point value.  <a href="a00241.html#gab2bae0d15dcdca6093f88f76b3975d97">More...</a><br /></td></tr>
<tr class="separator:gab2bae0d15dcdca6093f88f76b3975d97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga97f46b5f7b42fe44482e13356eb394ae"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga97f46b5f7b42fe44482e13356eb394ae"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, float, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00241.html#ga97f46b5f7b42fe44482e13356eb394ae">uintBitsToFloat</a> (vec&lt; L, uint, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga97f46b5f7b42fe44482e13356eb394ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point value corresponding to a unsigned integer encoding of a floating-point value.  <a href="a00241.html#ga97f46b5f7b42fe44482e13356eb394ae">More...</a><br /></td></tr>
<tr class="separator:ga97f46b5f7b42fe44482e13356eb394ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00280.html">Core features</a> </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.3 Common Functions</a> </dd></dl>

<p>Definition in file <a class="el" href="a00015_source.html">common.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
