<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: constants.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">constants.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00021.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_constants.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_constants extension included&quot;)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;{</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga788f5a421fc0f40a1296ebc094cbaa8a">zero</a>();</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga39c2fb227631ca25894326529bdd1ee5">one</a>();</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#gaa5276a4617566abcfe49286f40e3a256">two_pi</a>();</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga261380796b2cd496f68d2cf1d08b8eb9">root_pi</a>();</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga0c36b41d462e45641faf7d7938948bac">half_pi</a>();</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#gae94950df74b0ce382b1fc1d978ef7394">three_over_two_pi</a>();</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga3c9df42bd73c519a995c43f0f99e77e0">quarter_pi</a>();</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga555150da2b06d23c8738981d5013e0eb">one_over_pi</a>();</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga7c922b427986cbb2e4c6ac69874eefbc">one_over_two_pi</a>();</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga74eadc8a211253079683219a3ea0462a">two_over_pi</a>();</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga753950e5140e4ea6a88e4a18ba61dc09">four_over_pi</a>();</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga5827301817640843cf02026a8d493894">two_over_root_pi</a>();</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga788fa23a0939bac4d1d0205fb4f35818">one_over_root_two</a>();</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga4e276cb823cc5e612d4f89ed99c75039">root_half_pi</a>();</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga2bcedc575039fe0cd765742f8bbb0bd3">root_two_pi</a>();</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga4129412e96b33707a77c1a07652e23e2">root_ln_four</a>();</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a>();</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#gad8fe2e6f90bce9d829e9723b649fbd42">euler</a>();</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga74e607d29020f100c0d0dc46ce2ca950">root_two</a>();</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga4f286be4abe88be1eed7d2a9f6cb193e">root_three</a>();</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#gae9ebbded75b53d4faeb1e4ef8b3347a2">root_five</a>();</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga24f4d27765678116f41a2f336ab7975c">ln_two</a>();</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#gaf97ebc6c059ffd788e6c4946f71ef66c">ln_ten</a>();</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#gaca94292c839ed31a405ab7a81ae7e850">ln_ln_two</a>();</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga3077c6311010a214b69ddc8214ec13b5">third</a>();</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga9b4d2f4322edcf63a6737b92a29dd1f5">two_thirds</a>();</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR genType <a class="code" href="a00290.html#ga748cf8642830657c5b7eae04d0a80899">golden_ratio</a>();</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="preprocessor">#include &quot;constants.inl&quot;</span></div>
<div class="ttc" id="a00290_html_ga3077c6311010a214b69ddc8214ec13b5"><div class="ttname"><a href="a00290.html#ga3077c6311010a214b69ddc8214ec13b5">glm::third</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType third()</div><div class="ttdoc">Return 1 / 3. </div></div>
<div class="ttc" id="a00290_html_ga74e607d29020f100c0d0dc46ce2ca950"><div class="ttname"><a href="a00290.html#ga74e607d29020f100c0d0dc46ce2ca950">glm::root_two</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_two()</div><div class="ttdoc">Return sqrt(2). </div></div>
<div class="ttc" id="a00290_html_ga788fa23a0939bac4d1d0205fb4f35818"><div class="ttname"><a href="a00290.html#ga788fa23a0939bac4d1d0205fb4f35818">glm::one_over_root_two</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType one_over_root_two()</div><div class="ttdoc">Return 1 / sqrt(2). </div></div>
<div class="ttc" id="a00290_html_gad8fe2e6f90bce9d829e9723b649fbd42"><div class="ttname"><a href="a00290.html#gad8fe2e6f90bce9d829e9723b649fbd42">glm::euler</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType euler()</div><div class="ttdoc">Return Euler&#39;s constant. </div></div>
<div class="ttc" id="a00290_html_ga9b4d2f4322edcf63a6737b92a29dd1f5"><div class="ttname"><a href="a00290.html#ga9b4d2f4322edcf63a6737b92a29dd1f5">glm::two_thirds</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType two_thirds()</div><div class="ttdoc">Return 2 / 3. </div></div>
<div class="ttc" id="a00290_html_gaa5276a4617566abcfe49286f40e3a256"><div class="ttname"><a href="a00290.html#gaa5276a4617566abcfe49286f40e3a256">glm::two_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType two_pi()</div><div class="ttdoc">Return pi * 2. </div></div>
<div class="ttc" id="a00290_html_ga748cf8642830657c5b7eae04d0a80899"><div class="ttname"><a href="a00290.html#ga748cf8642830657c5b7eae04d0a80899">glm::golden_ratio</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType golden_ratio()</div><div class="ttdoc">Return the golden ratio constant. </div></div>
<div class="ttc" id="a00290_html_ga3c9df42bd73c519a995c43f0f99e77e0"><div class="ttname"><a href="a00290.html#ga3c9df42bd73c519a995c43f0f99e77e0">glm::quarter_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType quarter_pi()</div><div class="ttdoc">Return pi / 4. </div></div>
<div class="ttc" id="a00290_html_ga39c2fb227631ca25894326529bdd1ee5"><div class="ttname"><a href="a00290.html#ga39c2fb227631ca25894326529bdd1ee5">glm::one</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType one()</div><div class="ttdoc">Return 1. </div></div>
<div class="ttc" id="a00290_html_gae9ebbded75b53d4faeb1e4ef8b3347a2"><div class="ttname"><a href="a00290.html#gae9ebbded75b53d4faeb1e4ef8b3347a2">glm::root_five</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_five()</div><div class="ttdoc">Return sqrt(5). </div></div>
<div class="ttc" id="a00290_html_gae94950df74b0ce382b1fc1d978ef7394"><div class="ttname"><a href="a00290.html#gae94950df74b0ce382b1fc1d978ef7394">glm::three_over_two_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType three_over_two_pi()</div><div class="ttdoc">Return pi / 2 * 3. </div></div>
<div class="ttc" id="a00290_html_ga788f5a421fc0f40a1296ebc094cbaa8a"><div class="ttname"><a href="a00290.html#ga788f5a421fc0f40a1296ebc094cbaa8a">glm::zero</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType zero()</div><div class="ttdoc">Return 0. </div></div>
<div class="ttc" id="a00290_html_gaf97ebc6c059ffd788e6c4946f71ef66c"><div class="ttname"><a href="a00290.html#gaf97ebc6c059ffd788e6c4946f71ef66c">glm::ln_ten</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType ln_ten()</div><div class="ttdoc">Return ln(10). </div></div>
<div class="ttc" id="a00290_html_ga4f286be4abe88be1eed7d2a9f6cb193e"><div class="ttname"><a href="a00290.html#ga4f286be4abe88be1eed7d2a9f6cb193e">glm::root_three</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_three()</div><div class="ttdoc">Return sqrt(3). </div></div>
<div class="ttc" id="a00290_html_ga261380796b2cd496f68d2cf1d08b8eb9"><div class="ttname"><a href="a00290.html#ga261380796b2cd496f68d2cf1d08b8eb9">glm::root_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_pi()</div><div class="ttdoc">Return square root of pi. </div></div>
<div class="ttc" id="a00290_html_ga4b7956eb6e2fbedfc7cf2e46e85c5139"><div class="ttname"><a href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">glm::e</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType e()</div><div class="ttdoc">Return e constant. </div></div>
<div class="ttc" id="a00290_html_ga555150da2b06d23c8738981d5013e0eb"><div class="ttname"><a href="a00290.html#ga555150da2b06d23c8738981d5013e0eb">glm::one_over_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType one_over_pi()</div><div class="ttdoc">Return 1 / pi. </div></div>
<div class="ttc" id="a00290_html_ga74eadc8a211253079683219a3ea0462a"><div class="ttname"><a href="a00290.html#ga74eadc8a211253079683219a3ea0462a">glm::two_over_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType two_over_pi()</div><div class="ttdoc">Return 2 / pi. </div></div>
<div class="ttc" id="a00290_html_ga753950e5140e4ea6a88e4a18ba61dc09"><div class="ttname"><a href="a00290.html#ga753950e5140e4ea6a88e4a18ba61dc09">glm::four_over_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType four_over_pi()</div><div class="ttdoc">Return 4 / pi. </div></div>
<div class="ttc" id="a00290_html_ga2bcedc575039fe0cd765742f8bbb0bd3"><div class="ttname"><a href="a00290.html#ga2bcedc575039fe0cd765742f8bbb0bd3">glm::root_two_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_two_pi()</div><div class="ttdoc">Return sqrt(2 * pi). </div></div>
<div class="ttc" id="a00290_html_ga24f4d27765678116f41a2f336ab7975c"><div class="ttname"><a href="a00290.html#ga24f4d27765678116f41a2f336ab7975c">glm::ln_two</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType ln_two()</div><div class="ttdoc">Return ln(2). </div></div>
<div class="ttc" id="a00290_html_ga4129412e96b33707a77c1a07652e23e2"><div class="ttname"><a href="a00290.html#ga4129412e96b33707a77c1a07652e23e2">glm::root_ln_four</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_ln_four()</div><div class="ttdoc">Return sqrt(ln(4)). </div></div>
<div class="ttc" id="a00290_html_ga5827301817640843cf02026a8d493894"><div class="ttname"><a href="a00290.html#ga5827301817640843cf02026a8d493894">glm::two_over_root_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType two_over_root_pi()</div><div class="ttdoc">Return 2 / sqrt(pi). </div></div>
<div class="ttc" id="a00290_html_gaca94292c839ed31a405ab7a81ae7e850"><div class="ttname"><a href="a00290.html#gaca94292c839ed31a405ab7a81ae7e850">glm::ln_ln_two</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType ln_ln_two()</div><div class="ttdoc">Return ln(ln(2)). </div></div>
<div class="ttc" id="a00290_html_ga4e276cb823cc5e612d4f89ed99c75039"><div class="ttname"><a href="a00290.html#ga4e276cb823cc5e612d4f89ed99c75039">glm::root_half_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType root_half_pi()</div><div class="ttdoc">Return sqrt(pi / 2). </div></div>
<div class="ttc" id="a00290_html_ga0c36b41d462e45641faf7d7938948bac"><div class="ttname"><a href="a00290.html#ga0c36b41d462e45641faf7d7938948bac">glm::half_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType half_pi()</div><div class="ttdoc">Return pi / 2. </div></div>
<div class="ttc" id="a00290_html_ga7c922b427986cbb2e4c6ac69874eefbc"><div class="ttname"><a href="a00290.html#ga7c922b427986cbb2e4c6ac69874eefbc">glm::one_over_two_pi</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType one_over_two_pi()</div><div class="ttdoc">Return 1 / (pi * 2). </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
