<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: compatibility.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">compatibility.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00017.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../gtc/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_compatibility is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_compatibility extension included&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#       include &lt;cfloat&gt;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#elif GLM_COMPILER &amp; GLM_COMPILER_GCC</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#       include &lt;cmath&gt;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#       if(GLM_PLATFORM &amp; GLM_PLATFORM_ANDROID)</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#               undef isfinite</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#endif//GLM_COMPILER</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;{</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="a00315.html#ga5494ba3a95ea6594c86fc75236886864">   41</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt; GLM_FUNC_QUALIFIER T <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(T x, T y, T a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}                                                                                                                                                                     </div>
<div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="a00315.html#gaa551c0a0e16d2d4608e49f7696df897f">   42</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(<span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; y, T a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}                                                 </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="a00315.html#ga44a8b5fd776320f1713413dec959b32a">   44</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(<span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; y, T a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}                                                 </div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="a00315.html#ga89ac8e000199292ec7875519d27e214b">   45</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(<span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; y, T a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}                                                 </div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="a00315.html#gaf68de5baf72d16135368b8ef4f841604">   46</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(<span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; y, <span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}       </div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="a00315.html#ga4ae1a616c8540a2649eab8e0cd051bb3">   47</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(<span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; y, <span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}       </div>
<div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">   48</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; <a class="code" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a>(<span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; y, <span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; a){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">mix</a>(x, y, a);}       </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="a00315.html#ga0fd09e616d122bc2ed9726682ffd44b7">   50</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER T <a class="code" href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">saturate</a>(T x){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga7cd77683da6361e297c56443fc70806d">clamp</a>(x, T(0), T(1));}                                                                                                              </div>
<div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="a00315.html#gaee97b8001c794a78a44f5d59f62a8aba">   51</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; <a class="code" href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">saturate</a>(<span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga7cd77683da6361e297c56443fc70806d">clamp</a>(x, T(0), T(1));}                                 </div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="a00315.html#ga39bfe3a421286ee31680d45c31ccc161">   52</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; <a class="code" href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">saturate</a>(<span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga7cd77683da6361e297c56443fc70806d">clamp</a>(x, T(0), T(1));}                                 </div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">   53</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; <a class="code" href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">saturate</a>(<span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; x){<span class="keywordflow">return</span> <a class="code" href="a00241.html#ga7cd77683da6361e297c56443fc70806d">clamp</a>(x, T(0), T(1));}                                 </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="a00315.html#gac63011205bf6d0be82589dc56dd26708">   55</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER T <a class="code" href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">atan2</a>(T x, T y){<span class="keywordflow">return</span> <a class="code" href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">atan</a>(x, y);}                                                                                                                              </div>
<div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="a00315.html#ga83bc41bd6f89113ee8006576b12bfc50">   56</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;2, T, Q&gt; <a class="code" href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">atan2</a>(<span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; y){<span class="keywordflow">return</span> <a class="code" href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">atan</a>(x, y);}       </div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="a00315.html#gac39314f5087e7e51e592897cabbc1927">   57</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;3, T, Q&gt; <a class="code" href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">atan2</a>(<span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; y){<span class="keywordflow">return</span> <a class="code" href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">atan</a>(x, y);}       </div>
<div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">   58</a></span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_QUALIFIER vec&lt;4, T, Q&gt; <a class="code" href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">atan2</a>(<span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; x, <span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; y){<span class="keywordflow">return</span> <a class="code" href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">atan</a>(x, y);}       </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt; GLM_FUNC_DECL <span class="keywordtype">bool</span> <a class="code" href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">isfinite</a>(genType <span class="keyword">const</span>&amp; x);                                                                                       </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_DECL vec&lt;1, bool, Q&gt; <a class="code" href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">isfinite</a>(<span class="keyword">const</span> vec&lt;1, T, Q&gt;&amp; x);                                </div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_DECL vec&lt;2, bool, Q&gt; <a class="code" href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">isfinite</a>(<span class="keyword">const</span> vec&lt;2, T, Q&gt;&amp; x);                                </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_DECL vec&lt;3, bool, Q&gt; <a class="code" href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">isfinite</a>(<span class="keyword">const</span> vec&lt;3, T, Q&gt;&amp; x);                                </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt; GLM_FUNC_DECL vec&lt;4, bool, Q&gt; <a class="code" href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">isfinite</a>(<span class="keyword">const</span> vec&lt;4, T, Q&gt;&amp; x);                                </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="a00315.html#gaddcd7aa2e30e61af5b38660613d3979e">   66</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">bool</span>                                            <a class="code" href="a00315.html#gaddcd7aa2e30e61af5b38660613d3979e">bool1</a>;                  </div>
<div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="a00315.html#gaa09ab65ec9c3c54305ff502e2b1fe6d9">   67</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, highp&gt;                     <a class="code" href="a00315.html#gaa09ab65ec9c3c54305ff502e2b1fe6d9">bool2</a>;                  </div>
<div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="a00315.html#ga99629f818737f342204071ef8296b2ed">   68</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, highp&gt;                     <a class="code" href="a00315.html#ga99629f818737f342204071ef8296b2ed">bool3</a>;                  </div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="a00315.html#ga13c3200b82708f73faac6d7f09ec91a3">   69</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, highp&gt;                     <a class="code" href="a00315.html#ga13c3200b82708f73faac6d7f09ec91a3">bool4</a>;                  </div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div>
<div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="a00315.html#ga7f895c936f0c29c8729afbbf22806090">   71</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">bool</span>                                            <a class="code" href="a00315.html#ga7f895c936f0c29c8729afbbf22806090">bool1x1</a>;                </div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="a00315.html#gadb3703955e513632f98ba12fe051ba3e">   72</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, bool, highp&gt;          <a class="code" href="a00315.html#gadb3703955e513632f98ba12fe051ba3e">bool2x2</a>;                </div>
<div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="a00315.html#ga9ae6ee155d0f90cb1ae5b6c4546738a0">   73</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, bool, highp&gt;          <a class="code" href="a00315.html#ga9ae6ee155d0f90cb1ae5b6c4546738a0">bool2x3</a>;                </div>
<div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="a00315.html#ga4d7fa65be8e8e4ad6d920b45c44e471f">   74</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, bool, highp&gt;          <a class="code" href="a00315.html#ga4d7fa65be8e8e4ad6d920b45c44e471f">bool2x4</a>;                </div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="a00315.html#gac7d7311f7e0fa8b6163d96dab033a755">   75</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, bool, highp&gt;          <a class="code" href="a00315.html#gac7d7311f7e0fa8b6163d96dab033a755">bool3x2</a>;                </div>
<div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="a00315.html#ga6c97b99aac3e302053ffb58aace9033c">   76</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, bool, highp&gt;          <a class="code" href="a00315.html#ga6c97b99aac3e302053ffb58aace9033c">bool3x3</a>;                </div>
<div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="a00315.html#gae7d6b679463d37d6c527d478fb470fdf">   77</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, bool, highp&gt;          <a class="code" href="a00315.html#gae7d6b679463d37d6c527d478fb470fdf">bool3x4</a>;                </div>
<div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="a00315.html#ga9ed830f52408b2f83c085063a3eaf1d0">   78</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, bool, highp&gt;          <a class="code" href="a00315.html#ga9ed830f52408b2f83c085063a3eaf1d0">bool4x2</a>;                </div>
<div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="a00315.html#gad0f5dc7f22c2065b1b06d57f1c0658fe">   79</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, bool, highp&gt;          <a class="code" href="a00315.html#gad0f5dc7f22c2065b1b06d57f1c0658fe">bool4x3</a>;                </div>
<div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="a00315.html#ga7d2a7d13986602ae2896bfaa394235d4">   80</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, bool, highp&gt;          <a class="code" href="a00315.html#ga7d2a7d13986602ae2896bfaa394235d4">bool4x4</a>;                </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="a00315.html#ga0670a2111b5e4a6410bd027fa0232fc3">   82</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">int</span>                                                     <a class="code" href="a00315.html#ga0670a2111b5e4a6410bd027fa0232fc3">int1</a>;                   </div>
<div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="a00315.html#gafe3a8fd56354caafe24bfe1b1e3ad22a">   83</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, highp&gt;                      <a class="code" href="a00315.html#gafe3a8fd56354caafe24bfe1b1e3ad22a">int2</a>;                   </div>
<div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="a00315.html#ga909c38a425f215a50c847145d7da09f0">   84</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, highp&gt;                      <a class="code" href="a00315.html#ga909c38a425f215a50c847145d7da09f0">int3</a>;                   </div>
<div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="a00315.html#gaecdef18c819c205aeee9f94dc93de56a">   85</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, highp&gt;                      <a class="code" href="a00315.html#gaecdef18c819c205aeee9f94dc93de56a">int4</a>;                   </div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="a00315.html#ga056ffe02d3a45af626f8e62221881c7a">   87</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">int</span>                                                     <a class="code" href="a00315.html#ga056ffe02d3a45af626f8e62221881c7a">int1x1</a>;                 </div>
<div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="a00315.html#ga4e5ce477c15836b21e3c42daac68554d">   88</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, int, highp&gt;           <a class="code" href="a00315.html#ga4e5ce477c15836b21e3c42daac68554d">int2x2</a>;                 </div>
<div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="a00315.html#ga197ded5ad8354f6b6fb91189d7a269b3">   89</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, int, highp&gt;           <a class="code" href="a00315.html#ga197ded5ad8354f6b6fb91189d7a269b3">int2x3</a>;                 </div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="a00315.html#ga2749d59a7fddbac44f34ba78e57ef807">   90</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, int, highp&gt;           <a class="code" href="a00315.html#ga2749d59a7fddbac44f34ba78e57ef807">int2x4</a>;                 </div>
<div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="a00315.html#gaa4cbe16a92cf3664376c7a2fc5126aa8">   91</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, int, highp&gt;           <a class="code" href="a00315.html#gaa4cbe16a92cf3664376c7a2fc5126aa8">int3x2</a>;                 </div>
<div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="a00315.html#ga15c9649286f0bf431bdf9b3509580048">   92</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, int, highp&gt;           <a class="code" href="a00315.html#ga15c9649286f0bf431bdf9b3509580048">int3x3</a>;                 </div>
<div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="a00315.html#gaacac46ddc7d15d0f9529d05c92946a0f">   93</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, int, highp&gt;           <a class="code" href="a00315.html#gaacac46ddc7d15d0f9529d05c92946a0f">int3x4</a>;                 </div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="a00315.html#ga97a39dd9bc7d572810d80b8467cbffa1">   94</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, int, highp&gt;           <a class="code" href="a00315.html#ga97a39dd9bc7d572810d80b8467cbffa1">int4x2</a>;                 </div>
<div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="a00315.html#gae4a2c53f14aeec9a17c2b81142b7e82d">   95</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, int, highp&gt;           <a class="code" href="a00315.html#gae4a2c53f14aeec9a17c2b81142b7e82d">int4x3</a>;                 </div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="a00315.html#ga04dee1552424198b8f58b377c2ee00d8">   96</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, int, highp&gt;           <a class="code" href="a00315.html#ga04dee1552424198b8f58b377c2ee00d8">int4x4</a>;                 </div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="a00315.html#gaf5208d01f6c6fbcb7bb55d610b9c0ead">   98</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">float</span>                                           <a class="code" href="a00315.html#gaf5208d01f6c6fbcb7bb55d610b9c0ead">float1</a>;                 </div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="a00315.html#ga02d3c013982c183906c61d74aa3166ce">   99</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, highp&gt;            <a class="code" href="a00315.html#ga02d3c013982c183906c61d74aa3166ce">float2</a>;                 </div>
<div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="a00315.html#ga821ff110fc8533a053cbfcc93e078cc0">  100</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, highp&gt;            <a class="code" href="a00315.html#ga821ff110fc8533a053cbfcc93e078cc0">float3</a>;                 </div>
<div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="a00315.html#gae2da7345087db3815a25d8837a727ef1">  101</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, highp&gt;            <a class="code" href="a00315.html#gae2da7345087db3815a25d8837a727ef1">float4</a>;                 </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="a00315.html#ga73720b8dc4620835b17f74d428f98c0c">  103</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">float</span>                                           <a class="code" href="a00315.html#ga73720b8dc4620835b17f74d428f98c0c">float1x1</a>;               </div>
<div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="a00315.html#ga33d43ecbb60a85a1366ff83f8a0ec85f">  104</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, highp&gt;         <a class="code" href="a00315.html#ga33d43ecbb60a85a1366ff83f8a0ec85f">float2x2</a>;               </div>
<div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="a00315.html#ga939b0cff15cee3030f75c1b2e36f89fe">  105</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, highp&gt;         <a class="code" href="a00315.html#ga939b0cff15cee3030f75c1b2e36f89fe">float2x3</a>;               </div>
<div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="a00315.html#gafec3cfd901ab334a92e0242b8f2269b4">  106</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, highp&gt;         <a class="code" href="a00315.html#gafec3cfd901ab334a92e0242b8f2269b4">float2x4</a>;               </div>
<div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="a00315.html#gaa6c69f04ba95f3faedf95dae874de576">  107</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, highp&gt;         <a class="code" href="a00315.html#gaa6c69f04ba95f3faedf95dae874de576">float3x2</a>;               </div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="a00315.html#ga6ceb5d38a58becdf420026e12a6562f3">  108</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, highp&gt;         <a class="code" href="a00315.html#ga6ceb5d38a58becdf420026e12a6562f3">float3x3</a>;               </div>
<div class="line"><a name="l00109"></a><span class="lineno"><a class="line" href="a00315.html#ga4d2679c321b793ca3784fe0315bb5332">  109</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, highp&gt;         <a class="code" href="a00315.html#ga4d2679c321b793ca3784fe0315bb5332">float3x4</a>;               </div>
<div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="a00315.html#ga308b9af0c221145bcfe9bfc129d9098e">  110</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, highp&gt;         <a class="code" href="a00315.html#ga308b9af0c221145bcfe9bfc129d9098e">float4x2</a>;               </div>
<div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="a00315.html#gac0a51b4812038aa81d73ffcc37f741ac">  111</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, highp&gt;         <a class="code" href="a00315.html#gac0a51b4812038aa81d73ffcc37f741ac">float4x3</a>;               </div>
<div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="a00315.html#gad3051649b3715d828a4ab92cdae7c3bf">  112</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, highp&gt;         <a class="code" href="a00315.html#gad3051649b3715d828a4ab92cdae7c3bf">float4x4</a>;               </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="a00315.html#ga20b861a9b6e2a300323671c57a02525b">  114</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">double</span>                                          <a class="code" href="a00315.html#ga20b861a9b6e2a300323671c57a02525b">double1</a>;                </div>
<div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="a00315.html#ga31b729b04facccda73f07ed26958b3c2">  115</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, highp&gt;           <a class="code" href="a00315.html#ga31b729b04facccda73f07ed26958b3c2">double2</a>;                </div>
<div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="a00315.html#ga3d8b9028a1053a44a98902cd1c389472">  116</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, highp&gt;           <a class="code" href="a00315.html#ga3d8b9028a1053a44a98902cd1c389472">double3</a>;                </div>
<div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="a00315.html#gaf92f58af24f35617518aeb3d4f63fda6">  117</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, highp&gt;           <a class="code" href="a00315.html#gaf92f58af24f35617518aeb3d4f63fda6">double4</a>;                </div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="a00315.html#ga45f16a4dd0db1f199afaed9fd12fe9a8">  119</a></span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">double</span>                                          <a class="code" href="a00315.html#ga45f16a4dd0db1f199afaed9fd12fe9a8">double1x1</a>;              </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="a00315.html#gae57d0201096834d25f2b91b319e7cdbd">  120</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, highp&gt;                <a class="code" href="a00315.html#gae57d0201096834d25f2b91b319e7cdbd">double2x2</a>;              </div>
<div class="line"><a name="l00121"></a><span class="lineno"><a class="line" href="a00315.html#ga3655bc324008553ca61f39952d0b2d08">  121</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, highp&gt;                <a class="code" href="a00315.html#ga3655bc324008553ca61f39952d0b2d08">double2x3</a>;              </div>
<div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="a00315.html#gacd33061fc64a7b2dcfd7322c49d9557a">  122</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, highp&gt;                <a class="code" href="a00315.html#gacd33061fc64a7b2dcfd7322c49d9557a">double2x4</a>;              </div>
<div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="a00315.html#ga5ec08fc39c9d783dfcc488be240fe975">  123</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, highp&gt;                <a class="code" href="a00315.html#ga5ec08fc39c9d783dfcc488be240fe975">double3x2</a>;              </div>
<div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="a00315.html#ga4bad5bb20c6ddaecfe4006c93841d180">  124</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, highp&gt;                <a class="code" href="a00315.html#ga4bad5bb20c6ddaecfe4006c93841d180">double3x3</a>;              </div>
<div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="a00315.html#ga2ef022e453d663d70aec414b2a80f756">  125</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, highp&gt;                <a class="code" href="a00315.html#ga2ef022e453d663d70aec414b2a80f756">double3x4</a>;              </div>
<div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="a00315.html#gabca29ccceea53669618b751aae0ba83d">  126</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, highp&gt;                <a class="code" href="a00315.html#gabca29ccceea53669618b751aae0ba83d">double4x2</a>;              </div>
<div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="a00315.html#gafad66a02ccd360c86d6ab9ff9cfbc19c">  127</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, highp&gt;                <a class="code" href="a00315.html#gafad66a02ccd360c86d6ab9ff9cfbc19c">double4x3</a>;              </div>
<div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="a00315.html#gaab541bed2e788e4537852a2492860806">  128</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, highp&gt;                <a class="code" href="a00315.html#gaab541bed2e788e4537852a2492860806">double4x4</a>;              </div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor">#include &quot;compatibility.inl&quot;</span></div>
<div class="ttc" id="a00315_html_gaab541bed2e788e4537852a2492860806"><div class="ttname"><a href="a00315.html#gaab541bed2e788e4537852a2492860806">glm::double4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, highp &gt; double4x4</div><div class="ttdoc">double-qualifier floating-point matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00128">compatibility.hpp:128</a></div></div>
<div class="ttc" id="a00315_html_gaacac46ddc7d15d0f9529d05c92946a0f"><div class="ttname"><a href="a00315.html#gaacac46ddc7d15d0f9529d05c92946a0f">glm::int3x4</a></div><div class="ttdeci">mat&lt; 3, 4, int, highp &gt; int3x4</div><div class="ttdoc">integer matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00093">compatibility.hpp:93</a></div></div>
<div class="ttc" id="a00373_html_gac61629f3a4aa14057e7a8cae002291db"><div class="ttname"><a href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">glm::atan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; atan(vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Arc tangent. </div></div>
<div class="ttc" id="a00315_html_gaddcd7aa2e30e61af5b38660613d3979e"><div class="ttname"><a href="a00315.html#gaddcd7aa2e30e61af5b38660613d3979e">glm::bool1</a></div><div class="ttdeci">bool bool1</div><div class="ttdoc">boolean type with 1 component. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00066">compatibility.hpp:66</a></div></div>
<div class="ttc" id="a00315_html_gac0a51b4812038aa81d73ffcc37f741ac"><div class="ttname"><a href="a00315.html#gac0a51b4812038aa81d73ffcc37f741ac">glm::float4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, highp &gt; float4x3</div><div class="ttdoc">single-qualifier floating-point matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00111">compatibility.hpp:111</a></div></div>
<div class="ttc" id="a00315_html_gad3051649b3715d828a4ab92cdae7c3bf"><div class="ttname"><a href="a00315.html#gad3051649b3715d828a4ab92cdae7c3bf">glm::float4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, highp &gt; float4x4</div><div class="ttdoc">single-qualifier floating-point matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00112">compatibility.hpp:112</a></div></div>
<div class="ttc" id="a00315_html_gacd33061fc64a7b2dcfd7322c49d9557a"><div class="ttname"><a href="a00315.html#gacd33061fc64a7b2dcfd7322c49d9557a">glm::double2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, highp &gt; double2x4</div><div class="ttdoc">double-qualifier floating-point matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00122">compatibility.hpp:122</a></div></div>
<div class="ttc" id="a00315_html_gae57d0201096834d25f2b91b319e7cdbd"><div class="ttname"><a href="a00315.html#gae57d0201096834d25f2b91b319e7cdbd">glm::double2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, highp &gt; double2x2</div><div class="ttdoc">double-qualifier floating-point matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00120">compatibility.hpp:120</a></div></div>
<div class="ttc" id="a00315_html_ga5ec08fc39c9d783dfcc488be240fe975"><div class="ttname"><a href="a00315.html#ga5ec08fc39c9d783dfcc488be240fe975">glm::double3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, highp &gt; double3x2</div><div class="ttdoc">double-qualifier floating-point matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00123">compatibility.hpp:123</a></div></div>
<div class="ttc" id="a00315_html_gaba86c28da7bf5bdac64fecf7d56e8ff3"><div class="ttname"><a href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">glm::atan2</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt; atan2(const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y)</div><div class="ttdoc">Arc tangent. Returns an angle whose tangent is y/x. The signs of x and y are used to determine what q...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00058">compatibility.hpp:58</a></div></div>
<div class="ttc" id="a00315_html_ga45f16a4dd0db1f199afaed9fd12fe9a8"><div class="ttname"><a href="a00315.html#ga45f16a4dd0db1f199afaed9fd12fe9a8">glm::double1x1</a></div><div class="ttdeci">double double1x1</div><div class="ttdoc">double-qualifier floating-point matrix with 1 component. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00119">compatibility.hpp:119</a></div></div>
<div class="ttc" id="a00315_html_gab5477ab69c40de4db5d58d3359529724"><div class="ttname"><a href="a00315.html#gab5477ab69c40de4db5d58d3359529724">glm::lerp</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt; lerp(const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y, const vec&lt; 4, T, Q &gt; &amp;a)</div><div class="ttdoc">Returns the component-wise result of x * (1.0 - a) + y * a, i.e., the linear blend of x and y using v...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00048">compatibility.hpp:48</a></div></div>
<div class="ttc" id="a00315_html_ga4bad5bb20c6ddaecfe4006c93841d180"><div class="ttname"><a href="a00315.html#ga4bad5bb20c6ddaecfe4006c93841d180">glm::double3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, highp &gt; double3x3</div><div class="ttdoc">double-qualifier floating-point matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00124">compatibility.hpp:124</a></div></div>
<div class="ttc" id="a00315_html_gae2da7345087db3815a25d8837a727ef1"><div class="ttname"><a href="a00315.html#gae2da7345087db3815a25d8837a727ef1">glm::float4</a></div><div class="ttdeci">vec&lt; 4, float, highp &gt; float4</div><div class="ttdoc">single-qualifier floating-point vector with 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00101">compatibility.hpp:101</a></div></div>
<div class="ttc" id="a00315_html_ga056ffe02d3a45af626f8e62221881c7a"><div class="ttname"><a href="a00315.html#ga056ffe02d3a45af626f8e62221881c7a">glm::int1x1</a></div><div class="ttdeci">int int1x1</div><div class="ttdoc">integer matrix with 1 component. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00087">compatibility.hpp:87</a></div></div>
<div class="ttc" id="a00315_html_ga02d3c013982c183906c61d74aa3166ce"><div class="ttname"><a href="a00315.html#ga02d3c013982c183906c61d74aa3166ce">glm::float2</a></div><div class="ttdeci">vec&lt; 2, float, highp &gt; float2</div><div class="ttdoc">single-qualifier floating-point vector with 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00099">compatibility.hpp:99</a></div></div>
<div class="ttc" id="a00315_html_ga19925badbe10ce61df1d0de00be0b5ad"><div class="ttname"><a href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">glm::isfinite</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt; isfinite(const vec&lt; 4, T, Q &gt; &amp;x)</div><div class="ttdoc">Test whether or not a scalar or each vector component is a finite value. (From GLM_GTX_compatibility)...</div></div>
<div class="ttc" id="a00315_html_ga9ae6ee155d0f90cb1ae5b6c4546738a0"><div class="ttname"><a href="a00315.html#ga9ae6ee155d0f90cb1ae5b6c4546738a0">glm::bool2x3</a></div><div class="ttdeci">mat&lt; 2, 3, bool, highp &gt; bool2x3</div><div class="ttdoc">boolean matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00073">compatibility.hpp:73</a></div></div>
<div class="ttc" id="a00315_html_ga197ded5ad8354f6b6fb91189d7a269b3"><div class="ttname"><a href="a00315.html#ga197ded5ad8354f6b6fb91189d7a269b3">glm::int2x3</a></div><div class="ttdeci">mat&lt; 2, 3, int, highp &gt; int2x3</div><div class="ttdoc">integer matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00089">compatibility.hpp:89</a></div></div>
<div class="ttc" id="a00315_html_ga0670a2111b5e4a6410bd027fa0232fc3"><div class="ttname"><a href="a00315.html#ga0670a2111b5e4a6410bd027fa0232fc3">glm::int1</a></div><div class="ttdeci">int int1</div><div class="ttdoc">integer vector with 1 component. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00082">compatibility.hpp:82</a></div></div>
<div class="ttc" id="a00315_html_ga821ff110fc8533a053cbfcc93e078cc0"><div class="ttname"><a href="a00315.html#ga821ff110fc8533a053cbfcc93e078cc0">glm::float3</a></div><div class="ttdeci">vec&lt; 3, float, highp &gt; float3</div><div class="ttdoc">single-qualifier floating-point vector with 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00100">compatibility.hpp:100</a></div></div>
<div class="ttc" id="a00315_html_gafec3cfd901ab334a92e0242b8f2269b4"><div class="ttname"><a href="a00315.html#gafec3cfd901ab334a92e0242b8f2269b4">glm::float2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, highp &gt; float2x4</div><div class="ttdoc">single-qualifier floating-point matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00106">compatibility.hpp:106</a></div></div>
<div class="ttc" id="a00315_html_gadb3703955e513632f98ba12fe051ba3e"><div class="ttname"><a href="a00315.html#gadb3703955e513632f98ba12fe051ba3e">glm::bool2x2</a></div><div class="ttdeci">mat&lt; 2, 2, bool, highp &gt; bool2x2</div><div class="ttdoc">boolean matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00072">compatibility.hpp:72</a></div></div>
<div class="ttc" id="a00315_html_ga7d2a7d13986602ae2896bfaa394235d4"><div class="ttname"><a href="a00315.html#ga7d2a7d13986602ae2896bfaa394235d4">glm::bool4x4</a></div><div class="ttdeci">mat&lt; 4, 4, bool, highp &gt; bool4x4</div><div class="ttdoc">boolean matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00080">compatibility.hpp:80</a></div></div>
<div class="ttc" id="a00315_html_gaf5208d01f6c6fbcb7bb55d610b9c0ead"><div class="ttname"><a href="a00315.html#gaf5208d01f6c6fbcb7bb55d610b9c0ead">glm::float1</a></div><div class="ttdeci">float float1</div><div class="ttdoc">single-qualifier floating-point vector with 1 component. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00098">compatibility.hpp:98</a></div></div>
<div class="ttc" id="a00315_html_ga73720b8dc4620835b17f74d428f98c0c"><div class="ttname"><a href="a00315.html#ga73720b8dc4620835b17f74d428f98c0c">glm::float1x1</a></div><div class="ttdeci">float float1x1</div><div class="ttdoc">single-qualifier floating-point matrix with 1 component. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00103">compatibility.hpp:103</a></div></div>
<div class="ttc" id="a00315_html_gabca29ccceea53669618b751aae0ba83d"><div class="ttname"><a href="a00315.html#gabca29ccceea53669618b751aae0ba83d">glm::double4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, highp &gt; double4x2</div><div class="ttdoc">double-qualifier floating-point matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00126">compatibility.hpp:126</a></div></div>
<div class="ttc" id="a00315_html_gae4a2c53f14aeec9a17c2b81142b7e82d"><div class="ttname"><a href="a00315.html#gae4a2c53f14aeec9a17c2b81142b7e82d">glm::int4x3</a></div><div class="ttdeci">mat&lt; 4, 3, int, highp &gt; int4x3</div><div class="ttdoc">integer matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00095">compatibility.hpp:95</a></div></div>
<div class="ttc" id="a00315_html_ga9ed830f52408b2f83c085063a3eaf1d0"><div class="ttname"><a href="a00315.html#ga9ed830f52408b2f83c085063a3eaf1d0">glm::bool4x2</a></div><div class="ttdeci">mat&lt; 4, 2, bool, highp &gt; bool4x2</div><div class="ttdoc">boolean matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00078">compatibility.hpp:78</a></div></div>
<div class="ttc" id="a00315_html_ga33d43ecbb60a85a1366ff83f8a0ec85f"><div class="ttname"><a href="a00315.html#ga33d43ecbb60a85a1366ff83f8a0ec85f">glm::float2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, highp &gt; float2x2</div><div class="ttdoc">single-qualifier floating-point matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00104">compatibility.hpp:104</a></div></div>
<div class="ttc" id="a00315_html_ga909c38a425f215a50c847145d7da09f0"><div class="ttname"><a href="a00315.html#ga909c38a425f215a50c847145d7da09f0">glm::int3</a></div><div class="ttdeci">vec&lt; 3, int, highp &gt; int3</div><div class="ttdoc">integer vector with 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00084">compatibility.hpp:84</a></div></div>
<div class="ttc" id="a00315_html_ga308b9af0c221145bcfe9bfc129d9098e"><div class="ttname"><a href="a00315.html#ga308b9af0c221145bcfe9bfc129d9098e">glm::float4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, highp &gt; float4x2</div><div class="ttdoc">single-qualifier floating-point matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00110">compatibility.hpp:110</a></div></div>
<div class="ttc" id="a00315_html_ga3655bc324008553ca61f39952d0b2d08"><div class="ttname"><a href="a00315.html#ga3655bc324008553ca61f39952d0b2d08">glm::double2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, highp &gt; double2x3</div><div class="ttdoc">double-qualifier floating-point matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00121">compatibility.hpp:121</a></div></div>
<div class="ttc" id="a00315_html_ga939b0cff15cee3030f75c1b2e36f89fe"><div class="ttname"><a href="a00315.html#ga939b0cff15cee3030f75c1b2e36f89fe">glm::float2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, highp &gt; float2x3</div><div class="ttdoc">single-qualifier floating-point matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00105">compatibility.hpp:105</a></div></div>
<div class="ttc" id="a00315_html_gaa4cbe16a92cf3664376c7a2fc5126aa8"><div class="ttname"><a href="a00315.html#gaa4cbe16a92cf3664376c7a2fc5126aa8">glm::int3x2</a></div><div class="ttdeci">mat&lt; 3, 2, int, highp &gt; int3x2</div><div class="ttdoc">integer matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00091">compatibility.hpp:91</a></div></div>
<div class="ttc" id="a00315_html_ga13c3200b82708f73faac6d7f09ec91a3"><div class="ttname"><a href="a00315.html#ga13c3200b82708f73faac6d7f09ec91a3">glm::bool4</a></div><div class="ttdeci">vec&lt; 4, bool, highp &gt; bool4</div><div class="ttdoc">boolean type with 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00069">compatibility.hpp:69</a></div></div>
<div class="ttc" id="a00315_html_ga97a39dd9bc7d572810d80b8467cbffa1"><div class="ttname"><a href="a00315.html#ga97a39dd9bc7d572810d80b8467cbffa1">glm::int4x2</a></div><div class="ttdeci">mat&lt; 4, 2, int, highp &gt; int4x2</div><div class="ttdoc">integer matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00094">compatibility.hpp:94</a></div></div>
<div class="ttc" id="a00315_html_ga7f895c936f0c29c8729afbbf22806090"><div class="ttname"><a href="a00315.html#ga7f895c936f0c29c8729afbbf22806090">glm::bool1x1</a></div><div class="ttdeci">bool bool1x1</div><div class="ttdoc">boolean matrix with 1 x 1 component. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00071">compatibility.hpp:71</a></div></div>
<div class="ttc" id="a00315_html_ga356f8c3a7e7d6376d3d4b0a026407183"><div class="ttname"><a href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">glm::saturate</a></div><div class="ttdeci">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt; saturate(const vec&lt; 4, T, Q &gt; &amp;x)</div><div class="ttdoc">Returns clamp(x, 0, 1) for each component in x. (From GLM_GTX_compatibility) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00053">compatibility.hpp:53</a></div></div>
<div class="ttc" id="a00315_html_ga99629f818737f342204071ef8296b2ed"><div class="ttname"><a href="a00315.html#ga99629f818737f342204071ef8296b2ed">glm::bool3</a></div><div class="ttdeci">vec&lt; 3, bool, highp &gt; bool3</div><div class="ttdoc">boolean type with 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00068">compatibility.hpp:68</a></div></div>
<div class="ttc" id="a00241_html_ga7cd77683da6361e297c56443fc70806d"><div class="ttname"><a href="a00241.html#ga7cd77683da6361e297c56443fc70806d">glm::clamp</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR genType clamp(genType x, genType minVal, genType maxVal)</div><div class="ttdoc">Returns min(max(x, minVal), maxVal) for each component in x using the floating-point values minVal an...</div></div>
<div class="ttc" id="a00315_html_ga4e5ce477c15836b21e3c42daac68554d"><div class="ttname"><a href="a00315.html#ga4e5ce477c15836b21e3c42daac68554d">glm::int2x2</a></div><div class="ttdeci">mat&lt; 2, 2, int, highp &gt; int2x2</div><div class="ttdoc">integer matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00088">compatibility.hpp:88</a></div></div>
<div class="ttc" id="a00315_html_gafe3a8fd56354caafe24bfe1b1e3ad22a"><div class="ttname"><a href="a00315.html#gafe3a8fd56354caafe24bfe1b1e3ad22a">glm::int2</a></div><div class="ttdeci">vec&lt; 2, int, highp &gt; int2</div><div class="ttdoc">integer vector with 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00083">compatibility.hpp:83</a></div></div>
<div class="ttc" id="a00315_html_ga04dee1552424198b8f58b377c2ee00d8"><div class="ttname"><a href="a00315.html#ga04dee1552424198b8f58b377c2ee00d8">glm::int4x4</a></div><div class="ttdeci">mat&lt; 4, 4, int, highp &gt; int4x4</div><div class="ttdoc">integer matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00096">compatibility.hpp:96</a></div></div>
<div class="ttc" id="a00315_html_gac7d7311f7e0fa8b6163d96dab033a755"><div class="ttname"><a href="a00315.html#gac7d7311f7e0fa8b6163d96dab033a755">glm::bool3x2</a></div><div class="ttdeci">mat&lt; 3, 2, bool, highp &gt; bool3x2</div><div class="ttdoc">boolean matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00075">compatibility.hpp:75</a></div></div>
<div class="ttc" id="a00315_html_gafad66a02ccd360c86d6ab9ff9cfbc19c"><div class="ttname"><a href="a00315.html#gafad66a02ccd360c86d6ab9ff9cfbc19c">glm::double4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, highp &gt; double4x3</div><div class="ttdoc">double-qualifier floating-point matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00127">compatibility.hpp:127</a></div></div>
<div class="ttc" id="a00315_html_gad0f5dc7f22c2065b1b06d57f1c0658fe"><div class="ttname"><a href="a00315.html#gad0f5dc7f22c2065b1b06d57f1c0658fe">glm::bool4x3</a></div><div class="ttdeci">mat&lt; 4, 3, bool, highp &gt; bool4x3</div><div class="ttdoc">boolean matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00079">compatibility.hpp:79</a></div></div>
<div class="ttc" id="a00315_html_ga20b861a9b6e2a300323671c57a02525b"><div class="ttname"><a href="a00315.html#ga20b861a9b6e2a300323671c57a02525b">glm::double1</a></div><div class="ttdeci">double double1</div><div class="ttdoc">double-qualifier floating-point vector with 1 component. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00114">compatibility.hpp:114</a></div></div>
<div class="ttc" id="a00315_html_ga3d8b9028a1053a44a98902cd1c389472"><div class="ttname"><a href="a00315.html#ga3d8b9028a1053a44a98902cd1c389472">glm::double3</a></div><div class="ttdeci">vec&lt; 3, double, highp &gt; double3</div><div class="ttdoc">double-qualifier floating-point vector with 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00116">compatibility.hpp:116</a></div></div>
<div class="ttc" id="a00315_html_gaf92f58af24f35617518aeb3d4f63fda6"><div class="ttname"><a href="a00315.html#gaf92f58af24f35617518aeb3d4f63fda6">glm::double4</a></div><div class="ttdeci">vec&lt; 4, double, highp &gt; double4</div><div class="ttdoc">double-qualifier floating-point vector with 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00117">compatibility.hpp:117</a></div></div>
<div class="ttc" id="a00315_html_ga15c9649286f0bf431bdf9b3509580048"><div class="ttname"><a href="a00315.html#ga15c9649286f0bf431bdf9b3509580048">glm::int3x3</a></div><div class="ttdeci">mat&lt; 3, 3, int, highp &gt; int3x3</div><div class="ttdoc">integer matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00092">compatibility.hpp:92</a></div></div>
<div class="ttc" id="a00315_html_ga6c97b99aac3e302053ffb58aace9033c"><div class="ttname"><a href="a00315.html#ga6c97b99aac3e302053ffb58aace9033c">glm::bool3x3</a></div><div class="ttdeci">mat&lt; 3, 3, bool, highp &gt; bool3x3</div><div class="ttdoc">boolean matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00076">compatibility.hpp:76</a></div></div>
<div class="ttc" id="a00315_html_gaa6c69f04ba95f3faedf95dae874de576"><div class="ttname"><a href="a00315.html#gaa6c69f04ba95f3faedf95dae874de576">glm::float3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, highp &gt; float3x2</div><div class="ttdoc">single-qualifier floating-point matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00107">compatibility.hpp:107</a></div></div>
<div class="ttc" id="a00315_html_gaecdef18c819c205aeee9f94dc93de56a"><div class="ttname"><a href="a00315.html#gaecdef18c819c205aeee9f94dc93de56a">glm::int4</a></div><div class="ttdeci">vec&lt; 4, int, highp &gt; int4</div><div class="ttdoc">integer vector with 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00085">compatibility.hpp:85</a></div></div>
<div class="ttc" id="a00315_html_ga31b729b04facccda73f07ed26958b3c2"><div class="ttname"><a href="a00315.html#ga31b729b04facccda73f07ed26958b3c2">glm::double2</a></div><div class="ttdeci">vec&lt; 2, double, highp &gt; double2</div><div class="ttdoc">double-qualifier floating-point vector with 2 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00115">compatibility.hpp:115</a></div></div>
<div class="ttc" id="a00315_html_ga6ceb5d38a58becdf420026e12a6562f3"><div class="ttname"><a href="a00315.html#ga6ceb5d38a58becdf420026e12a6562f3">glm::float3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, highp &gt; float3x3</div><div class="ttdoc">single-qualifier floating-point matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00108">compatibility.hpp:108</a></div></div>
<div class="ttc" id="a00241_html_ga8e93f374aae27d1a88b921860351f8d4"><div class="ttname"><a href="a00241.html#ga8e93f374aae27d1a88b921860351f8d4">glm::mix</a></div><div class="ttdeci">GLM_FUNC_DECL genTypeT mix(genTypeT x, genTypeT y, genTypeU a)</div><div class="ttdoc">If genTypeU is a floating scalar or vector: Returns x * (1.0 - a) + y * a, i.e., the linear blend of ...</div></div>
<div class="ttc" id="a00315_html_gaa09ab65ec9c3c54305ff502e2b1fe6d9"><div class="ttname"><a href="a00315.html#gaa09ab65ec9c3c54305ff502e2b1fe6d9">glm::bool2</a></div><div class="ttdeci">vec&lt; 2, bool, highp &gt; bool2</div><div class="ttdoc">boolean type with 2 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00067">compatibility.hpp:67</a></div></div>
<div class="ttc" id="a00315_html_gae7d6b679463d37d6c527d478fb470fdf"><div class="ttname"><a href="a00315.html#gae7d6b679463d37d6c527d478fb470fdf">glm::bool3x4</a></div><div class="ttdeci">mat&lt; 3, 4, bool, highp &gt; bool3x4</div><div class="ttdoc">boolean matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00077">compatibility.hpp:77</a></div></div>
<div class="ttc" id="a00315_html_ga2749d59a7fddbac44f34ba78e57ef807"><div class="ttname"><a href="a00315.html#ga2749d59a7fddbac44f34ba78e57ef807">glm::int2x4</a></div><div class="ttdeci">mat&lt; 2, 4, int, highp &gt; int2x4</div><div class="ttdoc">integer matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00090">compatibility.hpp:90</a></div></div>
<div class="ttc" id="a00315_html_ga4d7fa65be8e8e4ad6d920b45c44e471f"><div class="ttname"><a href="a00315.html#ga4d7fa65be8e8e4ad6d920b45c44e471f">glm::bool2x4</a></div><div class="ttdeci">mat&lt; 2, 4, bool, highp &gt; bool2x4</div><div class="ttdoc">boolean matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) </div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00074">compatibility.hpp:74</a></div></div>
<div class="ttc" id="a00315_html_ga2ef022e453d663d70aec414b2a80f756"><div class="ttname"><a href="a00315.html#ga2ef022e453d663d70aec414b2a80f756">glm::double3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, highp &gt; double3x4</div><div class="ttdoc">double-qualifier floating-point matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00125">compatibility.hpp:125</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
<div class="ttc" id="a00315_html_ga4d2679c321b793ca3784fe0315bb5332"><div class="ttname"><a href="a00315.html#ga4d2679c321b793ca3784fe0315bb5332">glm::float3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, highp &gt; float3x4</div><div class="ttdoc">single-qualifier floating-point matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) ...</div><div class="ttdef"><b>Definition:</b> <a href="a00017_source.html#l00109">compatibility.hpp:109</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
