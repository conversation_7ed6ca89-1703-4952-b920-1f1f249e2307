<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: associated_min_max.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">associated_min_max.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00007.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_associated_min_max is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_associated_min_max extension included&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;{</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        GLM_FUNC_DECL U <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(T x, U a, T y, U b);</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        GLM_FUNC_DECL vec&lt;2, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b);</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                T x, <span class="keyword">const</span> vec&lt;L, U, Q&gt;&amp; a,</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                T y, <span class="keyword">const</span> vec&lt;L, U, Q&gt;&amp; b);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, U a,</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, U b);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        GLM_FUNC_DECL U <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                T x, U a,</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                T y, U b,</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                T z, U c);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        GLM_FUNC_DECL U <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                T x, U a,</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                T y, U b,</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                T z, U c,</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                T w, U d);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c,</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; w, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                T x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                T y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                T z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c,</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                T w, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a>(</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, U a,</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, U b,</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, U c,</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; w, U d);</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        GLM_FUNC_DECL U <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(T x, U a, T y, U b);</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        GLM_FUNC_DECL vec&lt;2, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                T x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                T y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, U a,</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, U b);</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        GLM_FUNC_DECL U <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;                T x, U a,</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;                T y, U b,</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;                T z, U c);</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;                T x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;                T y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;                T z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c);</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, U a,</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, U b,</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, U c);</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        GLM_FUNC_DECL U <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;                T x, U a,</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                T y, U b,</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;                T z, U c,</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;                T w, U d);</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c,</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; w, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;                T x, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; a,</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;                T y, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; b,</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;                T z, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; c,</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;                T w, vec&lt;L, U, Q&gt; <span class="keyword">const</span>&amp; d);</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, <span class="keyword">typename</span> U, qualifier Q&gt;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;        GLM_FUNC_DECL vec&lt;L, U, Q&gt; <a class="code" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a>(</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, U a,</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, U b,</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; z, U c,</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;                vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; w, U d);</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;<span class="preprocessor">#include &quot;associated_min_max.inl&quot;</span></div>
<div class="ttc" id="a00308_html_gab9c3dd74cac899d2c625b5767ea3b3fb"><div class="ttname"><a href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">glm::associatedMax</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, U, Q &gt; associatedMax(vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b, vec&lt; L, T, Q &gt; const &amp;z, U c, vec&lt; L, T, Q &gt; const &amp;w, U d)</div><div class="ttdoc">Maximum comparison between 4 variables and returns 4 associated variable values. </div></div>
<div class="ttc" id="a00308_html_ga2db7e351994baee78540a562d4bb6d3b"><div class="ttname"><a href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">glm::associatedMin</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, U, Q &gt; associatedMin(vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b, vec&lt; L, T, Q &gt; const &amp;z, U c, vec&lt; L, T, Q &gt; const &amp;w, U d)</div><div class="ttdoc">Minimum comparison between 4 variables and returns 4 associated variable values. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
