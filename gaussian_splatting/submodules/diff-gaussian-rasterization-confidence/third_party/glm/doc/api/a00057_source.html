<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: matrix.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">matrix.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00057.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00184.html">vec2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00185.html">vec3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00186.html">vec4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00048.html">mat2x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00049.html">mat2x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00050.html">mat2x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00051.html">mat3x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00052.html">mat3x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00053.html">mat3x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00054.html">mat4x2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00055.html">mat4x3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00056.html">mat4x4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a> {</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;{</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        <span class="keyword">struct </span>outerProduct_trait{};</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;2, 2, T, Q&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        {</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                <span class="keyword">typedef</span> mat&lt;2, 2, T, Q&gt; type;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        };</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;2, 3, T, Q&gt;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        {</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                <span class="keyword">typedef</span> mat&lt;3, 2, T, Q&gt; type;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        };</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;2, 4, T, Q&gt;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        {</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                <span class="keyword">typedef</span> mat&lt;4, 2, T, Q&gt; type;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        };</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;3, 2, T, Q&gt;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        {</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                <span class="keyword">typedef</span> mat&lt;2, 3, T, Q&gt; type;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        };</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;3, 3, T, Q&gt;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        {</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                <span class="keyword">typedef</span> mat&lt;3, 3, T, Q&gt; type;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        };</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;3, 4, T, Q&gt;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        {</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                <span class="keyword">typedef</span> mat&lt;4, 3, T, Q&gt; type;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        };</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;4, 2, T, Q&gt;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        {</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                <span class="keyword">typedef</span> mat&lt;2, 4, T, Q&gt; type;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        };</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;4, 3, T, Q&gt;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        {</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                <span class="keyword">typedef</span> mat&lt;3, 4, T, Q&gt; type;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        };</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        <span class="keyword">struct </span>outerProduct_trait&lt;4, 4, T, Q&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        {</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                <span class="keyword">typedef</span> mat&lt;4, 4, T, Q&gt; type;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        };</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        GLM_FUNC_DECL mat&lt;C, R, T, Q&gt; <a class="code" href="a00371.html#gaf14569404c779fedca98d0b9b8e58c1f">matrixCompMult</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x, mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        GLM_FUNC_DECL <span class="keyword">typename</span> detail::outerProduct_trait&lt;C, R, T, Q&gt;::type <a class="code" href="a00371.html#gac29fb7bae75a8e4c1b74cbbf85520e50">outerProduct</a>(vec&lt;C, T, Q&gt; <span class="keyword">const</span>&amp; c, vec&lt;R, T, Q&gt; <span class="keyword">const</span>&amp; r);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        GLM_FUNC_DECL <span class="keyword">typename</span> mat&lt;C, R, T, Q&gt;::transpose_type <a class="code" href="a00371.html#gae679d841da8ce9dbcc6c2d454f15bc35">transpose</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00371.html#gad7928795124768e058f99dce270f5c8d">determinant</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        <span class="keyword">template</span>&lt;length_t C, length_t R, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        GLM_FUNC_DECL mat&lt;C, R, T, Q&gt; <a class="code" href="a00371.html#gaed509fe8129b01e4f20a6d0de5690091">inverse</a>(mat&lt;C, R, T, Q&gt; <span class="keyword">const</span>&amp; m);</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="preprocessor">#include &quot;detail/func_matrix.inl&quot;</span></div>
<div class="ttc" id="a00371_html_gaf14569404c779fedca98d0b9b8e58c1f"><div class="ttname"><a href="a00371.html#gaf14569404c779fedca98d0b9b8e58c1f">glm::matrixCompMult</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt; matrixCompMult(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)</div><div class="ttdoc">Multiply matrix x by matrix y component-wise, i.e., result[i][j] is the scalar product of x[i][j] and...</div></div>
<div class="ttc" id="a00056_html"><div class="ttname"><a href="a00056.html">mat4x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00371_html_gad7928795124768e058f99dce270f5c8d"><div class="ttname"><a href="a00371.html#gad7928795124768e058f99dce270f5c8d">glm::determinant</a></div><div class="ttdeci">GLM_FUNC_DECL T determinant(mat&lt; C, R, T, Q &gt; const &amp;m)</div><div class="ttdoc">Return the determinant of a squared matrix. </div></div>
<div class="ttc" id="a00050_html"><div class="ttname"><a href="a00050.html">mat2x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00371_html_gac29fb7bae75a8e4c1b74cbbf85520e50"><div class="ttname"><a href="a00371.html#gac29fb7bae75a8e4c1b74cbbf85520e50">glm::outerProduct</a></div><div class="ttdeci">GLM_FUNC_DECL detail::outerProduct_trait&lt; C, R, T, Q &gt;::type outerProduct(vec&lt; C, T, Q &gt; const &amp;c, vec&lt; R, T, Q &gt; const &amp;r)</div><div class="ttdoc">Treats the first parameter c as a column vector and the second parameter r as a row vector and does a...</div></div>
<div class="ttc" id="a00055_html"><div class="ttname"><a href="a00055.html">mat4x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00184_html"><div class="ttname"><a href="a00184.html">vec2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00371_html_gae679d841da8ce9dbcc6c2d454f15bc35"><div class="ttname"><a href="a00371.html#gae679d841da8ce9dbcc6c2d454f15bc35">glm::transpose</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;::transpose_type transpose(mat&lt; C, R, T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the transposed matrix of x. </div></div>
<div class="ttc" id="a00049_html"><div class="ttname"><a href="a00049.html">mat2x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00054_html"><div class="ttname"><a href="a00054.html">mat4x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00052_html"><div class="ttname"><a href="a00052.html">mat3x3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00186_html"><div class="ttname"><a href="a00186.html">vec4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00371_html_gaed509fe8129b01e4f20a6d0de5690091"><div class="ttname"><a href="a00371.html#gaed509fe8129b01e4f20a6d0de5690091">glm::inverse</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt; inverse(mat&lt; C, R, T, Q &gt; const &amp;m)</div><div class="ttdoc">Return the inverse of a squared matrix. </div></div>
<div class="ttc" id="a00053_html"><div class="ttname"><a href="a00053.html">mat3x4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00051_html"><div class="ttname"><a href="a00051.html">mat3x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00185_html"><div class="ttname"><a href="a00185.html">vec3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00048_html"><div class="ttname"><a href="a00048.html">mat2x2.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
