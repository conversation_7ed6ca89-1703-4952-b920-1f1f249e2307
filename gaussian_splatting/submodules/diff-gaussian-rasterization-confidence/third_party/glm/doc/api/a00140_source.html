<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: reciprocal.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">reciprocal.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00140.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_reciprocal extension included&quot;)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;{</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#gae4bcbebee670c5ea155f0777b3acbd84">sec</a>(genType <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga59dd0005b6474eea48af743b4f14ebbb">csc</a>(genType <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga3a7b517a95bbd3ad74da3aea87a66314">cot</a>(genType <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga2c5b7f962c2c9ff684e6d2de48db1f10">asec</a>(genType x);</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga1b4bed91476b9b915e76b4a30236d330">acsc</a>(genType x);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#gaeadfb9c9d71093f7865b2ba2ca8d104d">acot</a>(genType x);</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga9a5cfd1e7170104a7b33863b1b75e5ae">sech</a>(genType <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga6d95843ff3ca6472ab399ba171d290a0">csch</a>(genType <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga6b8b770eb7198e4dea59d52e6db81442">coth</a>(genType <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#gaec7586dccfe431f850d006f3824b8ca6">asech</a>(genType x);</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#ga4b50aa5e5afc7e19ec113ab91596c576">acsch</a>(genType x);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00301.html#gafaca98a7100170db8841f446282debfa">acoth</a>(genType x);</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="preprocessor">#include &quot;reciprocal.inl&quot;</span></div>
<div class="ttc" id="a00301_html_gae4bcbebee670c5ea155f0777b3acbd84"><div class="ttname"><a href="a00301.html#gae4bcbebee670c5ea155f0777b3acbd84">glm::sec</a></div><div class="ttdeci">GLM_FUNC_DECL genType sec(genType angle)</div><div class="ttdoc">Secant function. </div></div>
<div class="ttc" id="a00301_html_ga59dd0005b6474eea48af743b4f14ebbb"><div class="ttname"><a href="a00301.html#ga59dd0005b6474eea48af743b4f14ebbb">glm::csc</a></div><div class="ttdeci">GLM_FUNC_DECL genType csc(genType angle)</div><div class="ttdoc">Cosecant function. </div></div>
<div class="ttc" id="a00301_html_ga6b8b770eb7198e4dea59d52e6db81442"><div class="ttname"><a href="a00301.html#ga6b8b770eb7198e4dea59d52e6db81442">glm::coth</a></div><div class="ttdeci">GLM_FUNC_DECL genType coth(genType angle)</div><div class="ttdoc">Cotangent hyperbolic function. </div></div>
<div class="ttc" id="a00301_html_ga2c5b7f962c2c9ff684e6d2de48db1f10"><div class="ttname"><a href="a00301.html#ga2c5b7f962c2c9ff684e6d2de48db1f10">glm::asec</a></div><div class="ttdeci">GLM_FUNC_DECL genType asec(genType x)</div><div class="ttdoc">Inverse secant function. </div></div>
<div class="ttc" id="a00257_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle. </div></div>
<div class="ttc" id="a00301_html_ga3a7b517a95bbd3ad74da3aea87a66314"><div class="ttname"><a href="a00301.html#ga3a7b517a95bbd3ad74da3aea87a66314">glm::cot</a></div><div class="ttdeci">GLM_FUNC_DECL genType cot(genType angle)</div><div class="ttdoc">Cotangent function. </div></div>
<div class="ttc" id="a00301_html_ga1b4bed91476b9b915e76b4a30236d330"><div class="ttname"><a href="a00301.html#ga1b4bed91476b9b915e76b4a30236d330">glm::acsc</a></div><div class="ttdeci">GLM_FUNC_DECL genType acsc(genType x)</div><div class="ttdoc">Inverse cosecant function. </div></div>
<div class="ttc" id="a00301_html_ga9a5cfd1e7170104a7b33863b1b75e5ae"><div class="ttname"><a href="a00301.html#ga9a5cfd1e7170104a7b33863b1b75e5ae">glm::sech</a></div><div class="ttdeci">GLM_FUNC_DECL genType sech(genType angle)</div><div class="ttdoc">Secant hyperbolic function. </div></div>
<div class="ttc" id="a00301_html_ga6d95843ff3ca6472ab399ba171d290a0"><div class="ttname"><a href="a00301.html#ga6d95843ff3ca6472ab399ba171d290a0">glm::csch</a></div><div class="ttdeci">GLM_FUNC_DECL genType csch(genType angle)</div><div class="ttdoc">Cosecant hyperbolic function. </div></div>
<div class="ttc" id="a00301_html_gafaca98a7100170db8841f446282debfa"><div class="ttname"><a href="a00301.html#gafaca98a7100170db8841f446282debfa">glm::acoth</a></div><div class="ttdeci">GLM_FUNC_DECL genType acoth(genType x)</div><div class="ttdoc">Inverse cotangent hyperbolic function. </div></div>
<div class="ttc" id="a00301_html_gaeadfb9c9d71093f7865b2ba2ca8d104d"><div class="ttname"><a href="a00301.html#gaeadfb9c9d71093f7865b2ba2ca8d104d">glm::acot</a></div><div class="ttdeci">GLM_FUNC_DECL genType acot(genType x)</div><div class="ttdoc">Inverse cotangent function. </div></div>
<div class="ttc" id="a00301_html_gaec7586dccfe431f850d006f3824b8ca6"><div class="ttname"><a href="a00301.html#gaec7586dccfe431f850d006f3824b8ca6">glm::asech</a></div><div class="ttdeci">GLM_FUNC_DECL genType asech(genType x)</div><div class="ttdoc">Inverse secant hyperbolic function. </div></div>
<div class="ttc" id="a00301_html_ga4b50aa5e5afc7e19ec113ab91596c576"><div class="ttname"><a href="a00301.html#ga4b50aa5e5afc7e19ec113ab91596c576">glm::acsch</a></div><div class="ttdeci">GLM_FUNC_DECL genType acsch(genType x)</div><div class="ttdoc">Inverse cosecant hyperbolic function. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
