<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: packing.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">gtc/packing.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00298.html">GLM_GTC_packing</a>  
<a href="#details">More...</a></p>

<p><a href="a00119_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga4944ad465ff950e926d49621f916c78d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga4944ad465ff950e926d49621f916c78d">packF2x11_1x10</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga4944ad465ff950e926d49621f916c78d"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first two components of the normalized floating-point value v into 11-bit signless floating-point values.  <a href="a00298.html#ga4944ad465ff950e926d49621f916c78d">More...</a><br /></td></tr>
<tr class="separator:ga4944ad465ff950e926d49621f916c78d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f648fc205467792dc6d8c59c748f8a6"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga3f648fc205467792dc6d8c59c748f8a6">packF3x9_E1x5</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga3f648fc205467792dc6d8c59c748f8a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first two components of the normalized floating-point value v into 11-bit signless floating-point values.  <a href="a00298.html#ga3f648fc205467792dc6d8c59c748f8a6">More...</a><br /></td></tr>
<tr class="separator:ga3f648fc205467792dc6d8c59c748f8a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uint16, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga2d8bbce673ebc04831c1fb05c47f5251">packHalf</a> (vec&lt; L, float, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer vector obtained by converting the components of a floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification.  <a href="a00298.html#ga2d8bbce673ebc04831c1fb05c47f5251">More...</a><br /></td></tr>
<tr class="separator:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43f2093b6ff192a79058ff7834fc3528"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga43f2093b6ff192a79058ff7834fc3528">packHalf1x16</a> (float v)</td></tr>
<tr class="memdesc:ga43f2093b6ff192a79058ff7834fc3528"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a floating-point scalar to the 16-bit floating-point representation found in the OpenGL Specification, and then packing this 16-bit value into a 16-bit unsigned integer.  <a href="a00298.html#ga43f2093b6ff192a79058ff7834fc3528">More...</a><br /></td></tr>
<tr class="separator:ga43f2093b6ff192a79058ff7834fc3528"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe2f7b39caf8f5ec555e1c059ec530e6"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gafe2f7b39caf8f5ec555e1c059ec530e6">packHalf4x16</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gafe2f7b39caf8f5ec555e1c059ec530e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a four-component floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification, and then packing these four 16-bit values into a 64-bit unsigned integer.  <a href="a00298.html#gafe2f7b39caf8f5ec555e1c059ec530e6">More...</a><br /></td></tr>
<tr class="separator:gafe2f7b39caf8f5ec555e1c059ec530e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06ecb6afb902dba45419008171db9023"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga06ecb6afb902dba45419008171db9023">packI3x10_1x2</a> (ivec4 const &amp;v)</td></tr>
<tr class="memdesc:ga06ecb6afb902dba45419008171db9023"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a four-component signed integer vector to the 10-10-10-2-bit signed integer representation found in the OpenGL Specification, and then packing these four values into a 32-bit unsigned integer.  <a href="a00298.html#ga06ecb6afb902dba45419008171db9023">More...</a><br /></td></tr>
<tr class="separator:ga06ecb6afb902dba45419008171db9023"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3644163cf3a47bf1d4af1f4b03013a7e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga3644163cf3a47bf1d4af1f4b03013a7e">packInt2x16</a> (i16vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga3644163cf3a47bf1d4af1f4b03013a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#ga3644163cf3a47bf1d4af1f4b03013a7e">More...</a><br /></td></tr>
<tr class="separator:ga3644163cf3a47bf1d4af1f4b03013a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1e4c8a9e67d86b61a6eec86703a827a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gad1e4c8a9e67d86b61a6eec86703a827a">packInt2x32</a> (i32vec2 const &amp;v)</td></tr>
<tr class="memdesc:gad1e4c8a9e67d86b61a6eec86703a827a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#gad1e4c8a9e67d86b61a6eec86703a827a">More...</a><br /></td></tr>
<tr class="separator:gad1e4c8a9e67d86b61a6eec86703a827a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8884b1f2292414f36d59ef3be5d62914"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga8884b1f2292414f36d59ef3be5d62914">packInt2x8</a> (i8vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga8884b1f2292414f36d59ef3be5d62914"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#ga8884b1f2292414f36d59ef3be5d62914">More...</a><br /></td></tr>
<tr class="separator:ga8884b1f2292414f36d59ef3be5d62914"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1989f093a27ae69cf9207145be48b3d7"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1989f093a27ae69cf9207145be48b3d7">packInt4x16</a> (i16vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga1989f093a27ae69cf9207145be48b3d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#ga1989f093a27ae69cf9207145be48b3d7">More...</a><br /></td></tr>
<tr class="separator:ga1989f093a27ae69cf9207145be48b3d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2238401d5ce2aaade1a44ba19709072"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaf2238401d5ce2aaade1a44ba19709072">packInt4x8</a> (i8vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaf2238401d5ce2aaade1a44ba19709072"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#gaf2238401d5ce2aaade1a44ba19709072">More...</a><br /></td></tr>
<tr class="separator:gaf2238401d5ce2aaade1a44ba19709072"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0466daf4c90f76cc64b3f105ce727295"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0466daf4c90f76cc64b3f105ce727295"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga0466daf4c90f76cc64b3f105ce727295">packRGBM</a> (vec&lt; 3, T, Q &gt; const &amp;rgb)</td></tr>
<tr class="memdesc:ga0466daf4c90f76cc64b3f105ce727295"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer vector obtained by converting the components of a floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification.  <a href="a00298.html#ga0466daf4c90f76cc64b3f105ce727295">More...</a><br /></td></tr>
<tr class="separator:ga0466daf4c90f76cc64b3f105ce727295"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="memTemplParams" colspan="2">template&lt;typename intType , length_t L, typename floatType , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, intType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#gaa54b5855a750d6aeb12c1c902f5939b8">packSnorm</a> (vec&lt; L, floatType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into signed integer values.  <a href="a00298.html#gaa54b5855a750d6aeb12c1c902f5939b8">More...</a><br /></td></tr>
<tr class="separator:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab22f8bcfdb5fc65af4701b25f143c1af"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab22f8bcfdb5fc65af4701b25f143c1af">packSnorm1x16</a> (float v)</td></tr>
<tr class="memdesc:gab22f8bcfdb5fc65af4701b25f143c1af"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into 16-bit integer value.  <a href="a00298.html#gab22f8bcfdb5fc65af4701b25f143c1af">More...</a><br /></td></tr>
<tr class="separator:gab22f8bcfdb5fc65af4701b25f143c1af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae3592e0795e62aaa1865b3a10496a7a1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gae3592e0795e62aaa1865b3a10496a7a1">packSnorm1x8</a> (float s)</td></tr>
<tr class="memdesc:gae3592e0795e62aaa1865b3a10496a7a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into 8-bit integer value.  <a href="a00298.html#gae3592e0795e62aaa1865b3a10496a7a1">More...</a><br /></td></tr>
<tr class="separator:gae3592e0795e62aaa1865b3a10496a7a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6be3cfb2cce3702f03e91bbeb5286d7e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga6be3cfb2cce3702f03e91bbeb5286d7e">packSnorm2x8</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga6be3cfb2cce3702f03e91bbeb5286d7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8-bit integer values.  <a href="a00298.html#ga6be3cfb2cce3702f03e91bbeb5286d7e">More...</a><br /></td></tr>
<tr class="separator:ga6be3cfb2cce3702f03e91bbeb5286d7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab997545661877d2c7362a5084d3897d3"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab997545661877d2c7362a5084d3897d3">packSnorm3x10_1x2</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gab997545661877d2c7362a5084d3897d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first three components of the normalized floating-point value v into 10-bit signed integer values.  <a href="a00298.html#gab997545661877d2c7362a5084d3897d3">More...</a><br /></td></tr>
<tr class="separator:gab997545661877d2c7362a5084d3897d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga358943934d21da947d5bcc88c2ab7832"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga358943934d21da947d5bcc88c2ab7832">packSnorm4x16</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga358943934d21da947d5bcc88c2ab7832"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 16-bit integer values.  <a href="a00298.html#ga358943934d21da947d5bcc88c2ab7832">More...</a><br /></td></tr>
<tr class="separator:ga358943934d21da947d5bcc88c2ab7832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada3d88d59f0f458f9c51a9fd359a4bc0"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gada3d88d59f0f458f9c51a9fd359a4bc0">packU3x10_1x2</a> (uvec4 const &amp;v)</td></tr>
<tr class="memdesc:gada3d88d59f0f458f9c51a9fd359a4bc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a four-component unsigned integer vector to the 10-10-10-2-bit unsigned integer representation found in the OpenGL Specification, and then packing these four values into a 32-bit unsigned integer.  <a href="a00298.html#gada3d88d59f0f458f9c51a9fd359a4bc0">More...</a><br /></td></tr>
<tr class="separator:gada3d88d59f0f458f9c51a9fd359a4bc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5eecc9e8cbaf51ac6cf57501e670ee19"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga5eecc9e8cbaf51ac6cf57501e670ee19">packUint2x16</a> (u16vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga5eecc9e8cbaf51ac6cf57501e670ee19"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#ga5eecc9e8cbaf51ac6cf57501e670ee19">More...</a><br /></td></tr>
<tr class="separator:ga5eecc9e8cbaf51ac6cf57501e670ee19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa864081097b86e83d8e4a4d79c382b22"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaa864081097b86e83d8e4a4d79c382b22">packUint2x32</a> (u32vec2 const &amp;v)</td></tr>
<tr class="memdesc:gaa864081097b86e83d8e4a4d79c382b22"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#gaa864081097b86e83d8e4a4d79c382b22">More...</a><br /></td></tr>
<tr class="separator:gaa864081097b86e83d8e4a4d79c382b22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c3c9fb53ae7823b10fa083909357590"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga3c3c9fb53ae7823b10fa083909357590">packUint2x8</a> (u8vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga3c3c9fb53ae7823b10fa083909357590"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#ga3c3c9fb53ae7823b10fa083909357590">More...</a><br /></td></tr>
<tr class="separator:ga3c3c9fb53ae7823b10fa083909357590"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ceb62cca347d8ace42ee90317a3f1f9"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2ceb62cca347d8ace42ee90317a3f1f9">packUint4x16</a> (u16vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga2ceb62cca347d8ace42ee90317a3f1f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#ga2ceb62cca347d8ace42ee90317a3f1f9">More...</a><br /></td></tr>
<tr class="separator:ga2ceb62cca347d8ace42ee90317a3f1f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0fe2f09aeb403cd66c1a062f58861ab"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaa0fe2f09aeb403cd66c1a062f58861ab">packUint4x8</a> (u8vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaa0fe2f09aeb403cd66c1a062f58861ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#gaa0fe2f09aeb403cd66c1a062f58861ab">More...</a><br /></td></tr>
<tr class="separator:gaa0fe2f09aeb403cd66c1a062f58861ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="memTemplParams" colspan="2">template&lt;typename uintType , length_t L, typename floatType , qualifier Q&gt; </td></tr>
<tr class="memitem:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uintType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#gaccd3f27e6ba5163eb7aa9bc8ff96251a">packUnorm</a> (vec&lt; L, floatType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gaccd3f27e6ba5163eb7aa9bc8ff96251a">More...</a><br /></td></tr>
<tr class="separator:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f82737bf2a44bedff1d286b76837886"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga9f82737bf2a44bedff1d286b76837886">packUnorm1x16</a> (float v)</td></tr>
<tr class="memdesc:ga9f82737bf2a44bedff1d286b76837886"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into a 16-bit integer value.  <a href="a00298.html#ga9f82737bf2a44bedff1d286b76837886">More...</a><br /></td></tr>
<tr class="separator:ga9f82737bf2a44bedff1d286b76837886"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga768e0337dd6246773f14aa0a421fe9a8"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga768e0337dd6246773f14aa0a421fe9a8">packUnorm1x5_1x6_1x5</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga768e0337dd6246773f14aa0a421fe9a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#ga768e0337dd6246773f14aa0a421fe9a8">More...</a><br /></td></tr>
<tr class="separator:ga768e0337dd6246773f14aa0a421fe9a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b2fa60df3460403817d28b082ee0736"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga4b2fa60df3460403817d28b082ee0736">packUnorm1x8</a> (float v)</td></tr>
<tr class="memdesc:ga4b2fa60df3460403817d28b082ee0736"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into a 8-bit integer value.  <a href="a00298.html#ga4b2fa60df3460403817d28b082ee0736">More...</a><br /></td></tr>
<tr class="separator:ga4b2fa60df3460403817d28b082ee0736"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f9abdb50f9be1aa1c14912504a0d98d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga7f9abdb50f9be1aa1c14912504a0d98d">packUnorm2x3_1x2</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga7f9abdb50f9be1aa1c14912504a0d98d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#ga7f9abdb50f9be1aa1c14912504a0d98d">More...</a><br /></td></tr>
<tr class="separator:ga7f9abdb50f9be1aa1c14912504a0d98d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab6bbd5be3b8e6db538ecb33a7844481c"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab6bbd5be3b8e6db538ecb33a7844481c">packUnorm2x4</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:gab6bbd5be3b8e6db538ecb33a7844481c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gab6bbd5be3b8e6db538ecb33a7844481c">More...</a><br /></td></tr>
<tr class="separator:gab6bbd5be3b8e6db538ecb33a7844481c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a666b1c688ab54100061ed06526de6e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga9a666b1c688ab54100061ed06526de6e">packUnorm2x8</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga9a666b1c688ab54100061ed06526de6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8-bit integer values.  <a href="a00298.html#ga9a666b1c688ab54100061ed06526de6e">More...</a><br /></td></tr>
<tr class="separator:ga9a666b1c688ab54100061ed06526de6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a1ee625d2707c60530fb3fca2980b19"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga8a1ee625d2707c60530fb3fca2980b19">packUnorm3x10_1x2</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga8a1ee625d2707c60530fb3fca2980b19"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first three components of the normalized floating-point value v into 10-bit unsigned integer values.  <a href="a00298.html#ga8a1ee625d2707c60530fb3fca2980b19">More...</a><br /></td></tr>
<tr class="separator:ga8a1ee625d2707c60530fb3fca2980b19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec4112086d7fb133bea104a7c237de52"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaec4112086d7fb133bea104a7c237de52">packUnorm3x5_1x1</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaec4112086d7fb133bea104a7c237de52"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gaec4112086d7fb133bea104a7c237de52">More...</a><br /></td></tr>
<tr class="separator:gaec4112086d7fb133bea104a7c237de52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f63c264e7ab63264e2b2a99fd393897"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1f63c264e7ab63264e2b2a99fd393897">packUnorm4x16</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga1f63c264e7ab63264e2b2a99fd393897"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 16-bit integer values.  <a href="a00298.html#ga1f63c264e7ab63264e2b2a99fd393897">More...</a><br /></td></tr>
<tr class="separator:ga1f63c264e7ab63264e2b2a99fd393897"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3e7e3ce521513584a53aedc5f9765c1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gad3e7e3ce521513584a53aedc5f9765c1">packUnorm4x4</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gad3e7e3ce521513584a53aedc5f9765c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gad3e7e3ce521513584a53aedc5f9765c1">More...</a><br /></td></tr>
<tr class="separator:gad3e7e3ce521513584a53aedc5f9765c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b1fd1e854705b1345e98409e0a25e50"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2b1fd1e854705b1345e98409e0a25e50">unpackF2x11_1x10</a> (uint32 p)</td></tr>
<tr class="memdesc:ga2b1fd1e854705b1345e98409e0a25e50"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and one 10-bit signless floating-point value .  <a href="a00298.html#ga2b1fd1e854705b1345e98409e0a25e50">More...</a><br /></td></tr>
<tr class="separator:ga2b1fd1e854705b1345e98409e0a25e50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9e60ebe3ad3eeced6a9ec6eb876d74e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab9e60ebe3ad3eeced6a9ec6eb876d74e">unpackF3x9_E1x5</a> (uint32 p)</td></tr>
<tr class="memdesc:gab9e60ebe3ad3eeced6a9ec6eb876d74e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and one 10-bit signless floating-point value .  <a href="a00298.html#gab9e60ebe3ad3eeced6a9ec6eb876d74e">More...</a><br /></td></tr>
<tr class="separator:gab9e60ebe3ad3eeced6a9ec6eb876d74e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30d6b2f1806315bcd6047131f547d33b"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga30d6b2f1806315bcd6047131f547d33b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, float, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga30d6b2f1806315bcd6047131f547d33b">unpackHalf</a> (vec&lt; L, uint16, Q &gt; const &amp;p)</td></tr>
<tr class="memdesc:ga30d6b2f1806315bcd6047131f547d33b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bit floating-point numbers and converting them to 32-bit floating-point values.  <a href="a00298.html#ga30d6b2f1806315bcd6047131f547d33b">More...</a><br /></td></tr>
<tr class="separator:ga30d6b2f1806315bcd6047131f547d33b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac37dedaba24b00adb4ec6e8f92c19dbf"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gac37dedaba24b00adb4ec6e8f92c19dbf">unpackHalf1x16</a> (uint16 v)</td></tr>
<tr class="memdesc:gac37dedaba24b00adb4ec6e8f92c19dbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point scalar with components obtained by unpacking a 16-bit unsigned integer into a 16-bit value, interpreted as a 16-bit floating-point number according to the OpenGL Specification, and converting it to 32-bit floating-point values.  <a href="a00298.html#gac37dedaba24b00adb4ec6e8f92c19dbf">More...</a><br /></td></tr>
<tr class="separator:gac37dedaba24b00adb4ec6e8f92c19dbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga57dfc41b2eb20b0ac00efae7d9c49dcd"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga57dfc41b2eb20b0ac00efae7d9c49dcd">unpackHalf4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:ga57dfc41b2eb20b0ac00efae7d9c49dcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a four-component floating-point vector with components obtained by unpacking a 64-bit unsigned integer into four 16-bit values, interpreting those values as 16-bit floating-point numbers according to the OpenGL Specification, and converting them to 32-bit floating-point values.  <a href="a00298.html#ga57dfc41b2eb20b0ac00efae7d9c49dcd">More...</a><br /></td></tr>
<tr class="separator:ga57dfc41b2eb20b0ac00efae7d9c49dcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a05330e5490be0908d3b117d82aff56"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL ivec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga9a05330e5490be0908d3b117d82aff56">unpackI3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga9a05330e5490be0908d3b117d82aff56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit signed integers.  <a href="a00298.html#ga9a05330e5490be0908d3b117d82aff56">More...</a><br /></td></tr>
<tr class="separator:ga9a05330e5490be0908d3b117d82aff56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaccde055882918a3175de82f4ca8b7d8e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i16vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaccde055882918a3175de82f4ca8b7d8e">unpackInt2x16</a> (int p)</td></tr>
<tr class="memdesc:gaccde055882918a3175de82f4ca8b7d8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaccde055882918a3175de82f4ca8b7d8e">More...</a><br /></td></tr>
<tr class="separator:gaccde055882918a3175de82f4ca8b7d8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab297c0bfd38433524791eb0584d8f08d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i32vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab297c0bfd38433524791eb0584d8f08d">unpackInt2x32</a> (int64 p)</td></tr>
<tr class="memdesc:gab297c0bfd38433524791eb0584d8f08d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gab297c0bfd38433524791eb0584d8f08d">More...</a><br /></td></tr>
<tr class="separator:gab297c0bfd38433524791eb0584d8f08d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0c59f1e259fca9e68adb2207a6b665e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i8vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab0c59f1e259fca9e68adb2207a6b665e">unpackInt2x8</a> (int16 p)</td></tr>
<tr class="memdesc:gab0c59f1e259fca9e68adb2207a6b665e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gab0c59f1e259fca9e68adb2207a6b665e">More...</a><br /></td></tr>
<tr class="separator:gab0c59f1e259fca9e68adb2207a6b665e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52c154a9b232b62c22517a700cc0c78c"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i16vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga52c154a9b232b62c22517a700cc0c78c">unpackInt4x16</a> (int64 p)</td></tr>
<tr class="memdesc:ga52c154a9b232b62c22517a700cc0c78c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#ga52c154a9b232b62c22517a700cc0c78c">More...</a><br /></td></tr>
<tr class="separator:ga52c154a9b232b62c22517a700cc0c78c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1cd8d2038cdd33a860801aa155a26221"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i8vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1cd8d2038cdd33a860801aa155a26221">unpackInt4x8</a> (int32 p)</td></tr>
<tr class="memdesc:ga1cd8d2038cdd33a860801aa155a26221"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#ga1cd8d2038cdd33a860801aa155a26221">More...</a><br /></td></tr>
<tr class="separator:ga1cd8d2038cdd33a860801aa155a26221"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga5c1ec97894b05ea21a05aea4f0204a02">unpackRGBM</a> (vec&lt; 4, T, Q &gt; const &amp;rgbm)</td></tr>
<tr class="memdesc:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bit floating-point numbers and converting them to 32-bit floating-point values.  <a href="a00298.html#ga5c1ec97894b05ea21a05aea4f0204a02">More...</a><br /></td></tr>
<tr class="separator:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="memTemplParams" colspan="2">template&lt;typename floatType , length_t L, typename intType , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga6d49b31e5c3f9df8e1f99ab62b999482">unpackSnorm</a> (vec&lt; L, intType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga6d49b31e5c3f9df8e1f99ab62b999482">More...</a><br /></td></tr>
<tr class="separator:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga96dd15002370627a443c835ab03a766c"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga96dd15002370627a443c835ab03a766c">unpackSnorm1x16</a> (uint16 p)</td></tr>
<tr class="memdesc:ga96dd15002370627a443c835ab03a766c"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a single 16-bit signed integers.  <a href="a00298.html#ga96dd15002370627a443c835ab03a766c">More...</a><br /></td></tr>
<tr class="separator:ga96dd15002370627a443c835ab03a766c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4851ff86678aa1c7ace9d67846894285"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga4851ff86678aa1c7ace9d67846894285">unpackSnorm1x8</a> (uint8 p)</td></tr>
<tr class="memdesc:ga4851ff86678aa1c7ace9d67846894285"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 8-bit unsigned integer p into a single 8-bit signed integers.  <a href="a00298.html#ga4851ff86678aa1c7ace9d67846894285">More...</a><br /></td></tr>
<tr class="separator:ga4851ff86678aa1c7ace9d67846894285"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b128e89be449fc71336968a66bf6e1a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga8b128e89be449fc71336968a66bf6e1a">unpackSnorm2x8</a> (uint16 p)</td></tr>
<tr class="memdesc:ga8b128e89be449fc71336968a66bf6e1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit signed integers.  <a href="a00298.html#ga8b128e89be449fc71336968a66bf6e1a">More...</a><br /></td></tr>
<tr class="separator:ga8b128e89be449fc71336968a66bf6e1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a4fbf79be9740e3c57737bc2af05e5b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga7a4fbf79be9740e3c57737bc2af05e5b">unpackSnorm3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga7a4fbf79be9740e3c57737bc2af05e5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers.  <a href="a00298.html#ga7a4fbf79be9740e3c57737bc2af05e5b">More...</a><br /></td></tr>
<tr class="separator:ga7a4fbf79be9740e3c57737bc2af05e5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaddf9c353528fe896106f7181219c7f4"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaaddf9c353528fe896106f7181219c7f4">unpackSnorm4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:gaaddf9c353528fe896106f7181219c7f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 64-bit unsigned integer p into four 16-bit signed integers.  <a href="a00298.html#gaaddf9c353528fe896106f7181219c7f4">More...</a><br /></td></tr>
<tr class="separator:gaaddf9c353528fe896106f7181219c7f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga48df3042a7d079767f5891a1bfd8a60a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga48df3042a7d079767f5891a1bfd8a60a">unpackU3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga48df3042a7d079767f5891a1bfd8a60a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit unsigned integers.  <a href="a00298.html#ga48df3042a7d079767f5891a1bfd8a60a">More...</a><br /></td></tr>
<tr class="separator:ga48df3042a7d079767f5891a1bfd8a60a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga035bbbeab7ec2b28c0529757395b645b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u16vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga035bbbeab7ec2b28c0529757395b645b">unpackUint2x16</a> (uint p)</td></tr>
<tr class="memdesc:ga035bbbeab7ec2b28c0529757395b645b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#ga035bbbeab7ec2b28c0529757395b645b">More...</a><br /></td></tr>
<tr class="separator:ga035bbbeab7ec2b28c0529757395b645b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf942ff11b65e83eb5f77e68329ebc6ab"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u32vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaf942ff11b65e83eb5f77e68329ebc6ab">unpackUint2x32</a> (uint64 p)</td></tr>
<tr class="memdesc:gaf942ff11b65e83eb5f77e68329ebc6ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaf942ff11b65e83eb5f77e68329ebc6ab">More...</a><br /></td></tr>
<tr class="separator:gaf942ff11b65e83eb5f77e68329ebc6ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7600a6c71784b637a410869d2a5adcd"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u8vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaa7600a6c71784b637a410869d2a5adcd">unpackUint2x8</a> (uint16 p)</td></tr>
<tr class="memdesc:gaa7600a6c71784b637a410869d2a5adcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaa7600a6c71784b637a410869d2a5adcd">More...</a><br /></td></tr>
<tr class="separator:gaa7600a6c71784b637a410869d2a5adcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab173834ef14cfc23a96a959f3ff4b8dc"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u16vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab173834ef14cfc23a96a959f3ff4b8dc">unpackUint4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:gab173834ef14cfc23a96a959f3ff4b8dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gab173834ef14cfc23a96a959f3ff4b8dc">More...</a><br /></td></tr>
<tr class="separator:gab173834ef14cfc23a96a959f3ff4b8dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6dc0e4341810a641c7ed08f10e335d1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u8vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaf6dc0e4341810a641c7ed08f10e335d1">unpackUint4x8</a> (uint32 p)</td></tr>
<tr class="memdesc:gaf6dc0e4341810a641c7ed08f10e335d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaf6dc0e4341810a641c7ed08f10e335d1">More...</a><br /></td></tr>
<tr class="separator:gaf6dc0e4341810a641c7ed08f10e335d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="memTemplParams" colspan="2">template&lt;typename floatType , length_t L, typename uintType , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga3e6ac9178b59f0b1b2f7599f2183eb7f">unpackUnorm</a> (vec&lt; L, uintType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga3e6ac9178b59f0b1b2f7599f2183eb7f">More...</a><br /></td></tr>
<tr class="separator:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83d34160a5cb7bcb5339823210fc7501"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga83d34160a5cb7bcb5339823210fc7501">unpackUnorm1x16</a> (uint16 p)</td></tr>
<tr class="memdesc:ga83d34160a5cb7bcb5339823210fc7501"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a of 16-bit unsigned integers.  <a href="a00298.html#ga83d34160a5cb7bcb5339823210fc7501">More...</a><br /></td></tr>
<tr class="separator:ga83d34160a5cb7bcb5339823210fc7501"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3bc08ecfc0f3339be93fb2b3b56d88a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab3bc08ecfc0f3339be93fb2b3b56d88a">unpackUnorm1x5_1x6_1x5</a> (uint16 p)</td></tr>
<tr class="memdesc:gab3bc08ecfc0f3339be93fb2b3b56d88a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#gab3bc08ecfc0f3339be93fb2b3b56d88a">More...</a><br /></td></tr>
<tr class="separator:gab3bc08ecfc0f3339be93fb2b3b56d88a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1319207e30874fb4931a9ee913983ee1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1319207e30874fb4931a9ee913983ee1">unpackUnorm1x8</a> (uint8 p)</td></tr>
<tr class="memdesc:ga1319207e30874fb4931a9ee913983ee1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a single 8-bit integer to a normalized floating-point value.  <a href="a00298.html#ga1319207e30874fb4931a9ee913983ee1">More...</a><br /></td></tr>
<tr class="separator:ga1319207e30874fb4931a9ee913983ee1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6abd5a9014df3b5ce4059008d2491260"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga6abd5a9014df3b5ce4059008d2491260">unpackUnorm2x3_1x2</a> (uint8 p)</td></tr>
<tr class="memdesc:ga6abd5a9014df3b5ce4059008d2491260"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga6abd5a9014df3b5ce4059008d2491260">More...</a><br /></td></tr>
<tr class="separator:ga6abd5a9014df3b5ce4059008d2491260"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e50476132fe5f27f08e273d9c70d85b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2e50476132fe5f27f08e273d9c70d85b">unpackUnorm2x4</a> (uint8 p)</td></tr>
<tr class="memdesc:ga2e50476132fe5f27f08e273d9c70d85b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga2e50476132fe5f27f08e273d9c70d85b">More...</a><br /></td></tr>
<tr class="separator:ga2e50476132fe5f27f08e273d9c70d85b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga637cbe3913dd95c6e7b4c99c61bd611f"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga637cbe3913dd95c6e7b4c99c61bd611f">unpackUnorm2x8</a> (uint16 p)</td></tr>
<tr class="memdesc:ga637cbe3913dd95c6e7b4c99c61bd611f"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit unsigned integers.  <a href="a00298.html#ga637cbe3913dd95c6e7b4c99c61bd611f">More...</a><br /></td></tr>
<tr class="separator:ga637cbe3913dd95c6e7b4c99c61bd611f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5156d3060355fe332865da2c7f78815f"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga5156d3060355fe332865da2c7f78815f">unpackUnorm3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga5156d3060355fe332865da2c7f78815f"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers.  <a href="a00298.html#ga5156d3060355fe332865da2c7f78815f">More...</a><br /></td></tr>
<tr class="separator:ga5156d3060355fe332865da2c7f78815f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ff95ff5bc16f396432ab67243dbae4d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga5ff95ff5bc16f396432ab67243dbae4d">unpackUnorm3x5_1x1</a> (uint16 p)</td></tr>
<tr class="memdesc:ga5ff95ff5bc16f396432ab67243dbae4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga5ff95ff5bc16f396432ab67243dbae4d">More...</a><br /></td></tr>
<tr class="separator:ga5ff95ff5bc16f396432ab67243dbae4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ae149c5d2473ac1e5f347bb654a242d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2ae149c5d2473ac1e5f347bb654a242d">unpackUnorm4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:ga2ae149c5d2473ac1e5f347bb654a242d"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 64-bit unsigned integer p into four 16-bit unsigned integers.  <a href="a00298.html#ga2ae149c5d2473ac1e5f347bb654a242d">More...</a><br /></td></tr>
<tr class="separator:ga2ae149c5d2473ac1e5f347bb654a242d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac58ee89d0e224bb6df5e8bbb18843a2d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gac58ee89d0e224bb6df5e8bbb18843a2d">unpackUnorm4x4</a> (uint16 p)</td></tr>
<tr class="memdesc:gac58ee89d0e224bb6df5e8bbb18843a2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#gac58ee89d0e224bb6df5e8bbb18843a2d">More...</a><br /></td></tr>
<tr class="separator:gac58ee89d0e224bb6df5e8bbb18843a2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00298.html">GLM_GTC_packing</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00119_source.html">gtc/packing.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
