<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: easing.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">easing.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00023.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_easing is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_easing extension included&quot;)</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga290c3e47cb0a49f2e8abe90b1872b649">linearInterpolation</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gaf42089d35855695132d217cd902304a0">quadraticEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga283717bc2d937547ad34ec0472234ee3">quadraticEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga03e8fc2d7945a4e63ee33b2159c14cea">quadraticEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gaff52f746102b94864d105563ba8895ae">cubicEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga40d746385d8bcc5973f5bc6a2340ca91">cubicEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga55134072b42d75452189321d4a2ad91c">cubicEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga808b41f14514f47dad5dcc69eb924afd">quarticEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga4dfb33fa7664aa888eb647999d329b98">quarticEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga6d000f852de12b197e154f234b20c505">quarticEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga097579d8e087dcf48037588140a21640">quinticEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga7dbd4d5c8da3f5353121f615e7b591d7">quinticEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga2a82d5c46df7e2d21cc0108eb7b83934">quinticEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gafb338ac6f6b2bcafee50e3dca5201dbf">sineEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gab3e454f883afc1606ef91363881bf5a3">sineEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gaa46e3d5fbf7a15caa28eff9ef192d7c7">sineEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga34508d4b204a321ec26d6086aa047997">circularEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga26fefde9ced9b72745fe21f1a3fe8da7">circularEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga0c1027637a5b02d4bb3612aa12599d69">circularEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga7f24ee9219ab4c84dc8de24be84c1e3c">exponentialEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga517f2bcfd15bc2c25c466ae50808efc3">exponentialEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga232fb6dc093c5ce94bee105ff2947501">exponentialEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga230918eccee4e113d10ec5b8cdc58695">elasticEaseIn</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gace9c9d1bdf88bf2ab1e7cdefa54c7365">elasticEaseOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga2db4ac8959559b11b4029e54812908d6">elasticEaseInOut</a>(genType <span class="keyword">const</span> &amp; a);</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga33777c9dd98f61d9472f96aafdf2bd36">backEaseIn</a>(genType <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga640c1ac6fe9d277a197da69daf60ee4f">backEaseOut</a>(genType <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga68a7b760f2afdfab298d5cd6d7611fb1">backEaseInOut</a>(genType <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga33777c9dd98f61d9472f96aafdf2bd36">backEaseIn</a>(genType <span class="keyword">const</span>&amp; a, genType <span class="keyword">const</span>&amp; o);</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga640c1ac6fe9d277a197da69daf60ee4f">backEaseOut</a>(genType <span class="keyword">const</span>&amp; a, genType <span class="keyword">const</span>&amp; o);</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga68a7b760f2afdfab298d5cd6d7611fb1">backEaseInOut</a>(genType <span class="keyword">const</span>&amp; a, genType <span class="keyword">const</span>&amp; o);</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gaac30767f2e430b0c3fc859a4d59c7b5b">bounceEaseIn</a>(genType <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#ga94007005ff0dcfa0749ebfa2aec540b2">bounceEaseOut</a>(genType <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;        GLM_FUNC_DECL genType <a class="code" href="a00318.html#gadf9f38eff1e5f4c2fa5b629a25ae413e">bounceEaseInOut</a>(genType <span class="keyword">const</span>&amp; a);</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;<span class="preprocessor">#include &quot;easing.inl&quot;</span></div>
<div class="ttc" id="a00318_html_gaac30767f2e430b0c3fc859a4d59c7b5b"><div class="ttname"><a href="a00318.html#gaac30767f2e430b0c3fc859a4d59c7b5b">glm::bounceEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType bounceEaseIn(genType const &amp;a)</div></div>
<div class="ttc" id="a00318_html_ga0c1027637a5b02d4bb3612aa12599d69"><div class="ttname"><a href="a00318.html#ga0c1027637a5b02d4bb3612aa12599d69">glm::circularEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType circularEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise circular function y = (1/2)(1 - sqrt(1 - 4x^2)) ; [0, 0.5) y = (1/2)(sqrt(-(2x - 3)*(2x - 1)) + 1) ; [0.5, 1]. </div></div>
<div class="ttc" id="a00318_html_gaff52f746102b94864d105563ba8895ae"><div class="ttname"><a href="a00318.html#gaff52f746102b94864d105563ba8895ae">glm::cubicEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType cubicEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after the cubic y = x^3. </div></div>
<div class="ttc" id="a00318_html_ga230918eccee4e113d10ec5b8cdc58695"><div class="ttname"><a href="a00318.html#ga230918eccee4e113d10ec5b8cdc58695">glm::elasticEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType elasticEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after the damped sine wave y = sin(13pi/2*x)*pow(2, 10 * (x - 1)) </div></div>
<div class="ttc" id="a00318_html_ga097579d8e087dcf48037588140a21640"><div class="ttname"><a href="a00318.html#ga097579d8e087dcf48037588140a21640">glm::quinticEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType quinticEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after the quintic y = x^5. </div></div>
<div class="ttc" id="a00318_html_gaa46e3d5fbf7a15caa28eff9ef192d7c7"><div class="ttname"><a href="a00318.html#gaa46e3d5fbf7a15caa28eff9ef192d7c7">glm::sineEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType sineEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after half sine wave. </div></div>
<div class="ttc" id="a00318_html_ga26fefde9ced9b72745fe21f1a3fe8da7"><div class="ttname"><a href="a00318.html#ga26fefde9ced9b72745fe21f1a3fe8da7">glm::circularEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType circularEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after shifted quadrant II of unit circle. </div></div>
<div class="ttc" id="a00318_html_gace9c9d1bdf88bf2ab1e7cdefa54c7365"><div class="ttname"><a href="a00318.html#gace9c9d1bdf88bf2ab1e7cdefa54c7365">glm::elasticEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType elasticEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the damped sine wave y = sin(-13pi/2*(x + 1))*pow(2, -10x) + 1. </div></div>
<div class="ttc" id="a00318_html_ga2db4ac8959559b11b4029e54812908d6"><div class="ttname"><a href="a00318.html#ga2db4ac8959559b11b4029e54812908d6">glm::elasticEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType elasticEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise exponentially-damped sine wave: y = (1/2)*sin(13pi/2*(2*x))*pow(2, 10 * ((2*x) - 1)) ; [0,0.5) y = (1/2)*(sin(-13pi/2*((2x-1)+1))*pow(2,-10(2*x-1)) + 2) ; [0.5, 1]. </div></div>
<div class="ttc" id="a00318_html_gafb338ac6f6b2bcafee50e3dca5201dbf"><div class="ttname"><a href="a00318.html#gafb338ac6f6b2bcafee50e3dca5201dbf">glm::sineEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType sineEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after quarter-cycle of sine wave. </div></div>
<div class="ttc" id="a00318_html_ga290c3e47cb0a49f2e8abe90b1872b649"><div class="ttname"><a href="a00318.html#ga290c3e47cb0a49f2e8abe90b1872b649">glm::linearInterpolation</a></div><div class="ttdeci">GLM_FUNC_DECL genType linearInterpolation(genType const &amp;a)</div><div class="ttdoc">Modelled after the line y = x. </div></div>
<div class="ttc" id="a00318_html_ga808b41f14514f47dad5dcc69eb924afd"><div class="ttname"><a href="a00318.html#ga808b41f14514f47dad5dcc69eb924afd">glm::quarticEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType quarticEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after the quartic x^4. </div></div>
<div class="ttc" id="a00318_html_ga4dfb33fa7664aa888eb647999d329b98"><div class="ttname"><a href="a00318.html#ga4dfb33fa7664aa888eb647999d329b98">glm::quarticEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType quarticEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the quartic y = 1 - (x - 1)^4. </div></div>
<div class="ttc" id="a00318_html_gab3e454f883afc1606ef91363881bf5a3"><div class="ttname"><a href="a00318.html#gab3e454f883afc1606ef91363881bf5a3">glm::sineEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType sineEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after quarter-cycle of sine wave (different phase) </div></div>
<div class="ttc" id="a00318_html_ga03e8fc2d7945a4e63ee33b2159c14cea"><div class="ttname"><a href="a00318.html#ga03e8fc2d7945a4e63ee33b2159c14cea">glm::quadraticEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType quadraticEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise quadratic y = (1/2)((2x)^2) ; [0, 0.5) y = -(1/2)((2x-1)*(2x-3) - 1) ; [...</div></div>
<div class="ttc" id="a00318_html_ga34508d4b204a321ec26d6086aa047997"><div class="ttname"><a href="a00318.html#ga34508d4b204a321ec26d6086aa047997">glm::circularEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType circularEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after shifted quadrant IV of unit circle. </div></div>
<div class="ttc" id="a00318_html_ga283717bc2d937547ad34ec0472234ee3"><div class="ttname"><a href="a00318.html#ga283717bc2d937547ad34ec0472234ee3">glm::quadraticEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType quadraticEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the parabola y = -x^2 + 2x. </div></div>
<div class="ttc" id="a00318_html_ga517f2bcfd15bc2c25c466ae50808efc3"><div class="ttname"><a href="a00318.html#ga517f2bcfd15bc2c25c466ae50808efc3">glm::exponentialEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType exponentialEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the exponential function y = -2^(-10x) + 1. </div></div>
<div class="ttc" id="a00318_html_ga7dbd4d5c8da3f5353121f615e7b591d7"><div class="ttname"><a href="a00318.html#ga7dbd4d5c8da3f5353121f615e7b591d7">glm::quinticEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType quinticEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the quintic y = (x - 1)^5 + 1. </div></div>
<div class="ttc" id="a00318_html_ga40d746385d8bcc5973f5bc6a2340ca91"><div class="ttname"><a href="a00318.html#ga40d746385d8bcc5973f5bc6a2340ca91">glm::cubicEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType cubicEaseOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the cubic y = (x - 1)^3 + 1. </div></div>
<div class="ttc" id="a00318_html_ga232fb6dc093c5ce94bee105ff2947501"><div class="ttname"><a href="a00318.html#ga232fb6dc093c5ce94bee105ff2947501">glm::exponentialEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType exponentialEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise exponential y = (1/2)2^(10(2x - 1)) ; [0,0.5) y = -(1/2)*2^(-10(2x - 1))...</div></div>
<div class="ttc" id="a00318_html_ga94007005ff0dcfa0749ebfa2aec540b2"><div class="ttname"><a href="a00318.html#ga94007005ff0dcfa0749ebfa2aec540b2">glm::bounceEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType bounceEaseOut(genType const &amp;a)</div></div>
<div class="ttc" id="a00318_html_ga2a82d5c46df7e2d21cc0108eb7b83934"><div class="ttname"><a href="a00318.html#ga2a82d5c46df7e2d21cc0108eb7b83934">glm::quinticEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType quinticEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise quintic y = (1/2)((2x)^5) ; [0, 0.5) y = (1/2)((2x-2)^5 + 2) ; [0...</div></div>
<div class="ttc" id="a00318_html_ga33777c9dd98f61d9472f96aafdf2bd36"><div class="ttname"><a href="a00318.html#ga33777c9dd98f61d9472f96aafdf2bd36">glm::backEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType backEaseIn(genType const &amp;a, genType const &amp;o)</div></div>
<div class="ttc" id="a00318_html_ga7f24ee9219ab4c84dc8de24be84c1e3c"><div class="ttname"><a href="a00318.html#ga7f24ee9219ab4c84dc8de24be84c1e3c">glm::exponentialEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType exponentialEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after the exponential function y = 2^(10(x - 1)) </div></div>
<div class="ttc" id="a00318_html_gaf42089d35855695132d217cd902304a0"><div class="ttname"><a href="a00318.html#gaf42089d35855695132d217cd902304a0">glm::quadraticEaseIn</a></div><div class="ttdeci">GLM_FUNC_DECL genType quadraticEaseIn(genType const &amp;a)</div><div class="ttdoc">Modelled after the parabola y = x^2. </div></div>
<div class="ttc" id="a00318_html_ga6d000f852de12b197e154f234b20c505"><div class="ttname"><a href="a00318.html#ga6d000f852de12b197e154f234b20c505">glm::quarticEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType quarticEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise quartic y = (1/2)((2x)^4) ; [0, 0.5) y = -(1/2)((2x-2)^4 - 2) ; [0...</div></div>
<div class="ttc" id="a00318_html_ga55134072b42d75452189321d4a2ad91c"><div class="ttname"><a href="a00318.html#ga55134072b42d75452189321d4a2ad91c">glm::cubicEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType cubicEaseInOut(genType const &amp;a)</div><div class="ttdoc">Modelled after the piecewise cubic y = (1/2)((2x)^3) ; [0, 0.5) y = (1/2)((2x-2)^3 + 2) ; [0...</div></div>
<div class="ttc" id="a00318_html_gadf9f38eff1e5f4c2fa5b629a25ae413e"><div class="ttname"><a href="a00318.html#gadf9f38eff1e5f4c2fa5b629a25ae413e">glm::bounceEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType bounceEaseInOut(genType const &amp;a)</div></div>
<div class="ttc" id="a00318_html_ga68a7b760f2afdfab298d5cd6d7611fb1"><div class="ttname"><a href="a00318.html#ga68a7b760f2afdfab298d5cd6d7611fb1">glm::backEaseInOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType backEaseInOut(genType const &amp;a, genType const &amp;o)</div></div>
<div class="ttc" id="a00318_html_ga640c1ac6fe9d277a197da69daf60ee4f"><div class="ttname"><a href="a00318.html#ga640c1ac6fe9d277a197da69daf60ee4f">glm::backEaseOut</a></div><div class="ttdeci">GLM_FUNC_DECL genType backEaseOut(genType const &amp;a, genType const &amp;o)</div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
