<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: fast_trigonometry.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">fast_trigonometry.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00033.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../gtc/constants.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_fast_trigonometry is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_fast_trigonometry extension included&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#ga069527c6dbd64f53435b8ebc4878b473">wrapAngle</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#ga0aab3257bb3b628d10a1e0483e2c6915">fastSin</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#gab34c8b45c23c0165a64dcecfcc3b302a">fastCos</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#gaf29b9c1101a10007b4f79ee89df27ba2">fastTan</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#ga562cb62c51fbfe7fac7db0bce706b81f">fastAsin</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#ga9721d63356e5d94fdc4b393a426ab26b">fastAcos</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#gae25de86a968490ff56856fa425ec9d30">fastAtan</a>(T y, T x);</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        GLM_FUNC_DECL T <a class="code" href="a00325.html#gae25de86a968490ff56856fa425ec9d30">fastAtan</a>(T <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#include &quot;fast_trigonometry.inl&quot;</span></div>
<div class="ttc" id="a00325_html_ga562cb62c51fbfe7fac7db0bce706b81f"><div class="ttname"><a href="a00325.html#ga562cb62c51fbfe7fac7db0bce706b81f">glm::fastAsin</a></div><div class="ttdeci">GLM_FUNC_DECL T fastAsin(T angle)</div><div class="ttdoc">Faster than the common asin function but less accurate. </div></div>
<div class="ttc" id="a00257_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle. </div></div>
<div class="ttc" id="a00325_html_ga9721d63356e5d94fdc4b393a426ab26b"><div class="ttname"><a href="a00325.html#ga9721d63356e5d94fdc4b393a426ab26b">glm::fastAcos</a></div><div class="ttdeci">GLM_FUNC_DECL T fastAcos(T angle)</div><div class="ttdoc">Faster than the common acos function but less accurate. </div></div>
<div class="ttc" id="a00325_html_gaf29b9c1101a10007b4f79ee89df27ba2"><div class="ttname"><a href="a00325.html#gaf29b9c1101a10007b4f79ee89df27ba2">glm::fastTan</a></div><div class="ttdeci">GLM_FUNC_DECL T fastTan(T angle)</div><div class="ttdoc">Faster than the common tan function but less accurate. </div></div>
<div class="ttc" id="a00325_html_gab34c8b45c23c0165a64dcecfcc3b302a"><div class="ttname"><a href="a00325.html#gab34c8b45c23c0165a64dcecfcc3b302a">glm::fastCos</a></div><div class="ttdeci">GLM_FUNC_DECL T fastCos(T angle)</div><div class="ttdoc">Faster than the common cos function but less accurate. </div></div>
<div class="ttc" id="a00325_html_gae25de86a968490ff56856fa425ec9d30"><div class="ttname"><a href="a00325.html#gae25de86a968490ff56856fa425ec9d30">glm::fastAtan</a></div><div class="ttdeci">GLM_FUNC_DECL T fastAtan(T angle)</div><div class="ttdoc">Faster than the common atan function but less accurate. </div></div>
<div class="ttc" id="a00325_html_ga0aab3257bb3b628d10a1e0483e2c6915"><div class="ttname"><a href="a00325.html#ga0aab3257bb3b628d10a1e0483e2c6915">glm::fastSin</a></div><div class="ttdeci">GLM_FUNC_DECL T fastSin(T angle)</div><div class="ttdoc">Faster than the common sin function but less accurate. </div></div>
<div class="ttc" id="a00325_html_ga069527c6dbd64f53435b8ebc4878b473"><div class="ttname"><a href="a00325.html#ga069527c6dbd64f53435b8ebc4878b473">glm::wrapAngle</a></div><div class="ttdeci">GLM_FUNC_DECL T wrapAngle(T angle)</div><div class="ttdoc">Wrap an angle to [0 2pi[ From GLM_GTX_fast_trigonometry extension. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
