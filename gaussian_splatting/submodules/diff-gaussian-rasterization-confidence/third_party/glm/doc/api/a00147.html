<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: scalar_integer.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">scalar_integer.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00261.html">GLM_EXT_scalar_integer</a>  
<a href="#details">More...</a></p>

<p><a href="a00147_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#ga2777901e41ad6e1e9d0ad6cc855d1075">findNSB</a> (genIUType x, int significantBitCount)</td></tr>
<tr class="memdesc:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the Nth significant bit set to 1 in the binary representation of value.  <a href="a00261.html#ga2777901e41ad6e1e9d0ad6cc855d1075">More...</a><br /></td></tr>
<tr class="separator:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec593d33956a8fe43f78fccc63ddde9a"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gaec593d33956a8fe43f78fccc63ddde9a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gaec593d33956a8fe43f78fccc63ddde9a">isMultiple</a> (genIUType v, genIUType Multiple)</td></tr>
<tr class="memdesc:gaec593d33956a8fe43f78fccc63ddde9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the 'Value' is a multiple of 'Multiple'.  <a href="a00261.html#gaec593d33956a8fe43f78fccc63ddde9a">More...</a><br /></td></tr>
<tr class="separator:gaec593d33956a8fe43f78fccc63ddde9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf491730354aa7da67fbe23d4d688763"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gadf491730354aa7da67fbe23d4d688763"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gadf491730354aa7da67fbe23d4d688763">isPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:gadf491730354aa7da67fbe23d4d688763"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the value is a power of two number.  <a href="a00261.html#gadf491730354aa7da67fbe23d4d688763">More...</a><br /></td></tr>
<tr class="separator:gadf491730354aa7da67fbe23d4d688763"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab770a3835c44c8a6fd225be4f4e6b317"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gab770a3835c44c8a6fd225be4f4e6b317"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gab770a3835c44c8a6fd225be4f4e6b317">nextMultiple</a> (genIUType v, genIUType Multiple)</td></tr>
<tr class="memdesc:gab770a3835c44c8a6fd225be4f4e6b317"><td class="mdescLeft">&#160;</td><td class="mdescRight">Higher multiple number of Source.  <a href="a00261.html#gab770a3835c44c8a6fd225be4f4e6b317">More...</a><br /></td></tr>
<tr class="separator:gab770a3835c44c8a6fd225be4f4e6b317"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#ga3a37c2f2fd347886c9af6a3ca3db04dc">nextPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value, round up to a power of two.  <a href="a00261.html#ga3a37c2f2fd347886c9af6a3ca3db04dc">More...</a><br /></td></tr>
<tr class="separator:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada3bdd871ffe31f2d484aa668362f636"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gada3bdd871ffe31f2d484aa668362f636"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gada3bdd871ffe31f2d484aa668362f636">prevMultiple</a> (genIUType v, genIUType Multiple)</td></tr>
<tr class="memdesc:gada3bdd871ffe31f2d484aa668362f636"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00261.html#gada3bdd871ffe31f2d484aa668362f636">More...</a><br /></td></tr>
<tr class="separator:gada3bdd871ffe31f2d484aa668362f636"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab21902a0e7e5a8451a7ad80333618727"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gab21902a0e7e5a8451a7ad80333618727"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gab21902a0e7e5a8451a7ad80333618727">prevPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:gab21902a0e7e5a8451a7ad80333618727"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value, round down to a power of two.  <a href="a00261.html#gab21902a0e7e5a8451a7ad80333618727">More...</a><br /></td></tr>
<tr class="separator:gab21902a0e7e5a8451a7ad80333618727"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00261.html">GLM_EXT_scalar_integer</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00147_source.html">scalar_integer.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
