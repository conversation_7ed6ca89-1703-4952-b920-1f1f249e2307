<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: bit.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">bit.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00309.html">GLM_GTX_bit</a>  
<a href="#details">More...</a></p>

<p><a href="a00008_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga0dcc8fe7c3d3ad60dea409281efa3d05"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga0dcc8fe7c3d3ad60dea409281efa3d05"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga0dcc8fe7c3d3ad60dea409281efa3d05">highestBitValue</a> (genIUType Value)</td></tr>
<tr class="separator:ga0dcc8fe7c3d3ad60dea409281efa3d05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga898ef075ccf809a1e480faab48fe96bf"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga898ef075ccf809a1e480faab48fe96bf"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga898ef075ccf809a1e480faab48fe96bf">highestBitValue</a> (vec&lt; L, T, Q &gt; const &amp;value)</td></tr>
<tr class="memdesc:ga898ef075ccf809a1e480faab48fe96bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find the highest bit set to 1 in a integer variable and return its value.  <a href="a00309.html#ga898ef075ccf809a1e480faab48fe96bf">More...</a><br /></td></tr>
<tr class="separator:ga898ef075ccf809a1e480faab48fe96bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ff6568089f3a9b67f5c30918855fc6f"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga2ff6568089f3a9b67f5c30918855fc6f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga2ff6568089f3a9b67f5c30918855fc6f">lowestBitValue</a> (genIUType Value)</td></tr>
<tr class="separator:ga2ff6568089f3a9b67f5c30918855fc6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8cda2459871f574a0aecbe702ac93291"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga8cda2459871f574a0aecbe702ac93291"><td class="memTemplItemLeft" align="right" valign="top">GLM_DEPRECATED GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga8cda2459871f574a0aecbe702ac93291">powerOfTwoAbove</a> (genIUType Value)</td></tr>
<tr class="memdesc:ga8cda2459871f574a0aecbe702ac93291"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value.  <a href="a00309.html#ga8cda2459871f574a0aecbe702ac93291">More...</a><br /></td></tr>
<tr class="separator:ga8cda2459871f574a0aecbe702ac93291"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2bbded187c5febfefc1e524ba31b3fab"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2bbded187c5febfefc1e524ba31b3fab"><td class="memTemplItemLeft" align="right" valign="top">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga2bbded187c5febfefc1e524ba31b3fab">powerOfTwoAbove</a> (vec&lt; L, T, Q &gt; const &amp;value)</td></tr>
<tr class="memdesc:ga2bbded187c5febfefc1e524ba31b3fab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value.  <a href="a00309.html#ga2bbded187c5febfefc1e524ba31b3fab">More...</a><br /></td></tr>
<tr class="separator:ga2bbded187c5febfefc1e524ba31b3fab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3de7df63c589325101a2817a56f8e29d"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga3de7df63c589325101a2817a56f8e29d"><td class="memTemplItemLeft" align="right" valign="top">GLM_DEPRECATED GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga3de7df63c589325101a2817a56f8e29d">powerOfTwoBelow</a> (genIUType Value)</td></tr>
<tr class="memdesc:ga3de7df63c589325101a2817a56f8e29d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value.  <a href="a00309.html#ga3de7df63c589325101a2817a56f8e29d">More...</a><br /></td></tr>
<tr class="separator:ga3de7df63c589325101a2817a56f8e29d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf78ddcc4152c051b2a21e68fecb10980"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf78ddcc4152c051b2a21e68fecb10980"><td class="memTemplItemLeft" align="right" valign="top">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#gaf78ddcc4152c051b2a21e68fecb10980">powerOfTwoBelow</a> (vec&lt; L, T, Q &gt; const &amp;value)</td></tr>
<tr class="memdesc:gaf78ddcc4152c051b2a21e68fecb10980"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value.  <a href="a00309.html#gaf78ddcc4152c051b2a21e68fecb10980">More...</a><br /></td></tr>
<tr class="separator:gaf78ddcc4152c051b2a21e68fecb10980"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f65973a5d2ea38c719e6a663149ead9"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga5f65973a5d2ea38c719e6a663149ead9"><td class="memTemplItemLeft" align="right" valign="top">GLM_DEPRECATED GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#ga5f65973a5d2ea38c719e6a663149ead9">powerOfTwoNearest</a> (genIUType Value)</td></tr>
<tr class="memdesc:ga5f65973a5d2ea38c719e6a663149ead9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is the closet to the input value.  <a href="a00309.html#ga5f65973a5d2ea38c719e6a663149ead9">More...</a><br /></td></tr>
<tr class="separator:ga5f65973a5d2ea38c719e6a663149ead9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac87e65d11e16c3d6b91c3bcfaef7da0b"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac87e65d11e16c3d6b91c3bcfaef7da0b"><td class="memTemplItemLeft" align="right" valign="top">GLM_DEPRECATED GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00309.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">powerOfTwoNearest</a> (vec&lt; L, T, Q &gt; const &amp;value)</td></tr>
<tr class="memdesc:gac87e65d11e16c3d6b91c3bcfaef7da0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is the closet to the input value.  <a href="a00309.html#gac87e65d11e16c3d6b91c3bcfaef7da0b">More...</a><br /></td></tr>
<tr class="separator:gac87e65d11e16c3d6b91c3bcfaef7da0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00309.html">GLM_GTX_bit</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00008_source.html">bit.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
