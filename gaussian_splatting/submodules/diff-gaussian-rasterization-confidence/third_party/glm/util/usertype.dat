attribute 
const 
uniform
varying
break
continue
do
for
while
if
else
in
out
inout
float
int
void
bool
true
false
discard
return
mat2
mat3
mat4
mat2x2
mat3x3
mat4x4
mat2x3
mat3x2
mat2x4
mat4x2
mat3x4
mat4x3
vec2
vec3
vec4
ivec2
ivec3
ivec4
uvec2
uvec3
uvec4
bvec2
bvec3
bvec4
sampler1D
sampler2D
sampler3D
samplerCube
sampler1DShadow
sampler2DShadow
struct

asm
class
union
enum
typedef
template
this
packed
goto
switch
default
inline
noinline
volatile
public
static
extern
external
interface
long
short
double
half
fixed
unsigned
input
output
sampler2DRect
sampler3DRect
sampler2DRectShadow
sizeof
cast
namespace
using

layout 
location
smooth
flat
noperspective
centroid
invariant
lowp
mediump
highp
precision
patch
sample
subroutine

hvec2
hvec3
hvec4
fvec2
fvec3
fvec4
dvec2
dvec3
dvec4

on

final
abstract
limited
access
self

uchar
schar
uint
sint

int8
int16
int32
int64

sint8
sint16
sint32
sint64

uint8
uint16
uint32
uint64

float16
float32
float64

quat
hquat
fquat
dquat

handle
handle8
handle16
handle32
handle64

flag
flag8
flag16
flag32
flag64

import
export

hmat2
hmat3
hmat4

fmat2
fmat3
fmat4

dmat2
dmat3
dmat4

hmat2x3
hmat3x2
hmat2x4
hmat4x2
hmat3x4
hmat4x3

fmat2x3
fmat3x2
fmat2x4
fmat4x2
fmat3x4
fmat4x3

dmat2x3
dmat3x2
dmat2x4
dmat4x2
dmat3x4
dmat4x3

null
pi
epsilon
infinite
self

byte
word
dword
qword

new_object
new_array
delete_object
delete_array

int8
int16
int32
int64

i8
i16
i32
i64

i8vec2
i8vec3
i8vec4

i16vec2
i16vec3
i16vec4

i32vec2
i32vec3
i32vec4

i64vec2
i64vec3
i64vec4

uint8
uint16
uint32
uint64

u8
u16
u32
u64

u8vec2
u8vec3
u8vec4

u16vec2
u16vec3
u16vec4

u32vec2
u32vec3
u32vec4

u64vec2
u64vec3
u64vec4

float16
float32
float64

f16
f32
f64

f16vec2
f16vec3
f16vec4

f32vec2
f32vec3
f32vec4

f64vec2
f64vec3
f64vec4

f16mat2
f16mat3
f16mat4

f16mat2x3
f16mat2x4
f16mat3x2
f16mat3x4
f16mat4x2
f16mat4x3

f32mat2
f32mat3
f32mat4

f32mat2x3
f32mat2x4
f32mat3x2
f32mat3x4
f32mat4x2
f32mat4x3

f64mat2
f64mat3
f64mat4

f64mat2x3
f64mat2x4
f64mat3x2
f64mat3x4
f64mat4x2
f64mat4x3

f16quat
f32quat
f64quat

bool1
bool2
bool3
bool4

bool1x1
bool2x2
bool3x3
bool4x4

bool2x3
bool2x4
bool3x2
bool3x4
bool4x2
bool4x3

int1
int2
int3
int4

int1x1
int2x2
int3x3
int4x4

int2x3
int2x4
int3x2
int3x4
int4x2
int4x3

half1
half2
half3
half4

half2x2
half3x3
half4x4

half2x3
half2x4
half3x2
half3x4
half4x2
half4x3

float1
float2
float3
float4

float1x1
float2x2
float3x3
float4x4

float2x3
float2x4
float3x2
float3x4
float4x2
float4x3

double1
double2
double3
double4

double1x1
double2x2
double3x3
double4x4

double2x3
double2x4
double3x2
double3x4
double4x2
double4x3
